package com.fxiaoke.open.oasyncdata.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * crm对象
 */
@Getter
@ToString
@AllArgsConstructor
public enum ObjectApiEnum {
    FS_APPROVAL_TASK_OBJ("审批流程待办",
            I18NStringEnum.s1240.getI18nKey(),
            "ApprovalTaskObj",
            "452",
            "approvalServiceImpl",
            "待办",
            I18NStringEnum.s1227.getI18nKey(),
            "crmToDo"),
    BPM_TASK_OBJ("业务流程待办",
            I18NStringEnum.s1241.getI18nKey(),
            "BpmTask",
            "457",
            "bpmTaskServiceImpl",
            "待办",
            I18NStringEnum.s1227.getI18nKey(),
            "crmToDo"),
    CRM_NOTIFY("CRM提醒",
            I18NStringEnum.s1242.getI18nKey(),
            "crmNotify",
            "",
            "commonTodoObjServiceImpl",
            "提醒",
            I18NStringEnum.s1239.getI18nKey(),
            "crmNotify"),
    TO_DISTRIBUTE_LEADSOBJ("销售线索",
            I18NStringEnum.s1219.getI18nKey(),
            "LeadsObj",
            "401,402",
            "commonTodoObjServiceImpl",
            "待办",
            I18NStringEnum.s1227.getI18nKey(),
            "crmToDo"),
    //    TO_FOLLOW_LEADSOBJ("crmToDo","402","402-LeadsObj", "待跟进的销售线索"),
    TO_SEND_SALES_ORDER_OBJ("销售订单",
            I18NStringEnum.s1121.getI18nKey(),
            "SalesOrderObj",
            "456",
            "commonTodoObjServiceImpl",
            "待办",
            I18NStringEnum.s1227.getI18nKey(),
            "crmToDo"),
    TO_DEAL_STAGE_TASK_OBJ("阶段任务",
            I18NStringEnum.s1220.getI18nKey(),
            "StageTaskObj",
            "460",
            "commonTodoObjServiceImpl",
            "待办",
            I18NStringEnum.s1227.getI18nKey(),
            "crmToDo"),
    TO_EVALUATE_JOURNAL_OBJ("日志",
            I18NStringEnum.s1221.getI18nKey(),
            "JournalObj",
            "11",
            "commonTodoObjServiceImpl",
            "待办",
            I18NStringEnum.s1227.getI18nKey(),
            "crmToDo"),
    TO_WAIT_APPROVAL_CASE_OBJ("工单",
            I18NStringEnum.s1243.getI18nKey(),
            "CasesObj",
            "457WaitAssign,457WaitApproval,457WaitCheckins,457WaitDeal,457WaitToOrder",
            "commonTodoObjServiceImpl",
            "待办",
            I18NStringEnum.s1227.getI18nKey(),
            "crmToDo"),
//    TO_WAIT_ASSIGN_CASE_OBJ("crmToDo","457WaitAssign","457-CasesObj", "待指派的工单"),
//    TO_WAIT_CHECKINS_CASE_OBJ("crmToDo","457WaitCheckins","457-CasesObj", "待执行外勤的工单"),
//    TO_WAIT_DEAL_CASE_OBJ("crmToDo","457WaitDeal","457WaitDeal", "待处理的工单"),
//    TO_WAIT_ORDER_CASE_OBJ("crmToDo","457WaitToOrder","CasesObj", "待接单的工单"),
    ;
    /**
     * 对象名称
     */
    private final String objName;
    private final String objNameI18nKey;
    /**
     * 对象ApiName
     */
    private final String objApiName;

    /**
     * 对象bizType
     */
    private final String bizType;

    /**
     * 处理bean名
     * @param bizType
     * @return
     */
   private final String serviceName;
    /**
     * businessType 业务类型
     */
    private final String businessTypeName;
    private final String businessTypeNameI18nKey;

    private final String businessType;

    public String getObjName(I18NStringManager i18NStringManager, String lang, String tenantId) {
        return i18NStringManager.get(objNameI18nKey,lang,tenantId,objApiName);
    }

    public String getBusinessTypeName(I18NStringManager i18NStringManager, String lang, String tenantId) {
        return i18NStringManager.get(businessTypeNameI18nKey,lang,tenantId,businessTypeName);
    }

    public static ObjectApiEnum getObjApiEnumByBizType(String bizType) {
        Optional<ObjectApiEnum> optional = Arrays.stream(ObjectApiEnum.values())
                .filter(p -> p.getBizType().contains(bizType))//注意工单457跟业务457,401,402
                .findFirst();
        if (optional.isPresent()) {
            ObjectApiEnum statusEnum = optional.get();
            return statusEnum;
        } else {
            return null;
        }
    }
    public static List<ObjectApiEnum> getBusinessEnumByType(String type){
        return Arrays.stream(ObjectApiEnum.values()).filter(item ->item.businessType.equals(type)).collect(Collectors.toList());

    }
    public static List<String> getStringBusinessEnumByType(String type){
        return Arrays.stream(ObjectApiEnum.values()).filter(item ->item.businessType.equals(type)).collect(Collectors.toList()).stream().map(ObjectApiEnum::getObjApiName).collect(Collectors.toList());

    }
    public static ObjectApiEnum getObjApiEnumByApiName(String apiName) {
        Optional<ObjectApiEnum> optional = Arrays.stream(ObjectApiEnum.values())
                .filter(p -> p.getObjApiName().equals(apiName))
                .findFirst();
        if (optional.isPresent()) {
            ObjectApiEnum statusEnum = optional.get();
            return statusEnum;
        } else {
            return null;
        }
    }
    public static boolean assistTodoType(String apiName) {
        if(apiName.equals(ObjectApiEnum.CRM_NOTIFY.getObjApiName())){
            return false;
        }else {
            return true;
        }
    }
    public static List<String> convertObjByCrmType(String apiName) {
        if(CrmTypeMessageEnum.CRM_NOTIFY.getType().equals(apiName)){
            return Lists.newArrayList(CrmTypeMessageEnum.CRM_NOTIFY.getType());
        }else if (CrmTypeMessageEnum.CRM_TODO_TYPE.getType().equals(apiName)){
            return ObjectApiEnum.getStringBusinessEnumByType(CrmTypeMessageEnum.CRM_TODO_TYPE.getType());
        }else {
            return null;
        }
    }
}
