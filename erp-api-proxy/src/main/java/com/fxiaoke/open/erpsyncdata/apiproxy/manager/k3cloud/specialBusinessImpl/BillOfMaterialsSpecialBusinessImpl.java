package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl;

import cn.hutool.core.collection.CollUtil;
import com.fxiaoke.crmrestapi.arg.GetConfigValueByKeyArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.result.GetConfigValueByKeyResult;
import com.fxiaoke.crmrestapi.service.SkuSpuService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.BomManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.SpecialBusiness;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.IdSaveExtend;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.SaveArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.ViewArg;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataMappingsManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailSnapshotManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BomUtils;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.CrmConfigKeyConstants;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjSplitTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.IsOpenCpqEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.SendEventMqRecord;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpDataPreprocessService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ProbeErpDataService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * k3的bom物料清单特殊逻辑。
 *
 * <AUTHOR>
 * @Date: 09:20 2021/1/6
 * @Desc:
 */
@Slf4j
@Component("ENG_BOM")
public class BillOfMaterialsSpecialBusinessImpl implements SpecialBusiness {
    @Autowired
    private ErpDataPreprocessService erpDataPreprocessService;
    @Autowired
    private ProbeErpDataService probeErpDataService;
    @Autowired
    private SkuSpuService skuSpuService;
    @Autowired
    private SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private SyncDataMappingsManager syncDataMappingsManager;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private BomManager bomManager;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private TenantConfigurationManager configurationManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    @Override
    public Result<String> beforeRunCreateErpObjData(StandardData standardData,
                                                    K3CloudApiClient apiClient,
                                                    SaveArg saveArg,
                                                    IdSaveExtend saveExtend) {
        String tenantId = standardData.getMasterFieldVal().getTenantId();
        if (configurationManager.inWhiteList(tenantId, TenantConfigurationTypeEnum.BOM_V1_TENANTS)) {
            beforeCreateV1(standardData, saveArg, tenantId);
        }

        return new Result<>();
    }

    private void beforeCreateV1(StandardData standardData, SaveArg saveArg, String tenantId) {
        SyncDataMappingsEntity destMapping = syncDataMappingsDao.setTenantId(tenantId)
                .getByDestData(tenantId,
                        ObjectApiNameEnum.FS_BOM_INSTANCE_OBJ.getObjApiName(),
                        "ENG_BOM.BillHead",
                        standardData.getMasterFieldVal().getId());
        if(destMapping!=null) {
            standardData.getMasterFieldVal().put("master_data_id",destMapping.getSourceDataId());
            String treeId = bomManager.getBomInstanceTreeId(tenantId,destMapping.getSourceDataId());
            log.info("BillOfMaterialsSpecialBusinessImpl.beforeRunCreateErpObjData,treeId={}",treeId);
            if(StringUtils.isNotEmpty(treeId)) {
                //crm->erp方向，物料清单同步，默认填充ERP侧明细对象的数据
                updateBomDetailData(tenantId,treeId, standardData, saveArg,treeId);
            }
        }
    }

    @Override
    public void afterRunCreateErpObjData(ErpIdResult erpIdResult, StandardData standardData, SaveArg saveArg,K3CloudApiClient apiClient) {
        String tenantId = apiClient.getTenantId();
        if (configurationManager.inWhiteList(tenantId, TenantConfigurationTypeEnum.BOM_V1_TENANTS)) {
            afterCreateV1(erpIdResult, standardData, apiClient, tenantId);
        }
    }

    private void afterCreateV1(ErpIdResult erpIdResult, StandardData standardData, K3CloudApiClient apiClient, String tenantId) {
        QueryArg queryArg = new QueryArg();
        queryArg.setFieldKeys("FID,FNumber,FMATERIALID.FNumber,FTreeEntity_FEntryId,FMATERIALIDCHILD.FNumber");
        queryArg.setFormId(standardData.getObjAPIName());
        queryArg.appendEqualFilter("FID", erpIdResult.getMasterDataId());
        Result<List<List<Object>>> listResult = apiClient.executeBillQuery(queryArg);
        if(!listResult.isSuccess() || CollectionUtils.isEmpty(listResult.getData())) return;

        String bom_id = erpIdResult.getMasterDataId();
        String bom_version = listResult.getData().get(0).get(1).toString();
        String parentMaterialNumber = listResult.getData().get(0).get(2).toString();

        String dstDataId = BomUtils.destDataFormat.replace("bom_id",bom_id)
                .replace("bom_version",bom_version)
                .replace("bom_entry_id",bom_id)
                .replace("material_number",parentMaterialNumber);
        erpIdResult.setMasterDataId(dstDataId);

        String masterDataId = standardData.getMasterFieldVal().getString("master_data_id");

        List<ObjectData> bomInstanceDataList = (List<ObjectData>) standardData.getMasterFieldVal().get("detailDataList");
        //动态生成物料清单明细的中间表数据
        for(ObjectData objectData : bomInstanceDataList) {
            SyncDataMappingsEntity mappingsEntity = new SyncDataMappingsEntity();
            mappingsEntity.setId(UUID.randomUUID().toString());
            mappingsEntity.setTenantId(tenantId);
            mappingsEntity.setCreateTime(System.currentTimeMillis());
            mappingsEntity.setUpdateTime(System.currentTimeMillis());

            mappingsEntity.setSourceTenantId(tenantId);
            mappingsEntity.setSourceObjectApiName(ObjectApiNameEnum.FS_BOM_INSTANCE_OBJ.getObjApiName());
            //mappingsEntity.setSourceDataId(objectData.getId());
            mappingsEntity.setSourceDataName(objectData.getName());
            mappingsEntity.setDestTenantId(tenantId);
            mappingsEntity.setDestObjectApiName("ENG_BOM.BillHead");
            //mappingsEntity.setDestDataId(null);
            mappingsEntity.setDestDataName(null);

            String product_id = objectData.getString("product_id");
            String srcDataId = BomUtils.getSrcDataId(objectData);
            mappingsEntity.setSourceDataId(srcDataId);

            SyncDataMappingsEntity destData = syncDataMappingsDao.setTenantId(tenantId).getByDestData(tenantId,
                    "BD_MATERIAL.BillHead",
                    ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName(),
                    product_id);
            if(destData==null) {
                log.info("BillOfMaterialsSpecialBusinessImpl.afterRunCreateErpObjData,destData==null,product_id={}",product_id);
                continue;
            }
            String materialNumber = destData.getSourceDataId();
            String destDataId = getBomDetailDestDataId(listResult.getData(),materialNumber);
            if(StringUtils.isEmpty(destDataId)) {
                log.info("BillOfMaterialsSpecialBusinessImpl.afterRunCreateErpObjData,destDataId==null,materialNumber={}",materialNumber);
                continue;
            }
            mappingsEntity.setDestDataId(destDataId);

            mappingsEntity.setMasterDataId(masterDataId);

            mappingsEntity.setLastSyncStatus(SyncDataStatusEnum.WRITE_SUCCESS.getStatus());
            mappingsEntity.setRemark(i18NStringManager.getByEi(I18NStringEnum.s3751, tenantId));
            mappingsEntity.setIsCreated(true);
            mappingsEntity.setIsDeleted(false);

            int count = syncDataMappingsDao.setTenantId(tenantId).insert(mappingsEntity);
            log.info("BillOfMaterialsSpecialBusinessImpl.afterRunCreateErpObjData,count={}",count);
        }
    }

    private String getBomDetailDestDataId(List<List<Object>> dataList,String materialNumber) {
        for(List<Object> item : dataList) {
            String bom_entry_id = item.get(3).toString();
            String child_material_number = item.get(4).toString();
            if(!StringUtils.equalsIgnoreCase(child_material_number,materialNumber)) continue;

            String bom_id = item.get(0).toString();
            String bom_version = item.get(1).toString();

            String childDstDataId = BomUtils.destDataFormat.replace("bom_id",bom_id)
                    .replace("bom_version",bom_version)
                    .replace("bom_entry_id",bom_entry_id)
                    .replace("material_number",child_material_number);
            return childDstDataId;
        }
        return null;
    }

    /**
     * 获取CRM字段值
     * @param orderProductList
     * @param bomId
     * @param crmFieldApiName
     * @return
     */
    private String getCrmFieldValue(List<com.fxiaoke.crmrestapi.common.data.ObjectData> orderProductList,String bomId,String crmFieldApiName) {
        String fieldValue = null;
        for(com.fxiaoke.crmrestapi.common.data.ObjectData objectData : orderProductList) {
            String bom_id = objectData.getString("bom_id");
            if(StringUtils.equalsIgnoreCase(bom_id,bomId)) {
                fieldValue = objectData.getString(crmFieldApiName);
            }
        }
        log.info("BillOfMaterialsSpecialBusinessImpl.getFieldValue,bomId={},crmFieldApiName={},fieldValue={}",bomId,crmFieldApiName,fieldValue);
        return fieldValue;
    }

    /**
     * 默认填充ENG_BOM子对象数据
     * @param tenantId
     * @param treeId
     * @param saveArg
     */
    private void updateBomDetailData(String tenantId, String treeId,StandardData standardData,SaveArg saveArg,String bomInstanceId) {
        List<com.fxiaoke.crmrestapi.common.data.ObjectData> dataList = bomManager.getBomInstanceList(tenantId,treeId);
        List<ObjectData> details = new ArrayList<>();

        Map<String, String> extraFieldMappings = configCenterConfig.getBOM_INSTANCE_DETAIL_EXTRA_FIELD_MAPPINGS(tenantId);
        log.info("BillOfMaterialsSpecialBusinessImpl.updateBomDetailData,extraFieldMappings={}",extraFieldMappings);
        List<com.fxiaoke.crmrestapi.common.data.ObjectData> orderProductList = null;
        if (extraFieldMappings.isEmpty() == false) {
            orderProductList = bomManager.getOrderProductList(tenantId,bomInstanceId);
        }

        if(CollectionUtils.isNotEmpty(dataList)) {
            List<ObjectData> detailDataList = new ArrayList<>();
            for(com.fxiaoke.crmrestapi.common.data.ObjectData objectData : dataList) {
                String root_id = objectData.getString("root_id");
                String bom_id = objectData.getString("bom_id");
                String product_id = objectData.getString("product_id");
                if(StringUtils.equalsIgnoreCase(root_id,bom_id)) continue;
                ObjectData detailData = new ObjectData();
                Pair<SyncDataMappingsEntity, SyncDataMappingsEntity> mapping2Way = syncDataMappingsManager.getMapping2Way(tenantId,
                        ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName(),
                        product_id,
                        "BD_MATERIAL.BillHead");
                String materialNumber = null;
                if(mapping2Way.getRight()!=null) {
                    materialNumber = mapping2Way.getRight().getSourceDataId();
                } else {
                    if(mapping2Way.getLeft()!=null) {
                        materialNumber = mapping2Way.getLeft().getDestDataId();
                    }
                }
                Map<String,String> materialMap = new HashMap<>();
                materialMap.put("FNumber",materialNumber);
                detailData.put("FMATERIALIDCHILD",materialMap);//子物料编码字段，必须放在明细数据的第一行，金蝶要求

                detailData.put("FMATERIALTYPE","1");
                detailData.put("FDOSAGETYPE","2");
                //用量：分子
                detailData.put("FNUMERATOR",objectData.getString("quantity"));
                //用量：分母
                detailData.put("FDENOMINATOR","1");

                if(extraFieldMappings.isEmpty()==false) {
                    //如果物料清单明细附加字段值配置不为空，刚填充对应字段的值
                    for(String crmFieldApiName : extraFieldMappings.keySet()) {
                        String erpFieldApiName = extraFieldMappings.get(crmFieldApiName);
                        String crmFieldValue = null;
                        //如果CRM字段apiName以BomInstanceObj:开头，刚从产品选配实例对象取数据
                        if(crmFieldApiName.startsWith("BomInstanceObj:")) {
                            String key = crmFieldApiName.replace("BomInstanceObj:","");
                            crmFieldValue = objectData.getString(key);
                        } else {
                            crmFieldValue = getCrmFieldValue(orderProductList, bom_id, crmFieldApiName);
                        }
                        detailData.put(erpFieldApiName,crmFieldValue);
                        log.info("BillOfMaterialsSpecialBusinessImpl.updateBomDetailData,detailData={}",detailData);
                    }
                }

                details.add(detailData);

                ObjectData temp = new ObjectData();
                temp.putAll(objectData);
                detailDataList.add(temp);
            }
            standardData.getMasterFieldVal().put("detailDataList",detailDataList);
        }
        saveArg.getModel().put("FTreeEntity",details);
    }

    /**
     * 查看对象数据的前置动作。
     */
    @Override
    public void afterRunListData(TimeFilterArg timeFilterArg, List<StandardData> standardDataList,
                                 K3CloudApiClient k3CloudApiClient) {
        String tenantId = timeFilterArg.getTenantId();
        String snapId = timeFilterArg.getSnapshotId();
        String realObjApiName = timeFilterArg.getObjAPIName();
        if (configurationManager.inWhiteList(tenantId, TenantConfigurationTypeEnum.BOM_V1_TENANTS)) {
            //v1版本逻辑
            processDataListV1(standardDataList, tenantId, snapId, realObjApiName);
        }else {
            handleListV2(standardDataList);
        }
    }

    @Override
    public void beforeRunView(ViewArg viewArg, ErpIdArg erpIdArg, K3CloudApiClient apiClient) {
        if (erpIdArg.getDataId().contains("{")){
            //适配物料清单明细进行查询逻辑，通过id拆分成主对象的id进行查询
            String dataId = erpIdArg.getDataId().substring(0, erpIdArg.getDataId().indexOf("{"));
            viewArg.setId(dataId);
        }
    }

    @Override
    public void afterRunGetReSyncObjDataById(ErpIdArg erpIdArg, List<StandardData> standardDataList) {
        String tenantId = erpIdArg.getTenantId();
        String snapId = erpIdArg.getSyncPloyDetailSnapshotId();
        String realObjApiName = erpIdArg.getObjAPIName();
        if (configurationManager.inWhiteList(tenantId, TenantConfigurationTypeEnum.BOM_V1_TENANTS)) {
            //v1版本逻辑
            processDataListV1(standardDataList, tenantId, snapId, realObjApiName);
        }else {
            handleListV2(standardDataList);
        }
    }

    private void handleListV2(List<StandardData> standardDataList){
        for (StandardData standardData : standardDataList) {
            processSingleV2(standardData);
        }
    }

    private void processSingleV2(StandardData data) {
        if (data == null || data.getMasterFieldVal() == null) {
            return;
        }
        data.getMasterFieldVal().put("quantity", 1);
        List<ObjectData> details = data.getDetailFieldVals().get("ENG_BOM.TreeEntity");
        if (CollUtil.isNotEmpty(details)) {
            for (ObjectData detail : details) {
                double num = 1d;
                Double denominator = detail.getDouble("FDENOMINATOR");
                Double numerator = detail.getDouble("FNUMERATOR");
                if (denominator != null && numerator != null) {
                    num = numerator / denominator;
                }
                detail.put("quantity", num);//固定字段，数量字段
            }
        }
    }


        /**
         * v1版本BOM处理，查询了物料数据，拼接虚拟BOM物料并发送MQ
         * 而BOM明细将伪装成主对象数据进行对接。
         */
    private void processDataListV1(List<StandardData> standardDataList, String tenantId, String snapId, String realObjApiName) {
        String erpFakeMasterApiName=null;
        if(StringUtils.isNotBlank(snapId)){
            SyncPloyDetailSnapshotEntity entity = syncPloyDetailSnapshotManager.getEntryBySnapshotId(tenantId, snapId);
            erpFakeMasterApiName =entity.getSourceObjectApiName();
        }else {////k3多数据中心有问题
            List<ErpObjectRelationshipEntity> splitObjApiName = erpObjManager.getSplitObjRelations(tenantId, realObjApiName);
            List<ErpObjectRelationshipEntity> collect = splitObjApiName.stream().filter(entity -> ErpObjSplitTypeEnum.NOT_SPLIT.equals(entity.getSplitType())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(collect)){
                erpFakeMasterApiName=collect.get(0).getErpSplitObjectApiname();
            }
        }
        if(StringUtils.isBlank(erpFakeMasterApiName)){
            return;
        }
        if (!isNeedHandleK3CloudCpq(tenantId)) {//如果没有开启cpq直接返回
            return;
        }
        List<StandardData> allData = Lists.newArrayList();
        for (StandardData bom : standardDataList) {
            List<StandardData> newBomList = dealWithBomV1(tenantId,erpFakeMasterApiName, bom);
            allData.addAll(newBomList);
        }
        standardDataList.clear();
        standardDataList.addAll(allData);
    }

    /**
     * v1版本BOM处理
     */
    private List<StandardData> dealWithBomV1(String tenantId, String erpFakeMasterApiName, StandardData bom) {
        String productId = bom.getMasterFieldVal().get("FMATERIALID.Number")==null?null: String.valueOf(bom.getMasterFieldVal().get("FMATERIALID.Number"));//物料编码
        if(StringUtils.isBlank(productId)){
            productId=bom.getMasterFieldVal().get("FMATERIALID.FNumber")==null?null: String.valueOf(bom.getMasterFieldVal().get("FMATERIALID.FNumber"));//物料编码
            if(StringUtils.isBlank(productId)){
                productId=bom.getMasterFieldVal().get("MATERIALID.Number")==null?null: String.valueOf(bom.getMasterFieldVal().get("MATERIALID.Number"));//物料编码
            }
        }
        String erpMaterialObjApiName=erpFakeMasterApiName.replaceAll(ObjectApiNameEnum.K3CLOUD_BOM.getObjApiName(),ObjectApiNameEnum.K3CLOUD_BD_MATERIAL.getObjApiName());
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setTenantId(tenantId);
        erpIdArg.setObjAPIName(erpMaterialObjApiName);
        erpIdArg.setDataId(productId);
        erpIdArg.setIncludeDetail(true);
        Result<SyncDataContextEvent> product = erpDataPreprocessService.getErpObjDataById(erpIdArg);
        if (product == null || !product.isSuccess()) {
            log.info("dealWithBomV1  erpDataPreprocessService.getErpObjDataById failed arg={},result={}", erpIdArg, product);
            return Lists.newArrayList();
        }
        //发送虚拟产品mq
        String bomEdition = String.valueOf(bom.getMasterFieldVal().get("FNumber"));
        String oldProductId = product.getData().getSourceData().getId();
        String oldProductNumber = String.valueOf(product.getData().getSourceData().get("Number"));//物料编码
        String oldProductName = String.valueOf(product.getData().getSourceData().get("name"));
        product.getData().getSourceData().putId(oldProductId + "{" + bomEdition + "}");
        product.getData().getSourceData().put("name", oldProductName + "{" + bomEdition + "}");
        product.getData().getSourceData().put("comName", oldProductNumber + "#" + oldProductName + "{" + bomEdition + "}");
        Result<SendEventMqRecord> sendEventMqRecordResult = probeErpDataService.batchSendErpDataMqByContext(Lists.newArrayList(product.getData()),true);
        if (sendEventMqRecordResult == null || !sendEventMqRecordResult.isSuccess()) {
            log.info("dealWithBomV1  erpDataPreprocessService.sendMq failed arg={},result={}", Lists.newArrayList(product.getData()), sendEventMqRecordResult);
            return Lists.newArrayList();
        }
        //把明细伪装成主对象数据
        List<StandardData> allData = Lists.newArrayList();
        StandardData newMainBean = BeanUtil.deepCopy(bom, StandardData.class);
        newMainBean.getDetailFieldVals().clear();
        String newMainProductId=productId+ "{" + bomEdition + "}";
        newMainBean.getMasterFieldVal().put("productId",newMainProductId);//固定字段，产品编码字段
        newMainBean.getMasterFieldVal().put("quantity",1);//固定字段，数量字段
        allData.add(newMainBean);
        List<ObjectData> details = bom.getDetailFieldVals().get("ENG_BOM.TreeEntity");
        if (CollectionUtils.isNotEmpty(details)) {
            String parentId = String.valueOf(newMainBean.getMasterFieldVal().get("id"));//父物料清单id
            Boolean isRequired="1".equals(newMainBean.getMasterFieldVal().get("FBOMCATEGORY"));
            for (ObjectData detail : details) {
                String childId = String.valueOf(detail.get("DetailId"));//子物料清单id
                detail.putId(parentId + "{" + childId + "}");
                detail.put("id",parentId + "{" + childId + "}");
                detail.remove("DetailId");
                StandardData newDetailBean = BeanUtil.deepCopy(newMainBean, StandardData.class);
                newDetailBean.setMasterFieldVal(detail);
                String detailProductId=newDetailBean.getMasterFieldVal().get("FMATERIALIDCHILD.Number")==null?null:String.valueOf(newDetailBean.getMasterFieldVal().get("FMATERIALIDCHILD.Number"));
                if(StringUtils.isBlank(detailProductId)){
                    detailProductId=newDetailBean.getMasterFieldVal().get("FMATERIALIDCHILD.FNumber")==null?null:String.valueOf(newDetailBean.getMasterFieldVal().get("FMATERIALIDCHILD.FNumber"));//物料编码
                    if(StringUtils.isBlank(detailProductId)){
                        detailProductId=newDetailBean.getMasterFieldVal().get("MATERIALIDCHILD.Number")==null?null:String.valueOf(newDetailBean.getMasterFieldVal().get("MATERIALIDCHILD.Number"));;//物料编码
                    }
                }
                newDetailBean.getMasterFieldVal().put("productId",detailProductId);//固定字段，产品编码字段
                Double num=1d;
                if(Objects.nonNull(newDetailBean.getMasterFieldVal().get("FDENOMINATOR"))&&Objects.nonNull(newDetailBean.getMasterFieldVal().get("FNUMERATOR"))){//分母，分子
                    Double denominator=Double.parseDouble(String.valueOf(newDetailBean.getMasterFieldVal().get("FDENOMINATOR")));
                    Double molecule=Double.parseDouble(String.valueOf(newDetailBean.getMasterFieldVal().get("FNUMERATOR")));
                    num=molecule/denominator;
                }
                newDetailBean.getMasterFieldVal().put("quantity",num);//固定字段，数量字段
                newDetailBean.getMasterFieldVal().put("isRequired",isRequired);//固定字段，必选字段
                //BOMObj需要的字段
                newDetailBean.getMasterFieldVal().put("root_id", parentId);//固定字段，根BOMid
                newDetailBean.getMasterFieldVal().put("parent_bom_id", parentId);//固定字段，父BOMid

                allData.add(newDetailBean);
            }
        }
        return allData;
    }

    /**
     * 开启了cpq才会返回true;
     *
     * @param tenantId
     * @return
     */
    public Boolean isNeedHandleK3CloudCpq(String tenantId) {
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        GetConfigValueByKeyArg arg = new GetConfigValueByKeyArg();
        arg.setKey(CrmConfigKeyConstants.CPQ);
        GetConfigValueByKeyResult isOpenCpq = skuSpuService.getConfigValueByKey(headerObj, arg);
        if (isOpenCpq == null || isOpenCpq.getCode() != 0) {
            return false;
        }
        //开启
        if (IsOpenCpqEnum.IsOpen.getValue().equals(isOpenCpq.getValue())) {
            return true;
        }
        return false;
    }
}
