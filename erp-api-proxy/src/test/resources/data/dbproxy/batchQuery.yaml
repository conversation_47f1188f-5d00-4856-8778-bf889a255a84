- name: 时间1
  config:
    querySql: |
      #YAML
      querySql: select * from customers where ${__where}
      batchWhere:
        - updated_at >= ${startTime__d} and updated_at < ${endTime__d} order by updated_at,id
      idWhere:
        - id = ${dataId}::INTEGER
  detailConfig: []
  arg:
    tenantId: ${tenantId}
    objAPIName: customers
    operationType: 2
    startTime: 1712900328000
    endTime: 1714920691000
    offset: 0
    limit: 2
  result:
    errCode: s106240000
    errMsg: 成功
    data:
      dataList:
        - objAPIName: customers
          masterFieldVal:
            id: 1
            name: 张三
            address: 北京市朝阳区
            code: ZS001
            created_at: "2024-04-12 13:37:15.644"
            updated_at: "2024-05-05 22:45:04.539"
            is_deleted: false
        - objAPIName: customers
          masterFieldVal:
            id: 2
            name: 李四
            address: 北京市朝阳区
            code: ZS002
            created_at: "2024-04-12 13:38:00.944"
            updated_at: "2024-05-05 22:45:04.539"
            is_deleted: false
- name: 时间2
  config:
    querySql: |
      #YAML
      querySql: select * from customers where ${__where}
      batchWhere:
        - updated_at >= ${startTime__d} and updated_at < ${endTime__d} order by updated_at,id
      idWhere:
        - id = ${dataId}::INTEGER
  detailConfig: []
  arg:
    tenantId: ${tenantId}
    objAPIName: customers
    operationType: 2
    startTime: 1712900328000
    endTime: 1714920691000
    offset: 2
    limit: 2
  result:
    errCode: s106240000
    errMsg: 成功
    data:
      dataList:
        - objAPIName: customers
          masterFieldVal:
            id: 3
            name: 王五
            address: 北京市朝阳区
            code: ZS003
            created_at: "2024-04-12 13:38:35.779"
            updated_at: "2024-05-05 22:45:04.539"
            is_deleted: false
        - objAPIName: customers
          masterFieldVal:
            id: 4
            name: 赵六
            address: 北京市朝阳区
            code: ZS004
            created_at: "2024-04-12 13:38:48.038"
            updated_at: "2024-05-05 22:45:04.539"
            is_deleted: false
- name: 作废1
  config:
    querySql: |
      #YAML
      querySql: select * from customers where ${__where}
      batchWhere:
        - updated_at >= ${startTime__d} and updated_at < ${endTime__d}
      batchInvalidWhere:
        - updated_at >= ${startTime__d} and updated_at < ${endTime__d} and is_deleted = true order by updated_at,id
  detailConfig: []
  arg:
    tenantId: ${tenantId}
    objAPIName: customers
    operationType: 3
    startTime: 1712900328000
    endTime: 1714920691000
    offset: 0
    limit: 2
  result:
    errCode: s106240000
    errMsg: 成功
    data:
      dataList:
        - objAPIName: customers
          masterFieldVal:
            id: 13
            name: 白杰
            address: 长治市
            code: cus-750531014035
            created_at: "2024-04-19 13:25:10.054"
            updated_at: "2024-05-05 22:46:01.524"
            is_deleted: true
        - objAPIName: customers
          masterFieldVal:
            id: 24
            name: 易芳
            address: 吉林市
            code: cus-050328233152
            created_at: "2024-04-19 13:26:53.418"
            updated_at: "2024-05-05 22:46:01.524"
            is_deleted: true




