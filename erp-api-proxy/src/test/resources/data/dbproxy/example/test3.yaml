#YAML
# 时间精度>ms
querySql: select * from test3 where ${__where}
batchWhere:
  - date = ${startTime__d} and id > ${lastMaxId} order by date,id # 当lastMaxId为空时，
  - date > ${startTime__d} and date <= ${endTime__d} order by date,id
idWhere:
  - id = ${dataId}::INTEGER
insertSql: |
  INSERT INTO test3 ("name", "date") VALUES(${name}, ${date__d});
updateSql: |
  UPDATE test3 SET name=${name}, date=${date__d} WHERE id=${id}::INTEGER;