<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <bean class="com.fxiaoke.open.erpsyncdata.admin.remote.EgressApiManager"/>


    <bean id="erOkHttpSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean" p:configName="fs-rest-api-http-support" init-method="init"/>
    <bean id="crmRestRetrofitFactory" class="com.fxiaoke.retrofitspring.fxiaoke.ConfigRetrofitSpringFactory" p:configNames="fs-crm-rest-api" init-method="init">
        <property name="okHttpSupport" ref="erOkHttpSupport"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.open.erpsyncdata.dbproxy.remote.base.service.EgressApiService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
</beans>