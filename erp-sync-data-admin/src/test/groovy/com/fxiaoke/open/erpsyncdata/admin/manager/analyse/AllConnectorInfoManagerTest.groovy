package com.fxiaoke.open.erpsyncdata.admin.manager.analyse


import spock.lang.Specification

class AllConnectorInfoManagerTest extends Specification {

    def "test subList"() {
        expect:
        def arr = new ArrayList()
        arr.add("hello")
        arr.add("hello")
        arr.add("hello")
        arr.add("hello")
        arr.add("hello")
        arr.add("hello")
        arr.add("hello")
        println(arr.subList(0, 5))
        println(arr)
        println(gain(arr))
        println(arr)
    }

    private int gain(List arr) {
        arr.add("what")
        arr.subList(0, 2).clear()
        return arr.size()
    }
}
