package com.fxiaoke.open.erpsyncdata.admin.service.superadmin

import com.fxiaoke.open.erpsyncdata.preprocess.manager.FileManager
import com.fxiaoke.open.erpsyncdata.admin.manager.analyse.AllStreamInfoManager
import com.fxiaoke.open.erpsyncdata.dbproxy.model.StreamSimpleInfo
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService
import spock.lang.Specification

class SuperAdminServiceImplTest extends Specification {

    def "test exportToExcel"(){
        given:
        def manager = Mock(AllStreamInfoManager) {
            getAllAdminStreamInfoList(*_) >> [new StreamSimpleInfo()]
        }
        FileManager fileManager = Mock(FileManager) {
            uploadTnFile(*_) >> { args ->
                def bytes = args[2]
                println(bytes)
                return "hello"
            }
        }
        I18NStringManager i18NStringManager = new I18NStringManager()
        NotificationService notificationService = Mock(NotificationService)
        def impl = new SuperAdminServiceImpl(allStreamInfoManager: manager, fileManager: fileManager, i18NStringManager: i18NStringManager, notificationService: notificationService)
        expect:
        impl.exportToExcel("123456", 100, "a111").isSuccess()
    }
}
