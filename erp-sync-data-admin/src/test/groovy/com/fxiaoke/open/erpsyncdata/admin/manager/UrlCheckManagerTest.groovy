package com.fxiaoke.open.erpsyncdata.admin.manager

import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.PlusTenantConfigManager
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException
import spock.lang.Specification
import spock.lang.Unroll

/**
 *
 * <AUTHOR> (^_−)☆
 */
@Unroll
class UrlCheckManagerTest extends Specification {
    PlusTenantConfigManager plusTenantConfigManager
    UrlCheckManager manager

    void setup() {
        plusTenantConfigManager = Mock()
        plusTenantConfigManager.inWhiteList("http://whitelist.com", TenantConfigurationTypeEnum.BASE_URL_WHITE_LIST) >> true
        plusTenantConfigManager.inWhiteList("http://************:32668", TenantConfigurationTypeEnum.BASE_URL_WHITE_LIST) >> true
        manager = new UrlCheckManager(
                plusTenantConfigManager: plusTenantConfigManager
        )
    }

    def "URL检查正常-#url"(String url) {
        when:
        manager.checkUrl(url)
        then:
        noExceptionThrown()
        where:
        url                         | _
        "http://whitelist.com"      | _//白名单
        "http://*******"            | _
        "https://www.fxiaoke.com"   | _ //公网域名
        "http://************:32668" | _ //白名单 内部ip
    }


    def "URL为内部IP-#url"(String url) {
        when:
        manager.checkUrl(url)

        then:
        ErpSyncDataException e = thrown()
        e.getErrMsg() == I18NStringEnum.kNotAllowPrivateIp.getText()
        where:
        url                         | _
        "https://oss.firstshare.cn" | _
        "http://**********"         | _
    }

    def "URL不合法-#url"(String url) {
        given:
        when:
        manager.checkUrl(url)

        then:
        ErpSyncDataException e = thrown()
        e.getErrMsg() == I18NStringEnum.kUrlIsInvalid.getText()


        where:
        url                  | _
        "http://invalid-url" | _
        "*******"            | _
    }
}
