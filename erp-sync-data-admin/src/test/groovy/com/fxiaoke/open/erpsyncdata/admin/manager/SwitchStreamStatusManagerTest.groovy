package com.fxiaoke.open.erpsyncdata.admin.manager

import com.fxiaoke.crmrestapi.common.data.FieldDescribe
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.common.result.Result
import com.fxiaoke.crmrestapi.result.ObjectDataQueryListByIdsResult
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3
import com.fxiaoke.open.erpsyncdata.admin.data.FieldMappingData
import com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData
import com.fxiaoke.open.erpsyncdata.admin.result.SyncPloyDetailResult
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.SfaApiManager
import com.fxiaoke.open.erpsyncdata.apiproxy.model.crm.BaseResult
import com.fxiaoke.open.erpsyncdata.apiproxy.model.crm.ModifyLog
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData
import com.fxiaoke.open.erpsyncdata.converter.manager.CrmMetaManager
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpSyncTimeDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpSyncTimeEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailSnapshotManager
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisCacheManager
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2
import com.fxiaoke.open.erpsyncdata.preprocess.service.AllModelDubboService
import org.apache.commons.lang3.tuple.Pair
import org.redisson.api.RLock
import org.redisson.api.RedissonClient
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2024/10/24 16:36:48
 */
class SwitchStreamStatusManagerTest extends Specification {
    SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager = Mock()
    RedisCacheManager redisCacheManager = Mock()
    ErpSyncTimeDao erpSyncTimeDao = Mock()
    SfaApiManager sfaApiManager = Mock()
    SyncLogManager syncLogManager = Mock()
    AllModelDubboService allModelDubboServiceRest = Mock()
    CrmMetaManager crmMetaManager = Mock()
    ObjectDataServiceV3 objectDataServiceV3 = Mock()
    RedissonClient redissonClient = Mock()
    ConfigCenterConfig configCenterConfig = Mock()
    RLock rlock = Mock()

    SwitchStreamStatusManager switchStreamStatusManager = new SwitchStreamStatusManager(syncPloyDetailSnapshotManager: syncPloyDetailSnapshotManager
            , redisCacheManager: redisCacheManager
            , erpSyncTimeDao: erpSyncTimeDao
            , sfaApiManager: sfaApiManager
            , syncLogManager: syncLogManager
            , allModelDubboServiceRest: allModelDubboServiceRest
            , crmMetaManager: crmMetaManager
            , objectDataServiceV3: objectDataServiceV3
            , redissonClient: redissonClient
            , configCenterConfig: configCenterConfig)

    def setup() {
        erpSyncTimeDao.setTenantId(*_) >> erpSyncTimeDao
        erpSyncTimeDao.setGlobalTenant(*_) >> erpSyncTimeDao
        redissonClient.getLock(*_) >> rlock
        rlock.tryLock(*_) >> true
    }

    @Unroll
    def "启用策略后处理 - #description"() {
        given:
        def syncPloyDetailResult = new SyncPloyDetailResult(id: "ployId",
                sourceTenantType: sourceTenantType,
                sourceObjectApiName: "objApiName",
                fieldMappings: [new FieldMappingData(sourceApiName: "field1")],
                syncConditions: new SyncConditionsData(filters: [[new FilterData(fieldApiName: "filter1")]]),
                detailObjectMappings: [new SyncPloyDetailResult.ObjectMappingInfo(sourceObjectApiName: "detailObjApiName", fieldMappings: [new FieldMappingData(sourceApiName: "detailField1")])],
                detailObjectSyncConditions: [new SyncConditionsData(apiName: "detailObjApiName", filters: [[new FilterData(fieldApiName: "detailFilter1")]])])
        configCenterConfig.getPloyDetailLastSyncTimeAndDel(*_) >> lastModifyTime
        redisCacheManager.getDelCache(*_) >> System.currentTimeMillis()
        erpSyncTimeDao.listByTenantIdAndObjectApiName(*_) >> [new ErpSyncTimeEntity(id: "id", tenantId: '88521', objectApiName: "objApiName", lastQueryMongoTime: System.currentTimeMillis())]

        sfaApiManager.queryModifyLog(*_) >> {
            return new BaseResult<>(code: 0, data: new ModifyLog.Result(modifyLogInfos: modifyLogInfos, hasMore: false))
        }
        objectDataServiceV3.queryListByIds(*_) >> new Result<>(code: 0, data: new ObjectDataQueryListByIdsResult(dataList: [new ObjectData(id: "dataId")]))
        syncLogManager.getInitLogId(*_) >> "logId"
        crmMetaManager.getMasterDetailField(*_) >> new FieldDescribe(api_name: "masterApiName")

        when:
        switchStreamStatusManager.processAfterEnablePloy('88521', needSyncDuringStop, syncPloyDetailResult)

        then:
        send * allModelDubboServiceRest.batchSendEventData2DispatcherMq(*_) >> Result2.newSuccess()
        updateTime * erpSyncTimeDao.updateLastQueryMongoTimeById(*_) >> System.currentTimeMillis()

        where:
        description               | sourceTenantType | needSyncDuringStop | lastModifyTime | modifyLogInfos                                                                                                                     | send | updateTime
        "CRM到ERP同步，有更新记录" | TenantType.CRM   | true               | 1234567890     | [new ModifyLog.Info(objectData: [new ModifyLog.DiffObjectData(fieldApiName: "field1")], dataId: "dataId1", bizOperationName: "1")] | 1    | 0
        "ERP到CRM同步，有更新记录" | TenantType.ERP   | true               | 1234567890     | [new ModifyLog.Info(objectData: [new ModifyLog.DiffObjectData(fieldApiName: "field1")], dataId: "dataId1", bizOperationName: "1")] | 0    | 1
        "CRM到ERP同步，无更新记录" | TenantType.CRM   | true               | null           | []                                                                                                                                 | 0    | 0
        "ERP到CRM同步，无更新记录" | TenantType.ERP   | true               | null           | []                                                                                                                                 | 0    | 0
        "客户选择不同步历史数据"  | TenantType.CRM   | false              | 1234567890     | [new ModifyLog.Info(objectData: [new ModifyLog.DiffObjectData(fieldApiName: "field1")], dataId: "dataId1", bizOperationName: "1")] | 0    | 0
    }


    @Unroll
    def "启用ERP到CRM同步 - #description"() {
        given:

        when:
        switchStreamStatusManager.processEnableErp2Crm('88521', "objApiName", lastModifyTime)

        then:
        1 * erpSyncTimeDao.listByTenantIdAndObjectApiName(*_) >> [new ErpSyncTimeEntity(id: "id", tenantId: '88521', objectApiName: "objApiName", lastQueryMongoTime: System.currentTimeMillis())]
        1 * erpSyncTimeDao.updateLastQueryMongoTimeById(*_) >> 1

        where:
        description      | lastModifyTime
        "有最后修改时间" | 1234567890
        "无最后修改时间" | System.currentTimeMillis()
    }


    @Unroll
    def "启用CRM到ERP同步 - #description"() {
        given:
        def syncPloyDetailResult = new SyncPloyDetailResult(id: "ployId",
                sourceObjectApiName: "objApiName",
                fieldMappings: [new FieldMappingData(sourceApiName: "field1")],
                syncConditions: new SyncConditionsData(filters: [[new FilterData(fieldApiName: "filter1")]]),
                detailObjectMappings: [new SyncPloyDetailResult.ObjectMappingInfo(sourceObjectApiName: "detailObjApiName", fieldMappings: [new FieldMappingData(sourceApiName: "detailField1")])],
                detailObjectSyncConditions: [new SyncConditionsData(apiName: "detailObjApiName", filters: [[new FilterData(fieldApiName: "detailFilter1")]])])
        crmMetaManager.getMasterDetailField(*_) >> new FieldDescribe(api_name: "masterApiName")
        objectDataServiceV3.queryListByIds(*_) >> new Result<>(code: 0, data: new ObjectDataQueryListByIdsResult(dataList: [new ObjectData(id: "dataId", masterApiName: "testMasterApiName")]))
        sfaApiManager.queryModifyLog(*_) >> {
            return new BaseResult<>(code: 0, data: new ModifyLog.Result(hasMore: false, modifyLogInfos: modifyLogInfos))
        }
        syncLogManager.getInitLogId(*_) >> "logId"
        allModelDubboServiceRest.batchSendEventData2DispatcherMq(*_) >> Result2.newSuccess()

        when:
        Pair<Set<String>, Set<String>> result = switchStreamStatusManager.processEnableCrm2Erp('88521', syncPloyDetailResult, "objApiName", lastModifyTime, lastSyncTime)

        then:
        result.getKey() == expectedResult.getKey()
        result.getValue() == expectedResult.getValue()

        where:
        description  | lastModifyTime | lastSyncTime | modifyLogInfos                                                                                                                                                                                                                                                       || expectedResult
        "有更新记录" | 1234567890     | 1234567890   | [new ModifyLog.Info(objectData: [new ModifyLog.DiffObjectData(fieldApiName: "field1")], dataId: "dataId1", bizOperationName: "1"), new ModifyLog.Info(objectData: [new ModifyLog.DiffObjectData(fieldApiName: "field1")], dataId: "dataId2", bizOperationName: "2")] || Pair.of(new HashSet<String>(["testMasterApiName", "dataId1", "dataId2"]), new HashSet<>())
        "无更新记录" | 1234567890     | 1234567890   | []                                                                                                                                                                                                                                                                   || Pair.of(new HashSet<>(), new HashSet<>())
    }

}
