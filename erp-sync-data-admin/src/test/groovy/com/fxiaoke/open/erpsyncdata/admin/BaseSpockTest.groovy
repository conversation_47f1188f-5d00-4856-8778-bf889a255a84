package com.fxiaoke.open.erpsyncdata.admin

import groovy.util.logging.Slf4j
import org.junit.Ignore
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/2/17
 */

@Slf4j
@ContextConfiguration(locations = "classpath:test-knowledgeManager.xml")
abstract class BaseSpockTest extends Specification {
    void setup() {
        log.info("set env")
        System.setProperty("process.profile", "fstest")
        System.setProperty("process.name", "fs-erp-sync-data-admin")
    }
}
