package com.fxiaoke.open.erpsyncdata.admin.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.open.erpsyncdata.admin.remote.CrmRemoteManager;
import com.fxiaoke.open.erpsyncdata.admin.result.ListObjectFieldsResult;
import com.fxiaoke.open.erpsyncdata.admin.result.ObjectFieldResult;
import com.fxiaoke.open.erpsyncdata.admin.service.FsCrmObjectService;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.PlusTenantConfigManager;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.CrmConfigKeyConstants;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.MultipleUnitStatusEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.CrmRemoteService;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class FsCrmObjectServiceImpl implements FsCrmObjectService {
    @Autowired
    private CrmRemoteManager crmRemoteManager;
    @Autowired
    private CrmRemoteService crmRemoteService;
    @Autowired
    private PlusTenantConfigManager plusTenantConfigManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    /**
     * 获取指定企业，指定对象的字段信息
     */
    @Override
    public Result<ListObjectFieldsResult> listObjectFieldsWithFilterBlackList(String tenantId, String objectApiName,String lang) {
        ListObjectFieldsResult listObjectFieldsResult = crmRemoteManager.listObjectFieldsWithFilterBlackList(tenantId, Lists.newArrayList(objectApiName)).get(objectApiName);
        dealWithListObjectFieldsResultResult(tenantId, lang, listObjectFieldsResult);
        return Result.newSuccess(listObjectFieldsResult);
    }

    private void dealWithListObjectFieldsResultResult(String tenantId, String lang, ListObjectFieldsResult listObjectFieldsResult) {
        if (listObjectFieldsResult != null && CollectionUtils.isNotEmpty(listObjectFieldsResult.getFields())) {
            if (ObjectApiNameEnum.FS_DEPARTMENTOBJ.getObjApiName().equals(listObjectFieldsResult.getObjectApiName())
                    || ObjectApiNameEnum.FS_PERSONNEL_OBJ.getObjApiName().equals(listObjectFieldsResult.getObjectApiName())) {//部门、人员
                for (ObjectFieldResult objectFieldResult : listObjectFieldsResult.getFields()) {
                    if ("dept_id".equals(objectFieldResult.getApiName()) || "user_id".equals(objectFieldResult.getApiName()) ||
                            "owner".equals(objectFieldResult.getApiName())) {//把dept_id、user_id和owner改为非必填
                        objectFieldResult.setIsRequired(false);
                    }
                    if (ErpFieldTypeEnum.department.name().equals(objectFieldResult.getType())) {//把部门类型字段改为查找关联部门
                        objectFieldResult.setType(ErpFieldTypeEnum.object_reference_many.name());
                        objectFieldResult.setTargetApiName(ObjectApiNameEnum.FS_DEPARTMENTOBJ.getObjApiName());
                    } else if (ErpFieldTypeEnum.employee.name().equals(objectFieldResult.getType())) {//把人员类型字段改为查找关联人员
                        objectFieldResult.setType(ErpFieldTypeEnum.object_reference_many.name());
                        objectFieldResult.setTargetApiName(ObjectApiNameEnum.FS_PERSONNEL_OBJ.getObjApiName());
                    }
                }
            } else if (ObjectApiNameEnum.FS_BOM_OBJ.getObjApiName().equals(listObjectFieldsResult.getObjectApiName())) {//BOMObj
                for (ObjectFieldResult objectFieldResult : listObjectFieldsResult.getFields()) {
                    if ("root_id".equals(objectFieldResult.getApiName())
                            || "parent_bom_id".equals(objectFieldResult.getApiName())) {//把root_id、parent_bom_id字段改为查找关联BOMObj
                        objectFieldResult.setType(ErpFieldTypeEnum.object_reference.name());
                        objectFieldResult.setTargetApiName(ObjectApiNameEnum.FS_BOM_OBJ.getObjApiName());
                        objectFieldResult.setIsRequired(true);
                    } else if ("product_group_id".equals(objectFieldResult.getApiName())) {//把product_group_id字段改为查找关联产品组
                        objectFieldResult.setType(ErpFieldTypeEnum.object_reference.name());
                        objectFieldResult.setTargetApiName(ObjectApiNameEnum.FS_PRODUCTGROUPOBJ.getObjApiName());
                    }
                    if ("related_core_id".equals(objectFieldResult.getApiName())){
                        //产品选配明细的关联产品组合文本类型改为查找关联产品组合
                        objectFieldResult.setType(ErpFieldTypeEnum.object_reference.name());
                        objectFieldResult.setTargetApiName(ObjectApiNameEnum.FS_BOM_CORE_OBJ.getObjApiName());
                    }
                }
            } else if (ObjectApiNameEnum.FS_PRODUCTGROUPOBJ.getObjApiName().equals(listObjectFieldsResult.getObjectApiName())) {//产品组
                for (ObjectFieldResult objectFieldResult : listObjectFieldsResult.getFields()) {
                    if ("parent_bom_id".equals(objectFieldResult.getApiName())) {//把parent_bom_id字段改为查找关联BOMObj
                        objectFieldResult.setType(ErpFieldTypeEnum.object_reference.name());
                        objectFieldResult.setTargetApiName(ObjectApiNameEnum.FS_BOM_OBJ.getObjApiName());
                        objectFieldResult.setIsRequired(true);
                    }
                }
            } else if (ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName().equalsIgnoreCase(listObjectFieldsResult.getObjectApiName())) {//产品对象
                listObjectFieldsResult.getFields().forEach((field) -> {
                    //把产品分类的字段类型修改成category类型
                    if ("category".equalsIgnoreCase(field.getApiName())) {
                        field.setType(ErpFieldTypeEnum.category.name());
                        field.setOptions(null);
                    } else if ("unit".equals(field.getApiName())) {//把unit字段改为查找关联UnitInfoObj,如果开启了多单位
                        Result<JSONObject> jsonObjectResult = crmRemoteService.checkModuleStatus(tenantId, CrmConfigKeyConstants.MULTIPLE_UNIT);
                        if (jsonObjectResult != null && jsonObjectResult.isSuccess() && jsonObjectResult.getData().getJSONObject("value") != null &&
                                MultipleUnitStatusEnum.IsMultipleUnitOpen.getValue().equals(jsonObjectResult.getData().getJSONObject("value").getString("openStatus"))) {
                            field.setType(ErpFieldTypeEnum.object_reference.name());
                            field.setTargetApiName(ObjectApiNameEnum.FS_UNITINFO_OBJ.getObjApiName());
                            field.setIsRequired(true);
                            field.setOptions(null);
                        }
                    } else if ("spu_id".equalsIgnoreCase(field.getApiName())) {
                        field.setIsRequired(false);
                    }
                });
            } else if (ObjectApiNameEnum.FS_SPU.getObjApiName().equalsIgnoreCase(listObjectFieldsResult.getObjectApiName())) {//商品对象
                listObjectFieldsResult.getFields().forEach((field) -> {
                    //把产品分类的字段类型修改成category类型
                    if ("category".equalsIgnoreCase(field.getApiName())) {
                        field.setType(ErpFieldTypeEnum.category.name());
                        field.setOptions(null);
                    }
                });
            } else if (ObjectApiNameEnum.FS_SALESORDER_PRODUCT_OBJ.getObjApiName().equalsIgnoreCase(listObjectFieldsResult.getObjectApiName())) {//订单产品对象
                ObjectFieldResult fieldResult = new ObjectFieldResult();
                fieldResult.setDefineType("package");
                fieldResult.setIsUnique(false);
                fieldResult.setIsRequired(false);//默认增加一个关联产品组的字段
                fieldResult.setApiName("product_group_id__v");
                fieldResult.setLabel(i18NStringManager.get(I18NStringEnum.s728, lang, tenantId));
                fieldResult.setType(ErpFieldTypeEnum.object_reference.name());
                fieldResult.setTargetApiName(ObjectApiNameEnum.FS_PRODUCTGROUPOBJ.getObjApiName());
                listObjectFieldsResult.getFields().add(fieldResult);
            } else if (ObjectApiNameEnum.FS_BATCHSTOCK.getObjApiName().equalsIgnoreCase(listObjectFieldsResult.getObjectApiName())) {
                //批次库存对象。取消库存id必填的属性
                for (ObjectFieldResult objectFieldResult : listObjectFieldsResult.getFields()) {
                    if ("stock_id".equals(objectFieldResult.getApiName())) {
                        objectFieldResult.setIsRequired(false);
                    }
                }
            } else if (ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName().equals(listObjectFieldsResult.getObjectApiName())) {//多单位
                for (ObjectFieldResult objectFieldResult : listObjectFieldsResult.getFields()) {
                    if ("product_id".equals(objectFieldResult.getApiName())) {//把product_id字段改为查找关联ProductObj
                        objectFieldResult.setType(ErpFieldTypeEnum.object_reference.name());
                        objectFieldResult.setTargetApiName(ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName());
                        objectFieldResult.setIsRequired(true);
                    } else if ("unit_id".equals(objectFieldResult.getApiName())) {//把unit_id字段改为查找关联UnitInfoObj
                        objectFieldResult.setType(ErpFieldTypeEnum.object_reference.name());
                        objectFieldResult.setTargetApiName(ObjectApiNameEnum.FS_UNITINFO_OBJ.getObjApiName());
                        objectFieldResult.setIsRequired(true);
                        objectFieldResult.setOptions(null);
                    }
                }
            }

            //都需要把自增编号改为非必填
            for (ObjectFieldResult objectFieldResult : listObjectFieldsResult.getFields()) {
                if (objectFieldResult.getIsUnique()) {//把自增编号改为非必填
                    objectFieldResult.setIsRequired(false);
                }
            }

            //强转字段
            Map<String, ObjectFieldResult> fieldTypeChangeMap = plusTenantConfigManager.getGlobalObjConfig(TenantConfigurationTypeEnum.FIELD_FORCE_CHANGE, new TypeReference<Map<String, ObjectFieldResult>>() {
            });
            if (fieldTypeChangeMap != null) {
                for (ObjectFieldResult field : listObjectFieldsResult.getFields()) {
                    ObjectFieldResult target = fieldTypeChangeMap.get(listObjectFieldsResult.getObjectApiName() + "." + field.getApiName());
                    if (target != null) {
                        BeanUtil.copyProperties(target, field, CopyOptions.create().ignoreNullValue());
                    }
                }
            }
        }
    }

    @Override
    public Result<Map<String, ListObjectFieldsResult>> listObjectsFieldsWithFilterBlackList(String tenantId, List<String> objectApiNames, String lang) {
        Map<String, ListObjectFieldsResult> listObjectFieldsResultMap = crmRemoteManager.listObjectFieldsWithFilterBlackList(tenantId, objectApiNames);
        for(String apiName:listObjectFieldsResultMap.keySet()){
            ListObjectFieldsResult listObjectFieldsResult=listObjectFieldsResultMap.get(apiName);
            dealWithListObjectFieldsResultResult(tenantId, lang, listObjectFieldsResult);
        }
        return Result.newSuccess(listObjectFieldsResultMap);
    }


}
