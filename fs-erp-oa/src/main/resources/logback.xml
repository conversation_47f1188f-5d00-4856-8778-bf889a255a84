<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="false" scanPeriod="60 seconds" debug="false">
    <property  scope="context" resource="./logback-desensitization-rule.properties"/>
    <!-- 自定义脱敏转换器 -->
    <conversionRule conversionWord="msg" converterClass="com.fxiaoke.open.oasyncdata.config.DesensitizationMessageConverter"/>
    <conversionRule conversionWord="msg" converterClass="com.fxiaoke.metrics.logback.MaskMessageConverter"/>
    <appender name="RollingFileAll" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 可让每天产生一个日志文件，最多15个，自动回滚 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/all-%d{yyyyMMdd}.log.zip</fileNamePattern>
            <maxHistory>15</maxHistory>
        </rollingPolicy>
        <encoder>
            <!-- 日志中默认打印traceId和userId，方便定位问题,异常栈中去掉包含如下字符的行避免打印很多无用的信息-->
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} [%class:%line] %msg%rEx{full,
                javassist,
                sun.reflect,
                org.apache,
                org.eclipse.jetty,
                $Proxy,
                java.net,
                javax.servlet,
                org.junit,
                com.sun,
                cglib,
                CGLIB,
                okhttp,
                org.jboss,
                }%n
            </pattern>
        </encoder>
    </appender>

    <appender name="RollingFileWarn" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} [%class:%line] %msg%n
            </pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${catalina.home}/logs/warn-%d{yyyyMMdd}.log.zip</FileNamePattern>
            <maxHistory>15</maxHistory>
        </rollingPolicy>
    </appender>

    <!-- 异步输出日志避免阻塞服务 记录全部日志-->
    <appender name="ASYNC-ALL" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <appender-ref ref="RollingFileAll"/>
        <includeCallerData>true</includeCallerData>
    </appender>
    <!-- 异步输出日志避免阻塞服务 记录WARN级别以上日志-->
    <appender name="ASYNC-WARN" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <appender-ref ref="RollingFileWarn"/>
        <includeCallerData>true</includeCallerData>
    </appender>

    <!-- 配置基础组件为WARN级别，避免打印过多影响服务自己日志 -->
    <logger name="com.alibaba.druid.sql" level="INFO"/>
    <logger name="org.hibernate" level="WARN"/>
    <logger name="com.alibaba.rocketmq.client" level="WARN"/>
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.apache" level="WARN"/>
    <logger name="com.fxiaoke.crmrestapi" level="WARN"/>
    <logger name="com.fxiaoke.open.oasyncdata" level="INFO"/>
    <logger name="org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping" level="WARN"/>

    <root level="INFO">
        <appender-ref ref="ASYNC-ALL"/>
        <appender-ref ref="ASYNC-WARN"/>
    </root>
</configuration>