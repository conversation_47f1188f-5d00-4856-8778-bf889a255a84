package oa.oasyncdata.impl


import com.facishare.organization.api.service.DepartmentProviderService
import com.facishare.organization.api.service.EmployeeProviderService
import com.fxiaoke.open.oasyncdata.model.QueryOAEmployeeMappingListArg
import com.fxiaoke.open.oasyncdata.service.OAUserService
import com.fxiaoke.open.oasyncdata.util.ContactsUtils
import com.google.common.collect.Lists
import oa.oasyncdata.BaseSpockTest
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

@Ignore
class OAUserServiceImplTest extends BaseSpockTest {
    @Autowired
    private OAUserService oaUserService
    @Autowired
    private EmployeeProviderService employeeProviderService
    @Autowired
    private DepartmentProviderService departmentProviderService

    @Test
    void getOAUserList() {
        QueryOAEmployeeMappingListArg arg = new QueryOAEmployeeMappingListArg()
        arg.setPageSize(20)
        arg.setPageNum(1)
        def result = oaUserService.getOAUserList("84801", arg)
        assert result.success
    }

    @Test
    void batchDeleteOAUserMapping() {
        def result = oaUserService.batchDeleteOAUserMapping("89029", "2c55c5172c8d413587f5f4b1f056ee4e",null,null,null)
        assert result.success
    }

    @Test
    void exportOAUserMapping() {
        def result = oaUserService.exportOAUserMapping("84801",1000, "f3bc773b70034f4bb0bd3bd69daa472e",
                Lists.newArrayList("805198661665554432","805200116686389248"),null)
        assert result.success
    }

    @Test
    void getEmployeeInfo() {
        def employeeDto = ContactsUtils.getEmployeeInfo("88521", "1000", employeeProviderService)
        assert employeeDto!=null
        def departmentDto = ContactsUtils.getDepInfo("88521", "1015", departmentProviderService)
        assert departmentDto!=null
    }
}
