import java.util.*;

public class TestOrder {
    public static void main(String[] args) {
        System.out.println("=== 测试操作符处理顺序 ===");
        
        String expression = "(status == \"inactive\" && type == \"normal\") || (status == \"active\" && type == \"urgent\")";
        System.out.println("表达式: " + expression);
        
        // 测试1: 检查表达式包含的操作符
        System.out.println("\n=== 操作符检查 ===");
        System.out.println("包含 &&: " + expression.contains("&&"));
        System.out.println("包含 ||: " + expression.contains("||"));
        
        // 测试2: 按不同顺序分割
        System.out.println("\n=== 先处理AND (错误顺序) ===");
        if (expression.contains("&&")) {
            System.out.println("检测到AND，尝试按AND分割...");
            List<String> andParts = splitLogicalExpression(expression, "&&");
            System.out.println("AND分割结果 (" + andParts.size() + " 部分):");
            for (int i = 0; i < andParts.size(); i++) {
                System.out.println("  部分" + (i+1) + ": [" + andParts.get(i) + "]");
            }
        }
        
        System.out.println("\n=== 先处理OR (正确顺序) ===");
        if (expression.contains("||")) {
            System.out.println("检测到OR，尝试按OR分割...");
            List<String> orParts = splitLogicalExpression(expression, "||");
            System.out.println("OR分割结果 (" + orParts.size() + " 部分):");
            for (int i = 0; i < orParts.size(); i++) {
                System.out.println("  部分" + (i+1) + ": [" + orParts.get(i) + "]");
            }
            
            // 然后处理第一个OR部分的AND
            if (!orParts.isEmpty()) {
                String firstPart = orParts.get(0);
                System.out.println("\n处理第一个OR部分的AND: " + firstPart);
                if (firstPart.contains("&&")) {
                    List<String> andParts = splitLogicalExpression(firstPart, "&&");
                    System.out.println("AND分割结果 (" + andParts.size() + " 部分):");
                    for (int i = 0; i < andParts.size(); i++) {
                        System.out.println("  部分" + (i+1) + ": [" + andParts.get(i) + "]");
                    }
                }
            }
        }
        
        System.out.println("\n=== 结论 ===");
        System.out.println("对于混合表达式，应该先处理优先级较低的操作符(||)，再处理优先级较高的操作符(&&)");
        System.out.println("这样才能正确地按照表达式的层次结构进行分割");
    }
    
    // 复制的分割方法
    private static List<String> splitLogicalExpression(String expression, String operator) {
        List<String> result = new ArrayList<>();
        
        // 去除最外层的括号
        expression = removeOuterBrackets(expression);
        
        int start = 0;
        int bracketCount = 0;
        boolean inQuotes = false;
        char quoteChar = 0;
        
        for (int i = 0; i < expression.length(); i++) {
            char c = expression.charAt(i);
            
            // 处理引号
            if ((c == '"' || c == '\'') && (i == 0 || expression.charAt(i - 1) != '\\')) {
                if (!inQuotes) {
                    inQuotes = true;
                    quoteChar = c;
                } else if (c == quoteChar) {
                    inQuotes = false;
                }
            }
            
            // 在引号内的内容不处理
            if (inQuotes) {
                continue;
            }
            
            if (c == '(') {
                bracketCount++;
            } else if (c == ')') {
                bracketCount--;
            } else if (bracketCount == 0) {
                // 检查是否匹配操作符
                boolean foundOperator = false;
                if (i + 1 < expression.length()) {
                    String twoChar = expression.substring(i, i + 2);
                    if (operator.equals("&&") && twoChar.equals("&&")) {
                        foundOperator = true;
                    } else if (operator.equals("||") && twoChar.equals("||")) {
                        foundOperator = true;
                    }
                }
                
                if (foundOperator) {
                    String part = expression.substring(start, i).trim();
                    if (!part.isEmpty()) {
                        result.add(removeOuterBrackets(part));
                    }
                    start = i + 2;
                    i++; // 跳过操作符的第二个字符
                }
            }
        }
        
        // 添加最后一部分
        String lastPart = expression.substring(start).trim();
        if (!lastPart.isEmpty()) {
            result.add(removeOuterBrackets(lastPart));
        }
        
        return result;
    }
    
    private static String removeOuterBrackets(String expression) {
        expression = expression.trim();
        
        while (expression.startsWith("(") && expression.endsWith(")")) {
            // 检查这对括号是否是匹配的外层括号
            int bracketCount = 0;
            boolean isOuterBracket = true;
            
            for (int i = 1; i < expression.length() - 1; i++) {
                if (expression.charAt(i) == '(') {
                    bracketCount++;
                } else if (expression.charAt(i) == ')') {
                    bracketCount--;
                    if (bracketCount < 0) {
                        isOuterBracket = false;
                        break;
                    }
                }
            }
            
            if (isOuterBracket && bracketCount == 0) {
                expression = expression.substring(1, expression.length() - 1).trim();
            } else {
                break;
            }
        }
        
        return expression;
    }
}
