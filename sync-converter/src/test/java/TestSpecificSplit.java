import java.util.*;

public class TestSpecificSplit {
    public static void main(String[] args) {
        System.out.println("=== 测试具体的表达式分割问题 ===");
        
        // 测试问题表达式
        String problemExpr = "(region == \"south\" && project_count > 10) || (experience_years > 5 && certification == \"expert\" && level > 8)";
        System.out.println("问题表达式: " + problemExpr);
        
        // 测试OR分割
        System.out.println("\n=== OR分割测试 ===");
        List<String> orParts = splitLogicalExpression(problemExpr, "||");
        System.out.println("OR分割结果 (" + orParts.size() + " 部分):");
        for (int i = 0; i < orParts.size(); i++) {
            System.out.println("  部分" + (i+1) + ": [" + orParts.get(i) + "]");
        }
        
        // 测试AND分割
        System.out.println("\n=== AND分割测试 ===");
        List<String> andParts = splitLogicalExpression(problemExpr, "&&");
        System.out.println("AND分割结果 (" + andParts.size() + " 部分):");
        for (int i = 0; i < andParts.size(); i++) {
            System.out.println("  部分" + (i+1) + ": [" + andParts.get(i) + "]");
        }
        
        // 如果OR分割成功，测试第一个OR部分的AND分割
        if (orParts.size() > 1) {
            System.out.println("\n=== 第一个OR部分的AND分割 ===");
            String firstOrPart = orParts.get(0);
            System.out.println("分割表达式: " + firstOrPart);
            List<String> firstAndParts = splitLogicalExpression(firstOrPart, "&&");
            System.out.println("AND分割结果 (" + firstAndParts.size() + " 部分):");
            for (int i = 0; i < firstAndParts.size(); i++) {
                System.out.println("  部分" + (i+1) + ": [" + firstAndParts.get(i) + "]");
            }
        }
        
        // 测试更复杂的表达式
        System.out.println("\n=== 测试完整的复杂表达式 ===");
        String fullExpr = "((user_type == \"admin\" && department == \"IT\") || (level > 5 && status == \"inactive\")) && ((region == \"south\" && project_count > 10) || (experience_years > 5 && certification == \"expert\" && level > 8))";
        System.out.println("完整表达式: " + fullExpr);
        
        // 先按AND分割（这是错误的顺序）
        System.out.println("\n错误顺序 - 先AND分割:");
        List<String> wrongAndParts = splitLogicalExpression(fullExpr, "&&");
        System.out.println("AND分割结果 (" + wrongAndParts.size() + " 部分):");
        for (int i = 0; i < wrongAndParts.size(); i++) {
            System.out.println("  部分" + (i+1) + ": [" + wrongAndParts.get(i) + "]");
        }
        
        // 再按OR分割（这是正确的顺序）
        System.out.println("\n正确顺序 - 先OR分割:");
        List<String> correctOrParts = splitLogicalExpression(fullExpr, "||");
        System.out.println("OR分割结果 (" + correctOrParts.size() + " 部分):");
        for (int i = 0; i < correctOrParts.size(); i++) {
            System.out.println("  部分" + (i+1) + ": [" + correctOrParts.get(i) + "]");
        }
    }
    
    // 复制的分割方法
    private static List<String> splitLogicalExpression(String expression, String operator) {
        List<String> result = new ArrayList<>();
        
        // 去除最外层的括号
        expression = removeOuterBrackets(expression);
        
        int start = 0;
        int bracketCount = 0;
        boolean inQuotes = false;
        char quoteChar = 0;
        
        for (int i = 0; i < expression.length(); i++) {
            char c = expression.charAt(i);
            
            // 处理引号
            if ((c == '"' || c == '\'') && (i == 0 || expression.charAt(i - 1) != '\\')) {
                if (!inQuotes) {
                    inQuotes = true;
                    quoteChar = c;
                } else if (c == quoteChar) {
                    inQuotes = false;
                }
            }
            
            // 在引号内的内容不处理
            if (inQuotes) {
                continue;
            }
            
            if (c == '(') {
                bracketCount++;
            } else if (c == ')') {
                bracketCount--;
            } else if (bracketCount == 0) {
                // 检查是否匹配操作符
                boolean foundOperator = false;
                if (i + 1 < expression.length()) {
                    String twoChar = expression.substring(i, i + 2);
                    if (operator.equals("&&") && twoChar.equals("&&")) {
                        foundOperator = true;
                    } else if (operator.equals("||") && twoChar.equals("||")) {
                        foundOperator = true;
                    }
                }
                
                if (foundOperator) {
                    String part = expression.substring(start, i).trim();
                    if (!part.isEmpty()) {
                        result.add(removeOuterBrackets(part));
                    }
                    start = i + 2;
                    i++; // 跳过操作符的第二个字符
                }
            }
        }
        
        // 添加最后一部分
        String lastPart = expression.substring(start).trim();
        if (!lastPart.isEmpty()) {
            result.add(removeOuterBrackets(lastPart));
        }
        
        return result;
    }
    
    private static String removeOuterBrackets(String expression) {
        expression = expression.trim();
        
        while (expression.startsWith("(") && expression.endsWith(")")) {
            // 检查这对括号是否是匹配的外层括号
            int bracketCount = 0;
            boolean isOuterBracket = true;
            
            for (int i = 1; i < expression.length() - 1; i++) {
                if (expression.charAt(i) == '(') {
                    bracketCount++;
                } else if (expression.charAt(i) == ')') {
                    bracketCount--;
                    if (bracketCount < 0) {
                        isOuterBracket = false;
                        break;
                    }
                }
            }
            
            if (isOuterBracket && bracketCount == 0) {
                expression = expression.substring(1, expression.length() - 1).trim();
            } else {
                break;
            }
        }
        
        return expression;
    }
}
