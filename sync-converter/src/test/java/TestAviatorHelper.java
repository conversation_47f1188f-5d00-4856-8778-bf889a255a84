public class TestAviatorHelper {
    public static void main(String[] args) {
        System.out.println("=== 简单测试逻辑 ===");

        // 简化的测试用例
        System.out.println("测试简单的OR表达式:");
        System.out.println("表达式: (status == \"inactive\" && type == \"normal\") || (status == \"active\" && type == \"urgent\")");
        System.out.println("环境变量: status=active, type=normal");
        System.out.println("预期结果: false (因为两个OR子表达式都失败)");
        System.out.println("第一个AND: status == \"inactive\" && type == \"normal\" -> false && true = false");
        System.out.println("第二个AND: status == \"active\" && type == \"urgent\" -> true && false = false");
        System.out.println("整体: false || false = false");
        System.out.println("应该找到的失败字段: status == \"inactive\" 或 type == \"urgent\"");
        System.out.println();

        System.out.println("测试用例7的逻辑分析:");
        System.out.println("环境变量:");
        System.out.println("  life_status = \"under_review\"");
        System.out.println("  field_gf5U2__c = \"1\"");
        System.out.println("  record_type = \"default__c\"");
        System.out.println("  owner_department = \"桃源\"");
        System.out.println("  card_type_reference__c = null");
        System.out.println("  field_hO03h__c = null");
        System.out.println();

        System.out.println("第一个OR部分分析:");
        System.out.println("1. !( life_status == \"under_review\" || ... ) -> !(true || ...) -> false");
        System.out.println("   因为life_status=\"under_review\"，所以第一个条件就是true，整个NOT表达式为false");
        System.out.println("2. 由于第一个AND条件为false，整个第一个OR部分为false");
        System.out.println();

        System.out.println("第二个OR部分分析:");
        System.out.println("1. record_type == \"record_oJ9W0__c\" -> \"default__c\" == \"record_oJ9W0__c\" -> false");
        System.out.println("2. 由于第一个AND条件为false，整个第二个OR部分为false");
        System.out.println();

        System.out.println("结论: 整个表达式 = false || false = false");
        System.out.println("应该找到的失败字段:");
        System.out.println("- 从第一个OR部分: life_status == \"under_review\" (导致NOT表达式失败)");
        System.out.println("- 从第二个OR部分: record_type == \"record_oJ9W0__c\" (直接失败)");
        System.out.println();

        System.out.println("算法应该能够找到这些失败的子表达式之一。");
    }
}
