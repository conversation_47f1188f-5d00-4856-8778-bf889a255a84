import com.googlecode.aviator.AviatorEvaluator;
import java.util.*;

public class TestAviatorHelper {
    public static void main(String[] args) {
        System.out.println("=== 简单测试 AviatorHelper 逻辑 ===");
        
        // 测试用例7的环境变量
        Map<String, Object> env = new HashMap<>();
        env.put("life_status", "under_review");
        env.put("field_gf5U2__c","1");
        env.put("record_type","default__c");
        env.put("owner_department", "桃源");
        env.put("card_type_reference__c", null);
        env.put("field_hO03h__c", null);
        
        // 测试用例7的表达式
        String expression = "(( !( life_status == \"under_review\" || life_status == \"ineffective\" || life_status == \"invalid\" ) ) && ( ( field_gf5U2__c == \"3\" || field_gf5U2__c == \"2\" ) ) && (record_type != \"record_oJ9W0__c\") && (owner_department==\"文创公司\") && (card_type_reference__c != \"2\")) || ((record_type == \"record_oJ9W0__c\") && (field_gf5U2__c == \"2\") && (field_hO03h__c != nil ) && (owner_department==\"文创公司\") && (card_type_reference__c != \"2\"))";
        
        System.out.println("环境变量: " + env);
        System.out.println("表达式: " + expression);
        
        try {
            Object result = AviatorEvaluator.execute(expression, env);
            System.out.println("整体表达式结果: " + result);
            
            // 手动分解OR表达式
            String part1 = "( !( life_status == \"under_review\" || life_status == \"ineffective\" || life_status == \"invalid\" ) ) && ( ( field_gf5U2__c == \"3\" || field_gf5U2__c == \"2\" ) ) && (record_type != \"record_oJ9W0__c\") && (owner_department==\"文创公司\") && (card_type_reference__c != \"2\")";
            String part2 = "(record_type == \"record_oJ9W0__c\") && (field_gf5U2__c == \"2\") && (field_hO03h__c != nil ) && (owner_department==\"文创公司\") && (card_type_reference__c != \"2\")";
            
            System.out.println("\n=== 分解OR表达式 ===");
            System.out.println("OR部分1: " + part1);
            Object result1 = AviatorEvaluator.execute(part1, env);
            System.out.println("OR部分1结果: " + result1);
            
            System.out.println("\nOR部分2: " + part2);
            Object result2 = AviatorEvaluator.execute(part2, env);
            System.out.println("OR部分2结果: " + result2);
            
            // 进一步分解第一个AND表达式
            if (Boolean.FALSE.equals(result1)) {
                System.out.println("\n=== 分解第一个AND表达式 ===");
                String[] andParts1 = {
                    "!( life_status == \"under_review\" || life_status == \"ineffective\" || life_status == \"invalid\" )",
                    "( field_gf5U2__c == \"3\" || field_gf5U2__c == \"2\" )",
                    "record_type != \"record_oJ9W0__c\"",
                    "owner_department==\"文创公司\"",
                    "card_type_reference__c != \"2\""
                };
                
                for (int i = 0; i < andParts1.length; i++) {
                    try {
                        Object partResult = AviatorEvaluator.execute(andParts1[i], env);
                        System.out.println("AND部分" + (i+1) + ": " + andParts1[i] + " = " + partResult);
                        if (Boolean.FALSE.equals(partResult)) {
                            System.out.println("*** 找到失败的AND部分: " + andParts1[i]);
                        }
                    } catch (Exception e) {
                        System.out.println("AND部分" + (i+1) + " 执行异常: " + e.getMessage());
                    }
                }
            }
            
            // 进一步分解第二个AND表达式
            if (Boolean.FALSE.equals(result2)) {
                System.out.println("\n=== 分解第二个AND表达式 ===");
                String[] andParts2 = {
                    "record_type == \"record_oJ9W0__c\"",
                    "field_gf5U2__c == \"2\"",
                    "field_hO03h__c != nil",
                    "owner_department==\"文创公司\"",
                    "card_type_reference__c != \"2\""
                };
                
                for (int i = 0; i < andParts2.length; i++) {
                    try {
                        Object partResult = AviatorEvaluator.execute(andParts2[i], env);
                        System.out.println("AND部分" + (i+1) + ": " + andParts2[i] + " = " + partResult);
                        if (Boolean.FALSE.equals(partResult)) {
                            System.out.println("*** 找到失败的AND部分: " + andParts2[i]);
                        }
                    } catch (Exception e) {
                        System.out.println("AND部分" + (i+1) + " 执行异常: " + e.getMessage());
                    }
                }
            }
            
        } catch (Exception e) {
            System.out.println("表达式执行异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
