import java.util.*;

public class TestComplexCases {
    public static void main(String[] args) {
        System.out.println("=== 测试复杂嵌套表达式 ===");
        
        // 测试用例10的逻辑验证
        testCase10Logic();
        
        // 测试用例11的逻辑验证
        testCase11Logic();
    }
    
    private static void testCase10Logic() {
        System.out.println("\n=== 测试用例10逻辑验证 ===");
        
        Map<String, Object> env = new HashMap<>();
        env.put("user_type", "admin");
        env.put("department", "IT");
        env.put("level", 3);
        env.put("status", "active");
        env.put("region", "north");
        env.put("project_count", 5);
        env.put("experience_years", 2);
        env.put("certification", "none");
        
        System.out.println("环境变量: " + env);
        
        // 分解表达式进行验证
        System.out.println("\n表达式分解分析:");
        
        // 第一个大的AND部分
        System.out.println("第一个大AND部分: ((user_type == \"admin\" && department == \"IT\") || (level > 5 && status == \"inactive\"))");
        
        // 第一个OR的第一部分
        boolean part1_1 = "admin".equals(env.get("user_type")) && "IT".equals(env.get("department"));
        System.out.println("  OR部分1: (user_type == \"admin\" && department == \"IT\") = " + part1_1);
        
        // 第一个OR的第二部分
        boolean part1_2 = ((Integer)env.get("level")) > 5 && "inactive".equals(env.get("status"));
        System.out.println("  OR部分2: (level > 5 && status == \"inactive\") = " + part1_2);
        
        boolean firstBigAnd = part1_1 || part1_2;
        System.out.println("  第一个大AND部分结果: " + firstBigAnd);
        
        // 第二个大的AND部分
        System.out.println("\n第二个大AND部分: ((region == \"south\" && project_count > 10) || (experience_years > 5 && certification == \"expert\" && level > 8))");
        
        // 第二个OR的第一部分
        boolean part2_1 = "south".equals(env.get("region")) && ((Integer)env.get("project_count")) > 10;
        System.out.println("  OR部分1: (region == \"south\" && project_count > 10) = " + part2_1);
        
        // 第二个OR的第二部分
        boolean part2_2 = ((Integer)env.get("experience_years")) > 5 && 
                          "expert".equals(env.get("certification")) && 
                          ((Integer)env.get("level")) > 8;
        System.out.println("  OR部分2: (experience_years > 5 && certification == \"expert\" && level > 8) = " + part2_2);
        
        boolean secondBigAnd = part2_1 || part2_2;
        System.out.println("  第二个大AND部分结果: " + secondBigAnd);
        
        boolean overall = firstBigAnd && secondBigAnd;
        System.out.println("\n整体表达式结果: " + overall);
        System.out.println("预期失败原因: 第二个大AND部分失败，因为所有OR子表达式都失败");
        System.out.println("具体失败字段应该是: region == \"south\" 或 experience_years > 5");
    }
    
    private static void testCase11Logic() {
        System.out.println("\n=== 测试用例11逻辑验证 ===");
        
        Map<String, Object> env = new HashMap<>();
        env.put("name", "John Doe");
        env.put("email", "<EMAIL>");
        env.put("age", 25);
        env.put("salary", 50000);
        env.put("department", "Engineering");
        env.put("manager_id", "MGR001");
        env.put("start_date", "2023-01-01");
        env.put("performance_score", 3.5);
        
        System.out.println("环境变量: " + env);
        
        // 简化的逻辑验证（不使用实际的string函数）
        System.out.println("\n表达式分解分析（简化版）:");
        
        // 第一个大OR的第一部分
        System.out.println("第一个大OR部分: (((string.contains(name, \"John\") && string.contains(email, \"@company.com\")) || age < 18) && ...)");
        
        // 模拟string函数结果
        boolean nameContains = env.get("name").toString().contains("John");
        boolean emailContains = env.get("email").toString().contains("@company.com");
        boolean ageCheck = ((Integer)env.get("age")) < 18;
        
        System.out.println("  string.contains(name, \"John\") = " + nameContains);
        System.out.println("  string.contains(email, \"@company.com\") = " + emailContains);
        System.out.println("  age < 18 = " + ageCheck);
        
        boolean firstOrPart = (nameContains && emailContains) || ageCheck;
        System.out.println("  第一个OR部分结果: " + firstOrPart);
        
        // 第二个部分
        boolean salaryCheck = ((Integer)env.get("salary")) > 100000;
        boolean deptCheck = "Executive".equals(env.get("department"));
        boolean managerCheck = env.get("manager_id").toString().startsWith("EXE");
        boolean perfCheck = ((Double)env.get("performance_score")) > 4.5;
        
        System.out.println("  salary > 100000 = " + salaryCheck);
        System.out.println("  department == \"Executive\" = " + deptCheck);
        System.out.println("  manager_id.startsWith(\"EXE\") = " + managerCheck);
        System.out.println("  performance_score > 4.5 = " + perfCheck);
        
        boolean secondAndPart = (salaryCheck || deptCheck) && (managerCheck || perfCheck);
        System.out.println("  第二个AND部分结果: " + secondAndPart);
        
        boolean firstBigOr = firstOrPart && secondAndPart;
        System.out.println("  第一个大OR部分结果: " + firstBigOr);
        
        // 第二个大OR部分
        boolean dateCheck = env.get("start_date").toString().endsWith("2020");
        boolean lowSalary = ((Integer)env.get("salary")) < 30000;
        boolean oldAge = ((Integer)env.get("age")) > 60;
        
        System.out.println("\n第二个大OR部分:");
        System.out.println("  start_date.endsWith(\"2020\") = " + dateCheck);
        System.out.println("  salary < 30000 = " + lowSalary);
        System.out.println("  age > 60 = " + oldAge);
        
        boolean secondBigOr = dateCheck && lowSalary && oldAge;
        System.out.println("  第二个大OR部分结果: " + secondBigOr);
        
        boolean overall = firstBigOr || secondBigOr;
        System.out.println("\n整体表达式结果: " + overall);
        System.out.println("预期失败原因: 两个大OR部分都失败");
        System.out.println("具体失败字段可能是: string.contains(email, \"@company.com\") 或 salary > 100000");
    }
}
