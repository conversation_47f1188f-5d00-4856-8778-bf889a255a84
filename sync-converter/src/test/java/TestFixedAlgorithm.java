import java.util.*;

public class TestFixedAlgorithm {
    public static void main(String[] args) {
        System.out.println("=== 测试修复后的算法 ===");
        
        // 模拟测试用例10的环境
        Map<String, Object> env = new HashMap<>();
        env.put("user_type", "admin");
        env.put("department", "IT");
        env.put("level", 3);
        env.put("status", "active");
        env.put("region", "north");
        env.put("project_count", 5);
        env.put("experience_years", 2);
        env.put("certification", "none");
        
        // 测试用例10的表达式
        String expression = "((user_type == \"admin\" && department == \"IT\") || " +
                           "(level > 5 && status == \"inactive\")) && " +
                           "((region == \"south\" && project_count > 10) || " +
                           "(experience_years > 5 && certification == \"expert\" && level > 8))";
        
        System.out.println("表达式: " + expression);
        System.out.println("环境变量: " + env);
        
        // 模拟算法逻辑
        String result = simulateAlgorithm(expression, env, 0);
        System.out.println("\n最终结果: " + result);
    }
    
    private static String simulateAlgorithm(String expression, Map<String, Object> env, int depth) {
        if (depth > 10) {
            System.out.println("DEBUG: [深度" + depth + "] 递归深度超过限制");
            return null;
        }
        
        System.out.println("DEBUG: [深度" + depth + "] 分析表达式: " + expression);
        
        // 验证表达式确实为false
        boolean result = evaluateExpression(expression, env);
        System.out.println("DEBUG: [深度" + depth + "] 表达式结果: " + result);
        
        if (result) {
            System.out.println("DEBUG: [深度" + depth + "] 表达式为true，跳过");
            return null;
        }
        
        // 如果是简单表达式，直接返回
        if (!expression.contains("&&") && !expression.contains("||")) {
            System.out.println("DEBUG: [深度" + depth + "] 找到简单失败表达式: " + expression);
            return expression;
        }
        
        // 检查顶层操作符
        List<String> topLevelAndParts = splitLogicalExpression(expression, "&&");
        List<String> topLevelOrParts = splitLogicalExpression(expression, "||");
        
        System.out.println("DEBUG: [深度" + depth + "] 顶层AND分割: " + topLevelAndParts.size() + " 部分");
        System.out.println("DEBUG: [深度" + depth + "] 顶层OR分割: " + topLevelOrParts.size() + " 部分");
        
        // 如果顶层是AND操作符
        if (topLevelAndParts.size() > 1) {
            System.out.println("DEBUG: [深度" + depth + "] 顶层是AND表达式");
            
            for (int i = 0; i < topLevelAndParts.size(); i++) {
                String part = topLevelAndParts.get(i).trim();
                System.out.println("DEBUG: [深度" + depth + "] AND部分" + i + ": [" + part + "]");
                
                if (!evaluateExpression(part, env)) {
                    System.out.println("DEBUG: [深度" + depth + "] 找到失败的AND部分: " + part);
                    
                    // 递归分析
                    String subResult = simulateAlgorithm(part, env, depth + 1);
                    if (subResult != null && !subResult.equals(part)) {
                        return subResult;
                    }
                    return part;
                }
            }
        }
        
        // 如果顶层是OR操作符
        if (topLevelOrParts.size() > 1) {
            System.out.println("DEBUG: [深度" + depth + "] 顶层是OR表达式");
            
            // 检查是否所有OR子表达式都失败
            boolean allFalse = true;
            for (String part : topLevelOrParts) {
                if (evaluateExpression(part.trim(), env)) {
                    allFalse = false;
                    break;
                }
            }
            
            if (allFalse) {
                System.out.println("DEBUG: [深度" + depth + "] 所有OR子表达式均为false");
                
                // 分析第一个失败的OR子表达式
                String firstPart = topLevelOrParts.get(0).trim();
                System.out.println("DEBUG: [深度" + depth + "] 分析第一个OR部分: " + firstPart);
                
                String subResult = simulateAlgorithm(firstPart, env, depth + 1);
                if (subResult != null && !subResult.equals(firstPart)) {
                    return subResult;
                }
                return firstPart;
            }
        }
        
        System.out.println("DEBUG: [深度" + depth + "] 无法进一步分析，返回当前表达式");
        return expression;
    }
    
    // 简化的表达式求值器
    private static boolean evaluateExpression(String expr, Map<String, Object> env) {
        expr = expr.trim();
        
        // 处理简单的比较表达式
        if (expr.contains("==")) {
            String[] parts = expr.split("==");
            if (parts.length == 2) {
                String left = parts[0].trim();
                String right = parts[1].trim().replace("\"", "");
                Object value = env.get(left);
                return Objects.equals(String.valueOf(value), right);
            }
        }
        
        if (expr.contains(">")) {
            String[] parts = expr.split(">");
            if (parts.length == 2) {
                String left = parts[0].trim();
                String right = parts[1].trim();
                Object value = env.get(left);
                if (value instanceof Integer) {
                    return ((Integer) value) > Integer.parseInt(right);
                }
            }
        }
        
        // 处理OR表达式
        if (expr.contains("||")) {
            List<String> orParts = splitLogicalExpression(expr, "||");
            for (String part : orParts) {
                if (evaluateExpression(part.trim(), env)) {
                    return true;
                }
            }
            return false;
        }
        
        // 处理AND表达式
        if (expr.contains("&&")) {
            List<String> andParts = splitLogicalExpression(expr, "&&");
            for (String part : andParts) {
                if (!evaluateExpression(part.trim(), env)) {
                    return false;
                }
            }
            return true;
        }
        
        return false; // 默认返回false
    }
    
    // 复制的分割方法
    private static List<String> splitLogicalExpression(String expression, String operator) {
        List<String> result = new ArrayList<>();
        
        // 去除最外层的括号
        expression = removeOuterBrackets(expression);
        
        int start = 0;
        int bracketCount = 0;
        boolean inQuotes = false;
        char quoteChar = 0;
        
        for (int i = 0; i < expression.length(); i++) {
            char c = expression.charAt(i);
            
            // 处理引号
            if ((c == '"' || c == '\'') && (i == 0 || expression.charAt(i - 1) != '\\')) {
                if (!inQuotes) {
                    inQuotes = true;
                    quoteChar = c;
                } else if (c == quoteChar) {
                    inQuotes = false;
                }
            }
            
            // 在引号内的内容不处理
            if (inQuotes) {
                continue;
            }
            
            if (c == '(') {
                bracketCount++;
            } else if (c == ')') {
                bracketCount--;
            } else if (bracketCount == 0) {
                // 检查是否匹配操作符
                boolean foundOperator = false;
                if (i + 1 < expression.length()) {
                    String twoChar = expression.substring(i, i + 2);
                    if (operator.equals("&&") && twoChar.equals("&&")) {
                        foundOperator = true;
                    } else if (operator.equals("||") && twoChar.equals("||")) {
                        foundOperator = true;
                    }
                }
                
                if (foundOperator) {
                    String part = expression.substring(start, i).trim();
                    if (!part.isEmpty()) {
                        result.add(removeOuterBrackets(part));
                    }
                    start = i + 2;
                    i++; // 跳过操作符的第二个字符
                }
            }
        }
        
        // 添加最后一部分
        String lastPart = expression.substring(start).trim();
        if (!lastPart.isEmpty()) {
            result.add(removeOuterBrackets(lastPart));
        }
        
        return result;
    }
    
    private static String removeOuterBrackets(String expression) {
        expression = expression.trim();
        
        while (expression.startsWith("(") && expression.endsWith(")")) {
            // 检查这对括号是否是匹配的外层括号
            int bracketCount = 0;
            boolean isOuterBracket = true;
            
            for (int i = 1; i < expression.length() - 1; i++) {
                if (expression.charAt(i) == '(') {
                    bracketCount++;
                } else if (expression.charAt(i) == ')') {
                    bracketCount--;
                    if (bracketCount < 0) {
                        isOuterBracket = false;
                        break;
                    }
                }
            }
            
            if (isOuterBracket && bracketCount == 0) {
                expression = expression.substring(1, expression.length() - 1).trim();
            } else {
                break;
            }
        }
        
        return expression;
    }
}
