package com.fxiaoke.open.erpsyncdata.dbproxy.annotation;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.factory.GrayAndDoubleWriteFactory;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.lang.reflect.Method;

/**
 * Created by fengyh on 2020/8/27.
 *
 * 用来指定entity中哪个字段是ei字段
 */


@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD,ElementType.PARAMETER})
public @interface TenantID {
    /**
     * 指定tenantId,spel表达式
     * 当前仅 {@link GrayAndDoubleWriteFactory#getTenantId} 使用
     */
    String value() default "";
}
