package com.fxiaoke.open.erpsyncdata.dbproxy.monitor;

import com.fxiaoke.open.erpsyncdata.dbproxy.util.ParallelUtils;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/8/19
 */
@Slf4j
@RestController()
@RequestMapping("monitor")
public class MonitorController {

    @GetMapping("mem/sampling/{intervalSecond}/{times}")
    public Result<String> memSampling(@PathVariable Long intervalSecond, @PathVariable Integer times) {
        ParallelUtils.createBackgroundTask().submit(() -> {
            try {
                MemoryMonitor.getInstance().sampling(intervalSecond, times);
            } catch (InterruptedException e) {
                log.info("sampling error:", e);
            }
        }).run();
        return Result.newSuccess();
    }

    @GetMapping("mem/info")
    public Result<MemoryMonitor.MemoryInfo> memInfo() {

        MemoryMonitor.MemoryInfo memoryInfo = MemoryMonitor.getInstance().getMemoryInfo();
        return Result.newSuccess(memoryInfo);
    }

    @GetMapping("mem")
    public Result<MemoryMonitor.MemoryInfoRecord> memRecord() {
        MemoryMonitor.MemoryInfoRecord memoryInfoRecord = MemoryMonitor.getInstance().getMemoryInfoRecord();
        return Result.newSuccess(memoryInfoRecord);
    }

    @GetMapping("mem2")
    public Result<String> memRecordFormat() {
        String memoryInfoRecord = MemoryMonitor.getInstance().getRecordFormat();
        return Result.newSuccess(memoryInfoRecord);
    }


    @GetMapping("mem/stop")
    public Result<Void> memStop() {
        MemoryMonitor.getInstance().stopSampling();
        return Result.newSuccess();
    }


}
