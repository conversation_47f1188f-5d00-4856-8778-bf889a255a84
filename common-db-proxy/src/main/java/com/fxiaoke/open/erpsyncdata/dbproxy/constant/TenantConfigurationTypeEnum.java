package com.fxiaoke.open.erpsyncdata.dbproxy.constant;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.open.erpsyncdata.common.constant.IntegrationStreamNodeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.K3CreateConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.MultiDomain;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.config.TriggerFlowConfig;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.OuterConnector;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

import static java.lang.Character.isDigit;

/**
 * <AUTHOR>
 * @Date: 14:54 2021/2/25
 * @Desc:
 */
@Slf4j
@NoArgsConstructor
public enum TenantConfigurationTypeEnum {
    /**
     * 测试jetCache使用
     */
    TEST_JET_CACHE,
    /**
     * 消息通知设置
     */
    messageNotification,
    /**
     * 最后消息通知时间
     */
    lastMessageNotificationTime,
    /**
     * 销售订单是否走cpq逻辑
     */
    saleOrderNeedHandleCpq,
    /**
     * 销售出库单的序列号配置
     */
    outStockSerialNumber,
    /**
     * 直接调拨单的序列号配置
     */
    transferDirectSerialNumber,
    /**
     * 销售退货单的序列号配置
     */
    returnStockSerialNumber,
    /**
     * k3商品产品是否走多单位逻辑
     */
    productNeedHandleMultipleUnit,
    /**
     * 客户和纷享的服务器之间的时间戳的差， 用来调整轮询时间的结束时间, 单位ms
     * 客户和纷享的服务器之间的时区的差，也是用这个调整，只需要调整轮询的结束之间，不需要调整开始时间。
     */
    SERVER_TIME_DIFF,

    /**
     * 这个选项用来矫正 轮询ERP的开始时间的， 增加一个偏移（正负都支持）。
     * 场景： ERP 在 05s插入的数据(因为ERP内部的实现逻辑不够严谨)，最后修改时间传[01s, 06s]这种正确的查询条件，却查不到数据。
     * 要延迟一定时间后(多为有分钟级，我们也碰到过小时级)， 再重新传入[01s, 06s]这个查询条件。
     * 由于轮询过程中，一直在往前滚动时间，因此开始时间要往回退，只能通过该配置项来实现
     */
    PROBE_ERP_START_TIME_DIFF,

    /**
     * 启用移除不需要同步的数据
     */
    ENABLE_REMOVE_NOT_NEED_SYNC,

    /**
     * 库存可用量的配置
     */
    INVENTORY_AVB_QTY,

    /**
     * 项目具有实施负责人
     */
    EXIST_PROJECT_OWNER,


    /**
     * k3使用View接口获取数据详情的对象配置(旧接口）
     */
    USE_BILLQUERY_INTERFACE_TO_VIEW,

    /**
     * 单据接口按明细拆开查询
     */
    @Deprecated
    USE_BILLQUERY_INTERFACE_SPLIT_DETAIL,

    /**
     * k3对比ExecuteBillQuery接口和view接口数据
     */
    COMPARE_BILLQUERY_VIEW_RESULT,
    /**
     * 数据维护创建错误数量阈值
     */
    DATA_MAPPING_ERROR,

    /**
     * 推送数据每分钟限制条数
     */
    LIMIT_PUSH_ERP_DATA_PER_MINUTE,
    /**
     * 实际上不一定是每分钟
     */
    TENANT_LIMIT_PUSH_ERP_DATA_PER_MINUTE,
    DEFAULT_LIMIT_PUSH_ERP_DATA_PER_MINUTE,


    /**
     * 轮询临时库是ERP往CRM限制的倍数
     */
    DEFAULT_LIMIT_QUERY_TEMP_COEFFICIENT,

    /**
     * 批量写crm,企业和对象配置
     */
    BATCH_WRITE_TO_CRM_TENANT_OBJECT_API_NAME(FormatType.listValueMap),

    /**
     * topic聚合的时间配置
     */
    MONGO_DISPATCHER_DATA_AGGREGATION_TIME(FormatType.json),
    /**
     * 处理CRM删除事件的配置,
     * 新的代码已经不需要配置控制了，作为默认逻辑了
     */
    @Deprecated
    LISTEN_CRM_DELETE_DATA,

    /**
     * 序列化null值的企业名单
     */
    SERIALIZE_NULL_TENANTS,
    /**
     * 不保存syncData的企业
     */
    NOT_SAVE_SYNC_DATA_TENANTS,

    /**
     * 企业路由配置
     */
    CONFIG_ROUTE_TENANT,

    /**
     * Top60的ei
     */
    TOP60_TENANTS,

    /**
     * 扫描临时库触发告警信息数量
     */
    SCAN_NO_TRIGGER_TEMP_NOTIFY_COUNT,
    /**
     * 扫描临时库触发限制数量
     */
    SCAN_NO_TRIGGER_TEMP_LIMIT_COUNT,

    /**
     * CPQ订单，CRM-ERP方向，只同步bom父项
     */
    ONLY_SYNC_CPQ_PARENT,

    /**
     * 配置需要检查服务状态的企业
     */
    ERP_SERVER_CHECK_TEANANTS,

    /**
     * 配置需要检查服务状态的企业允许的最大预警错误数量
     */
    ERP_SERVER_STATUS_ERROR_LIMIT,

    /**
     * 配置检查服务状态失败后再次重新检测的时间间隔
     */
    ERP_SERVER_CHECK_INTERVAL,
    /**
     * 配置企业需要检查服务状态的URL
     */
    TENANT_SERVER_CHECK_URL,
    /**
     * 产品选配实例附加明细字段映射
     */
    BOM_INSTANCE_DETAIL_EXTRA_FIELD_MAPPINGS(FormatType.json),
    /**
     * erp对象级监控白名单。
     * 格式:ei1,ei2,ei3...
     */
    ERP_TEMP_MONGO_DEADOBJ_MONITOR_WHITELIST,
    /**
     * 检查时间范围内erp有新数据产生,租户级别配置,dcId为0,channel为ALL
     * 格式:erpObjAPIName1:startTime1-endTime1,erpObjAPIName2:startTime2-endTime2,...
     */
    ERP_TEMP_MONGO_NOT_GET_DATA_MONITOR,
    /**
     * erp->crm 每分钟同步条数限制，针对CRM action业务接口.
     * 这个值的默认值只有应用专属可以调整。
     */
    TENANT_LIMIT_PER_COUNT_SECOND_2CRM(FormatType.longMap),
    /**
     * erp->crm 每分钟同步条数限制, 针对批量写PAAS库接口。
     * 这个值的默认值只有数据库专属可以调整, 且要确认有没有挂 操作CRM的函数 后才能调整。
     */
    TENANT_LIMIT_PER_COUNT_SECOND_2CRM_BATCHWRITE(FormatType.longMap),
    /**
     * crm->erp 每分钟同步条数限制
     */
    TENANT_LIMIT_PER_COUNT_SECOND_2ERP(FormatType.longMap),
    /**
     * 获取erp对象数据时的每页数量
     */
    ERP_LIST_PAGE_SIZE,
    /*** 轮询特殊分页参数，key:中间对象apiname 或者tenantId_apiname,value：每页拉取多少数据*/
    SPECIAL_LIST_SIZE,
    /*** 在一个统计维度内，条数限制(默认)2crm*/
    @Deprecated
    DEFAULT_LIMIT_PER_COUNT_SECOND_2CRM,
    /*** 在一个统计维度内，条数限制(默认)2erp*/
    @Deprecated
    DEFAULT_LIMIT_PER_COUNT_SECOND_2ERP,
    /*** getById熔断过期时间，单位秒*/
    KEY_GET_BY_ID_BREAK_EXPIRE_TIME,
    /*** getById熔断失败次数阀值*/
    KEY_GET_BY_ID_BREAK_COUNT,
    /**
     * 企业ei和所在环境的对应关系
     */
    SPECIAL_ENV_MAP,
    /**
     * 没实现getbyid接口的 ei_对象apiname， 一个企业下可能对象A实现了getbyid, 对象B没有实现。所以配置上要管理到对象这一级。
     */
    NOT_OFFER_GETBYID_INTERFACE_TENANTS,


    /*** 告警通知 */
    ALERT_NOTICE,
    /**
     * 熔断通知
     */
    BREAK_PLOY_NOTICE,
    /**
     * 异常同步增量信息,拼上集成流id作为type
     */
    @Deprecated
    SYNC_FAILED_STAT,

    /**
     * 列表筛选条件配置
     * @see TenantConfigurationTypeEnum#TIME_FILTER
     */
    @Deprecated
    LIST_FILTER,

    /**
     * 列表筛选条件配置V2,支持or,
     * 没有时间时,会自动补上默认的时间
     * 有时间的话,会以配置的时间为准,不会管作废等时间,
     * 配置了时间的,集成流配置的执行动作不生效
     * key:中间对象名称
     * value: list<list>,外面list之间是or连接,里面是and连接
     * eg: {"UYOG_CRMFL.BillHead":[[{"fieldApiName":"FModifyDate","operate":"between","fieldValue":[""],"isVariableBetween":true,"fieldType":"date_time"}]]}, 生成的filter为 FModifyDate> {ts'2023-04-19 19:54:03'} and FModifyDate<= {ts'2023-04-21 11:30:07'}
     */
    TIME_FILTER,

    /**
     * 查询作废条件
     * @see TenantConfigurationTypeEnum#TIME_FILTER
     * 旧的数据按 {@link TenantConfigurationTypeEnum#TIME_FILTER} 的配置走
     * 新的接口在配置TIME_FILTER时,TIME_FILTER_INVALID会配置默认值
     */
    TIME_FILTER_INVALID,

    /**
     * crm->erp，k3cloud销售订单更新，写到ERP侧的收款计划去掉FEntryId字段，解决整单赠送场景报“整单收款计划应收金额合计不等于整单价税合计，不允许保存”的问题
     */
    SALE_ORDER_PLAN_REMOVE_FENTRY_ID_EI_LIST(FormatType.tenantList),

    /**
     * 默认明细数量限制
     */
    DEFAULT_LIMIT_DETAIL_SIZE,

    /**
     * 特殊对象的明细数量限制
     */
    SPECIAL_LIMIT_DETAIL_SIZE(FormatType.json),
    /**
     * 按照租户，记录商品开关打开还是关闭, 格式{"ei0":"1", "ei1":"0"}, value取值参考 com.fxiaoke.open.erpsyncdata.preprocess.constant.IsOpenSpuEnum
     */
    SPU_OPEN_CLOSE_STATUS(FormatType.json),

    //下面这两个配置项，要一起操作，配置主以后，把从业配置好，
    /**
     * 需要补齐从数据后才能同步到CRM的主对象apiname
     */
    NEED_FILL_UP_CRM_OBJ_SET,

    /**
     * IP白名单
     **/
    IP_REQUEST_TENANT_WHITE_LIST,
    /**
     * 日志mongo条数限制。key：tenantId/channel/default
     */
    LIMIT_LOG_MONGO_COUNT(FormatType.longMap),

    /**
     * 精简字段后，补充研发内部固定需要使用的字段
     */
    OBJECT_FIXED_FIELD_NAME,
    /**
     * 屏蔽的企业默认全字段查询
     */
    APPROVAL_TENANT_QUERY_ALL_FIELD,

    /**
     * 需要过滤掉paasmq的企业DataSource，
     */
    TENANT_NEED_PASS_DATASOURCE(FormatType.json),

    /**
     * 需要快速过滤掉进入进入分发框架的单独引用计算统计字段
     */
    TENANT_NEED_PASS_SPECIAL_FIELD,
    /**
     * 轮询库存的字段
     */
    STOCK_FIELDS,
    /**
     * 调用crm接口，跳过查重规则
     */
    SKIP_DUPLICATE_SEARCH,
    /**
     * erpReSyncData企业专表配置
     */
    ERP_RE_SYNC_DATA_CONFIG(FormatType.json),

    /**
     * 即时库存不计算组合仓库id,库存id
     */
    IGNORE_COMBINE_STOCK_ID,

    /**
     * waiting数据延迟分发的时间
     */
    DELAY_DISPATCHER_TIME,

    /**
     * 订单新变更单删除时只给id的灰度企业名单
     * white:1;2;3;4
     */
    XORDER_DELETE_GRAY_RULE,

    /**
     * 使用直接订单变更，也就是直接更新K3C侧的订单，忽略K3C侧订单变更的配置
     */
    K3C_USE_DIRECT_ORDER_CHANGE,

    /**
     * 数据比对信息
     */
    TENANT_COMPARE_INFO(FormatType.json),

    /**
     * 管理工具使用说明
     */
    OPSTOOL_README,


    /**
     * 文件上传限速
     */
    TENANT_FILE_UPLOAD_RATE_LIMIT(FormatType.json),
    /**
     * 监控视图灰度企业
     * white:1;2;3;4
     */
    MONITOR_TENANTS,
    /**
     * 写SYNC_LOG上限,包含全局上限,默认上限和企业上限
     * 时间为60s
     * {"ALL":1000000,"default":1000,"84801":3}
     */
    WRITE_SYNC_LOG_LIMIT,
    /**
     * 写SYNC_DATA上限,包含全局上限,默认上限和企业上限
     * 时间为60s
     * {"ALL":1000000,"default":1000,"84801":3}
     */
    WRITE_SYNC_DATA_LIMIT,
    /**
     * 写INTERFACE_MONITOR上限,包含全局上限,默认上限和企业上限
     * 时间为60s
     * {"ALL":1000000,"default":1000,"84801":3}
     * 需要兼容 com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.InterfaceMonitorDataDao#maxCountPerMinute
     */
    WRITE_INTERFACE_MONITOR_LIMIT,
    /**
     * 超出日志上限后,是否直接丢弃日志
     */
    DROP_WHEN_OVER_SAVE_LOG_LIMIT_PLOY,

    /**
     * 企业开始节点超时时间
     */
    TENANT_DATA_NODES_TIME_OUT(FormatType.json),

    /**
     * 需要发送节点数据监控的企业
     */
    TENANT_NEED_SEND_NODES_MSG,

    /**
     * 使用分库mongo
     * white:1;2;3;4
     */
    SHARDING_MONGO_GRAY,

    /**
     * 是否忽略双写时写失败旧数据库
     */
    IGNORE_DOUBLE_WRITE_EXCEPTION,

    /**
     * 哪些配置的哪些企业新数据需要双写,
     * 暂时只有SHARDING_MONGO_GRAY使用
     * {"SHARDING_MONGO_GRAY":"white:739065","SHARDING_MONGO_GRAY_2":"white:*"}
     */
    NEED_DOUBLE_WRITE_NEW_BEAN_TYPE,

    /**
     * 哪些配置的哪些企业旧数据需要双写,
     * 暂时只有SHARDING_MONGO_GRAY使用
     * {"SHARDING_MONGO_GRAY":"white:739065","SHARDING_MONGO_GRAY_2":"white:*"}
     */
    NEED_DOUBLE_WRITE_OLD_BEAN_TYPE,
    /**
     * 企业分发框架堆积数据阈值, 控制一个企业最多在聚合框架堆积几分钟的数据，超过这个值，则不再从临时库往聚合框架灌数据
     */
    TENANT_DISPATCHER_REMAIN_MAXIMUM(FormatType.json),

    /**
     * 是否停止迁移
     */
    STOP_TRANSFER,
    /**
     * 聚合主和其所有从对象为一个集合的企业主对象配置
     */
    MERGE_MASTER_DETAIL_TENANT_OBJ(FormatType.json),

    /**
     * crm明细的默认聚合时间
     */
    CRM_DETAIL_OBJ_DEFAULT_AGGREGATION_TIME,

    /**
     * 禁用K3C即时库存自动填充辅助属性字段
     */
    DISABLE_K3C_INVENTORY_AUX_PROP_EI_LIST(FormatType.tenantList),


    /**
     * 企业级主从一起更新配置,按,分隔
     *
     * @see ConfigCenterConfig#NEED_FILL_UP_CRM_OBJ_SET
     * {"707988":"DeliveryNoteObj", "84801":"DeliveryNoteObj,ReturnedGoodsInvoiceObj"}
     */
    NEED_FILL_UP_CRM_OBJ_SET_BY_TENANT_ID,

    /**
     * k3c对象id,number,name key mapping
     */
    K3C_OBJ_ID_NUMBER_NAME_KEY_MAPPING(FormatType.json),


    /**
     * range类型的json表达式
     */
    RANG_EXPRESSION,


    /**
     * 检查同步中函数添加字段时间间隔,
     * 单位 秒
     * 值小于0表示不做通知
     */
    NOTIFY_DURING_FUNCTION_ADD_FIELDS_INTERVAL,

    /**
     * ERPk3的保存接口的配置项 {"IsDeleteEntry":"false","IsVerifyBaseDataField":"false"}
     */
    ERP_K3_SAVE_ARG_SETTING(FormatType.json),
    /**
     * ERPk3的保存接口的配置项 {"realApiName":{"IsDeleteEntry":"false","IsVerifyBaseDataField":"false"}}
     */
    ERP_K3_OBJ_SAVE_ARG_SETTING(FormatType.json),
    /**
     * ERPk3的接口的配置项 {"realApiName":{"1":{"IsDeleteEntry":"false","IsVerifyBaseDataField":"false"}}}
     * 1:见：com.fxiaoke.open.erpsyncdata.admin.model.K3CSaveAction
     */
    ERP_K3_OBJ_INTERFACE_ARG_SETTING(FormatType.json),
    /**
     * 需要拦截重复同步数据的企业
     */
    NEED_INTERCEPT_REPEAT_SYNC_TENANT,

    /**
     * 轮询时没有时间过滤的对象,支持按企业级,支持*通配符, ","分隔,使用真实对象
     * {"*":"SAL_MATERIALGROUP", "707988":"test123,CRM_CONTACT"}
     */
    NO_TIME_FILTER_OBJECT_API_NAME,
    /**
     * 不使用PAAS的v1/inner/rest/object_data/新增相关接口，这个配置和userPaasAddApiNames结合使用，可以让特定的对象走前端的action接口
     */
    NOT_USE_PAAS_ADD_API_NAME(FormatType.listSplitBySemicolon),
    /**
     * 不使用PAAS的v1/inner/rest/object_data/更新相关接口，这个配置和userPaasAddApiNames结合使用，可以让特定的对象走前端的action接口
     */
    NOT_USE_PAAS_UPDATE_API_NAME(FormatType.listSplitBySemicolon),

    /**
     * 查询erp数据速度限制，单位：条/s, 可配置default
     */
    QUERY_ERP_DATA_SPEED_LIMIT(FormatType.longMap),
    /**
     * 自定义函数调用次数，单位：条/s, 可配置default
     */
    CUSTOMER_FUNCTION_SPEED_LIMIT(FormatType.longMap),
    /**
     * 历史任务查询erp数据速度限制，单位：条/s, 可配置default
     */
    QUERY_ERP_DATA_HISTORY_SPEED_LIMIT(FormatType.longMap),

    /**
     * socket time out 重试企业.阻塞完成写
     */
    NEED_DO_SOCKET_TIME_OUT_TENANT,
    /**
     * 不需要重试依赖数据企业
     */
    NO_NEED_DO_RE_SYNC_DATA_TENANT,

    /**
     * dispatcher的mongo中，按照id拆分topic的企业列表
     */
    DISPATCHER_MONGO_SPLIT_BYID_TENANTS,

    /**
     * 不轮询erp数据企业，执行定时任务的地方判断
     */
    NOT_QUERY_ERP_DATA_TENANTS,
    /**
     * 数据维护查询只能查询id企业名单
     */
    ONLY_SEARCH_ID_TENANTS,
    /**
     * 不自动重试企业，超时自动重试
     */
    NOT_AUTO_SYNC_TENANTS,
    /**
     * 明细数量太多的企业对象,<tenantId,List<主对象apiName>>
     */
    TOO_MANY_DETAILS_TENANTS_OBJS,

    /**
     * #访问db限速,double解析，即支持小数，格式{"ei":20}
     */
    tenantPgAccessPerSecond,

    /**
     * k3c使用规格的企业
     */
    K3_USE_SPECIFICATION,
    /**
     * 需要先反审核再更新的企业对象列表.白名单
     */
    unAuditSaveEiObjectList,

    /**
     * 检查配额灰度企业
     */
    checkQuotaGrayTenants,

    /**
     * 不检查配额的企业列表
     */
    checkQuotaDisableTenants,
    /**
     * 需要对比明细，发送作废明细事件对象
     */
    NEED_COMPARE_DETAIL_OBJ,

    /**
     * NPATH URL格式，用于工单转乐享
     */
    NPATH_URL_FORMAT,

    /**
     * 暂停初始化表的sourceId
     */
    pauseInitTableSourceId,

    /**
     * 轮询错误时不跳过时间段，默认情况下如果不是第一页出错，会跳过时间段
     */
    NOT_SKIP_TIME_WHEN_ERROR,

    /**
     * 日志存储规则，取值1或2，1对应的规则1，2对应的规则2
     * 目前的现状对应为规则1「仅保留最近2条快照的日志」，增加一个规则2「保留所有快照的日志」，限制：日志量、时间最长90天、去掉2条快照的限制
     *
     * 规格1 适用场景： 尽可能保留更多数据近2次变动的日志，不会因为数据的频繁变动而导致日志量暴涨
     *
     * 规则2 适用场景：每变更一次即生成一条快照，保留所有快照的日志，可能会因为数据的频繁变动而导致日志量暴涨
     */
    LOG_STORAGE_RULE,

    /**
     * 不转换BigDecimal企业
     * 格式:ei1;ei2
     */
    NotChangeBigDecimalTenant(FormatType.tenantList),

    /**针对ei染色，打印DEBUG以上日志。*/
    COLOR_EI_LOG_LIST(FormatType.tenantList),

    /**
     * K3c 创建配置
     * Map[真实对象apiName: {@link K3CreateConfig}]
     */
    K3C_CREATE_CONFIG,


    /**
     *
     * 格式:gray;normal
     */
    NeedSendDispatcherMqEnv,
    /**
     * 重试节点重试数据单独集合过期时间
     */
    RE_SYNC_DATA_NODE_MSG_CONFIG(FormatType.json),

    /**
     * 前端组件限制,企业级别,没有dcID
     * @see IntegrationStreamNodeEnum#getNodeName()
     * eg:["sourceSystemNode","fieldMappingNode","reverseWriteNode","destSystemNode"]
     */
    INTEGRATION_STREAM_NODE_WHITE_LIST,


    /**
     * 企业连接器报错后,需要将消息同步到谁
     * 格式: {"tenantId": userIds}
     * eg: {"1":[123,1234],"2":[45,456]}
     */
    SEND_NOTIFY_TO_OTHER_TENANT_CONFIG,

    /**
     * v1版本bom对接的企业名单
     */
    BOM_V1_TENANTS(FormatType.tenantList),


    /**
     * 全链路日志过期时间,单位天
     * eg: 180
     *
     * 已经改为CH存储,固定90天过期
     */
    @Deprecated
    SYNC_LOG_EXPIRE_TIME_TENANT_CONFIG,

    /**
     * 集成流最后同步时间
     * tenantId,ployDetailId,ALL
     * eg: lastSyncTime
     */
    PLOY_DETAIL_LAST_SYNC_TIME,

    /**
     * 多云的域名
     */
    MULTI_CLOUD_DOMAINS(FormatType.listObject, MultiDomain.class),


    /**
     *
     * 格式:{"ei":100}
     */
    PushFrequencyLimitPerMin(FormatType.json),

    /**
     * cron表达式开始时间
     * 格式:{"ei":开始分钟数}
     * eg:{"88521":5}
     */
    CRON_BEGIN_MINUTE(FormatType.json),

    /**
     * k3云星空旗舰版创建ERP单据配置，支持配置保存或暂存动作，提交，审核接口是否执行，提交或审核失败是否删除已创建单据
     */
    K3_ULTIMATE_CREATE_ERP_OBJ_CONFIG(FormatType.json),
    /**
     * k3云星空旗舰版更新ERP单据配置，支持配置保存或暂存动作，提交，审核接口是否执行
     */
    K3_ULTIMATE_UPDATE_ERP_OBJ_CONFIG(FormatType.json),


    /**
     * 计算1天企业集成流同步数量  0,0,ALL
     * 格式:{"ei":"计算n天前的数据"}
     */
    COUNT_PLOY_SYNC_DATA(FormatType.json),

    /**
     * 查看临时表数据使用主库 0,0,ALL
     * ei1;ei2
     */
    READ_TEMP_SECONDARY_PREFERENCE(FormatType.tenantList),

    /**
     * crm作废 ->k3c作废。
     * 但是k3c的客户和物料，联系人这种基础资料没有作废接口，只有禁用接口，
     * 所以如果对象是 客户和物料，则需要调用禁用接口。
     * 格式: BD_MATERIAL;BD_Customer;BD_CommonContact;BAS_PreBaseDataTwo
     */
    K3_FORBID_OPERATION_API_NAME(FormatType.listSplitBySemicolon),

    //已知系统信息
    /**
     * 已作废
     * @see com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum#knownSystem
     */
    @Deprecated
    KNOWN_SYSTEM_INFO(FormatType.json),

    /**
     * hub信息
     * @deprecated 改为使用配置中心管理
     */
    @Deprecated
    CONNECTOR_HUB_INFO(),

    /**
     * 在字段转换过程中，如果源数据为null，或者没有，转换成目标数据的null值,
     * 格式：BD_MATERIAL.BillHeader;BD_Customer.BillHeader;
     */
    KEEP_NULL_VALUE_IN_FIELD_VALUE_CONVERTER(FormatType.listSplitBySemicolon),

    /**
     * 禁止进入环境的配置，key是进入环境,值是tenantId或者ConnectorKey
     */
    FORBID_INTO_ENV(FormatType.listValueMap),
    brushTableDataConfig,
    meedStopBrushTableDataConfig,

    /**
     * v1版本商品产品对接企业名单
     */
    SPU_V1_TENANT_LIST(FormatType.tenantList),
    /**
     * 开启了商品也允许允许更新产品名称
     */
    ALLOW_UPDATE_PRODUCT_NAME_WHILE_OPEN_SPU(FormatType.tenantList),
    /**
     * CRM提醒里面的  数据集成通知 菜单是否显示
     */
    DATA_INTEGRATION_NOTIFICATION_ENTRY_WHITE_LIST(FormatType.listSplitBySemicolon),

    /**
     * 前端环境对应的JS Md5文件名
     * eg:[{"name":"gray","ut":123,"seajsmap":[["/app-dist/erpdss-dist/modules/vuentry.js","/app-dist/erpdss-dist/modules/vuentry.53a82b2b.js"],["/app-dist/erpdss-dist/assets/css/vuentry.css","/app-dist/erpdss-dist/assets/css/vuentry.664f041f.css"]]},{"name":"normal","ut":123,"seajsmap":[["/app-dist/erpdss-dist/modules/vuentry.js","/app-dist/erpdss-dist/modules/vuentry.53a82b2b.js"],["/app-dist/erpdss-dist/assets/css/vuentry.css","/app-dist/erpdss-dist/assets/css/vuentry.664f041f.css"]]}]
     */
    ENV_SEAJS_MAP,
    /**
     * 需要双写企业（mapping迁移数据期间）配置
     */
    NeedBidirectionalWriting(FormatType.json),
    /**
     * 物料支持使用FMATERIALID作为主键d企业
     * 存在配置则生效
     */
    SUPPORT_FMATERIALID_TO_ID,

    /**
     * 是否是托管企业
     */
    MANAGED_ENTERPRISE,

    /**
     * 重试mq
     */
    RE_TRY_PAAS_MQ  ,

    /**
     * 大屏设置
     *
     */
    DATA_SCREEN_FILTER_CONDITION,

    /**
     * 字段类型转换
     */
    FIELD_FORCE_CHANGE(FormatType.json),

    /**
     * 暂停重刷数据
     */
    STOP_RETRY_DATA,

    /**
     * crm自动绑定erp字段配置,分号分隔
     * eg: 历史数据：erpIdField,erpNameField，新数据：{"isOpen":false,"type":1}
     */
    CRM_EMPLOYEE_AUTO_BIND_FIELD_CONFIG(),


    /**
     * apl类信息
     */
    APL_CLASS_INFO(FormatType.listObject, OuterConnector.class),

    /**
     * 开发者企业
     */
    DEV_TENANTS(FormatType.tenantList),

    /**
     * 把集成平台告警消息发送到纷享消息中心
     */
    SEND_ERPDSS_MSG_2_FS_MSG_CENTER,
    /**
     * 企业过期时间配置
     */
    TENANT_LOG_EXPIRE_TIME_INTERVAL(FormatType.json),
    /**
     * u8销售出库单明细支持带序列号的明细
     * 增加这个配置，尽可能不影响历史企业
     */
    U8_SALE_OUT_DETAIL_SUPPORT_SERIAL,
    /**
     * 渠道对应应用市场id
     */
    CHANNEL_APPLICATION_MARKET_ID(FormatType.json),

    /**
     * 是否触发流程配置,因为有批量更新crm的场景,无法做到连接器级别,只能是企业级别
     * key:crm对象名称 value:{@link TriggerFlowConfig} true:触发(默认值) false:不触发
     * eg:{"crmObjectName":{"triggerFlow":true,"triggerWorkFlow":false}}
     */
    TRIGGER_FLOW_CONFIG(FormatType.json),

    /**
     * sap系统版本
     */
    SAP_SYSTEM_VERSION(FormatType.listSplitBySemicolon),

    /**
     * 启用集成流时需要接口检查的列表；可以是：connectorKey, tenantId, tenantId.dcId,
     */
    NEED_INTERFACE_CHECK_ENABLE_STREAM(FormatType.listSplitBySemicolon),


    /**
     * 对象分发优先级配置，企业所有对象塞到了一个配置，需要注意有特殊的才放进去。
     */
    OBJ_DISPATCH_PRIORITY_CONFIG_MAP(FormatType.object,Map.class),

    
    /**
     * 上报数据最后时间
     */
    REPORT_DATA_2_FS_LAST_TIME,
    /**
     * 上报数据最后时间
     */
    REPORT_DATA_2_FS_EI,


    /**控制后台告警多语言,格式:{"ei1":"语言1", "ei2":"语言2"}*/
    BACKGROUND_DEFAULT_LANG(FormatType.json),
    /**
     * 重新同步ERP数据线程数
     * 默认1,防止ERP不支持多线程
     */
    RE_SYNC_ERP_DATA_THREAD_NUM,

    //push接口单企业在单个pod上最大并发线程数，数字
    PUSH_PARALLEL_THREAD_NUM,
    /**
     * 新文件上传接口
     */
    NEW_FILE_UPLOAD_INTERFACE,
    /**
     * 可以代理请求的企业列表
     */
    PROXY_REQUEST_EIS(FormatType.tenantList),
    /**
     * 需要设置updateJson的环境
     */
    NEED_SET_UPDATEJSONFIELD_ENV,
    /**
     * 不上报错误数量企业：ei1,ei2
     */
    NOT_REPORT_ERROR_NUM,
    /**
     * 云星空旗舰版签名、解密配置
     */
    ERP_K3_ULTIMATE_SIGNATURE_CONFIG(FormatType.json),

    /**
     * 批量写crm接口跳过依赖校验, 对象级, 支持*
     * key:tenantId 0 ALL
     * value: crm对象apiName ';'分隔,主从都需要写
     * eg: AccountObj;SalesOrderObj;SalesOrderProductObj
     */
    BATCH_WRITE_CRM_UNCHECK_DEPENDENCY(FormatType.listSplitBySemicolon),

    /**
     * 是否需要syncMain同步锁,listen上报只记录了企业和时间,所以暂时做租户级配置
     * key:tenantId 0 ALL
     * value: true/false 只检查是否为true {@link BooleanUtils#toBooleanObject(String)}
     */
    SYNC_PROCESS_DATA_LOCK,

    /**
     * url白名单，绕过检查
     */
    BASE_URL_WHITE_LIST,

    /**
     * 所有企业配置
     * 单独同步的erp明细对象apiName:{"main_objApiName":["detail_objApiName"]}
     */
    ERP_NEED_SINGLE_SYNC_OBJ_DEFAULT(FormatType.json),
    /**
     * 单独同步的erp明细对象apiName:{"main_objApiName":["detail_objApiName"]}
     */
    ERP_NEED_SINGLE_SYNC_OBJ_TENANT(FormatType.json),
    /**
     * 所有企业配置
     * 单独同步的erp明细对象apiName:["detail_objApiName"]
     */
    CRM_NEED_SINGLE_SYNC_OBJ_DEFAULT(FormatType.json),
    /**
     * 单独同步的erp明细对象apiName:["detail_objApiName"]
     */
    CRM_NEED_SINGLE_SYNC_OBJ_TENANT(FormatType.json),

    /**
     * 批量新增接口是否需要构造相关团队, 对象级, 支持*
     * key:tenantId 0 ALL
     * value: crm对象apiName ';'分隔,主从都需要写
     * eg: AccountObj;SalesOrderObj;SalesOrderProductObj
     */
    NEED_ADD_RELEVANT_TEAM_APINAME(FormatType.listSplitBySemicolon),

    /**
     * 免费ai请求
     */
    FREE_AI_REQUEST(FormatType.longMap),

    /**
     * 调用crm接口，填写了合作伙伴时，不往下游推送通知
     */
    IGNORE_SENDING_REMIND,
    ;

    /**
     * 格式
     */
    @Getter
    private FormatType formatType = FormatType.none;
    /**
     * 格式
     */
    @Getter
    private Type dataType;

    TenantConfigurationTypeEnum(FormatType formatType) {
        this.formatType = formatType;
    }


    TenantConfigurationTypeEnum(FormatType formatType, Type dataType) {
        this.formatType = formatType;
        this.dataType = dataType;
    }

    public enum FormatType {
        none,
        json,
        longMap,
        tenantList,//校验字符串必须是数字和分号
        /**
         * 分号分隔的列表
         */
        listSplitBySemicolon,
        /**
         * key为string，value为列表的map的json
         */
        listValueMap,
        /**
         * 对象列表，配合tClass验证
         */
        listObject,
        /**
         * 对象，配合tClass验证
         */
        object,
    }

    private static boolean isTenantList(String tenantListStr, char separator) {
        int index = 0;
        for (byte ch : tenantListStr.getBytes()) {
            index++;
            if (!isDigit(ch) && (ch != separator) && ch!= '*') {
                log.error("isTenantList chk error, tenantListStr:{} index:{} char:{} ", tenantListStr, index, ch);
                return false;
            }
        }
        return true;
    }

    public void checkFormat(String tenantID, String content) {
        TenantConfigurationTypeEnum.checkFormat(tenantID, this, content);
    }

    public static void checkFormat(String tenantID, TenantConfigurationTypeEnum type, String content) {
        boolean chkGood = true;
        switch (type.formatType) {
            case tenantList:
                chkGood = isTenantList(content, ';');
                break;
            case json:
                //校验json格式
                try {
                    JSON.parse(content);
                } catch (Exception e) {
                    chkGood = false;
                }
                ;
                break;
            case longMap:
                try {
                    JacksonUtil.fromJson(content, new TypeReference<Map<String, Long>>() {
                    });
                } catch (Exception e) {
                    chkGood = false;
                }
                break;
            case listValueMap:
                try {
                    HashMap<String, HashSet<String>> listValueMap = JacksonUtil.fromJson(content, new TypeReference<HashMap<String, HashSet<String>>>() {
                    });
                }catch (Exception e) {
                    chkGood = false;
                }
                break;
            case listObject:
                try {
                    JSON.parseArray(content, (Class<?>)type.getDataType());
                } catch (Exception e) {
                    chkGood = false;
                }
                break;
            case object:
                try {
                    JSON.parseObject(content, type.dataType);
                } catch (Exception e) {
                    chkGood = false;
                }
                break;
            default:
                break;
        }
        if (!chkGood) {
            log.error("配置文件内容格式不对,tenantID:{}, type:{}, content：{} ", tenantID, type, content);
            throw new ErpSyncDataException(I18NStringEnum.s237,tenantID);
        }
    }
}
