package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import cn.hutool.core.util.StrUtil;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpSyncDataBackStageEnvironmentEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.github.autoconf.admin.ConfigAdminClient;
import com.github.autoconf.admin.api.IConfigAdmin;
import com.github.autoconf.base.ProcessInfo;
import com.github.autoconf.helper.ConfigHelper;
import com.github.autoconf.intern.InnerUtils;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Scanner;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Component
@Slf4j
public class ConfigCenterManager {
    private IConfigAdmin iConfigAdmin = new ConfigAdminClient();

    private String eiConfigToken = "B60C41865B61CBD899144BEE75DE1CCEDB19F52CAD29D4F3D1FCE9FB24A5B0D4";   //走普通审批申请，
    private String eiConfig = "variables_erp_ei_env";  //配置文件名
    public static final String tenantIdSeparator = ",";  //企业分隔符

    private String globalName = "variables_erp_global";
    private String globalToken = "87A4512C93871CFA0746C10EF5199CF95CA8E88CEE5EF0A0CDB8A7C7A110E615";

    @SneakyThrows
    public void updateGlobalConfig(String key, String value) {
        iConfigAdmin.update(globalToken, getGlobalProfile(), globalName, key, value);
    }

    @SneakyThrows
    public String getGlobalConfig(String key) {
        final String s = iConfigAdmin.get(globalToken, globalName);
        final Map<String, String> from = InnerUtils.from(s);
        return from.get(key);
    }

    private String getGlobalProfile() {
        ProcessInfo process = ConfigHelper.getProcessInfo();
        if (process.getProfile().contains("fstest") || process.getProfileCandidates().contains("fstest")) {
            return "fstest";
        } else if (process.getProfile().contains("foneshare") || process.getProfileCandidates().contains("foneshare")) {
            return "foneshare";
        }

        throw new RuntimeException("unknown profile, profile:" + process.getProfile() + ", candidates:" + process.getProfileCandidates());
    }


    public Result<String> batchChangeTenantInfo(List<String> tenantIds, ErpSyncDataBackStageEnvironmentEnum toEnv) {//可能存在并发
        //key第一次需要手动添加
        Map<String, String> key2Value = getValueByKey(ErpSyncDataBackStageEnvironmentEnum.listConfigKeys());
        if (CollectionUtils.isEmpty(key2Value.keySet())) {
            return Result.newError("not key2Value");
        }
        removeTenantEnv(tenantIds, key2Value);
        addTenantEnv(tenantIds, toEnv, key2Value);
        try {
            if (CollectionUtils.isNotEmpty(key2Value.keySet())) {
                boolean update = iConfigAdmin.updateMultiple(eiConfigToken, getProfile(), eiConfig, key2Value);
                if (!update) {
                    return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
                }
            }
        } catch (Exception e) {
            return Result.newError(e.getMessage());
        }
        return Result.newSuccess();
    }


    public String readHubInfo() {
        String profile = getProfile();
        try {
            String configName = "erpdss-outer-connector";
            String s = iConfigAdmin.get(eiConfigToken, profile, configName);
            final Map<String, String> configMap = InnerUtils.from(s);
            log.info("readHubInfo,profile:{},configMap:{}", profile, configMap);
            return configMap.getOrDefault("hubInfo", "{}");
        } catch (Exception e) {
            throw ErpSyncDataException.wrap(e);
        }
    }

    public boolean updateHubInfo(String hubInfoStr, String editor) {
        String profile = getProfile();
        try {
            String configName = "erpdss-outer-connector";
            boolean update = iConfigAdmin.update2(eiConfigToken, profile, configName, "hubInfo", hubInfoStr, editor);
            log.info("updateHubInfo,profile:{},update:{},editor:{}", profile, update, editor);
            return update;
        } catch (Exception e) {
            throw ErpSyncDataException.wrap(e);
        }
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 2, timeUnit = TimeUnit.MINUTES)
    public Set<String> readDenyTenantIds() {
        return ImmutableSet.copyOf(readDenyCollections().stream().map(v -> v.split("_")[1]).filter(v -> v != null).collect(Collectors.toList()));
    }

    public Set<String> readDenyCollections() {
        String profile = getProfileIncludeCloud();
        if ("cloud".equals(profile)) {
            return ImmutableSet.of();
        }
        try {
            String configName = eiConfig;
            String s = iConfigAdmin.get(eiConfigToken, profile, configName);
            final Map<String, String> configMap = InnerUtils.from(s);
            String denyStr = configMap.get("denyCollections");
            if (StrUtil.isBlank(denyStr)) {
                return ImmutableSet.of();
            }
            //与com.fxiaoke.dispatcher.repository.mongo.MongoEventRepository.eventCollections 一致
            List<String> denyCollSet = Splitter.on(',').trimResults().omitEmptyStrings().splitToList(denyStr);
            return ImmutableSet.copyOf(denyCollSet);
        } catch (Exception e) {
            throw ErpSyncDataException.wrap(e);
        }
    }

    public void updateDenyCollections(String denyStr, String editor) {
        String profile = getProfile();
        try {
            String configName = eiConfig;
            boolean update = iConfigAdmin.update2(eiConfigToken, profile, configName, "denyCollections", denyStr, editor);
            log.info("updateDenyCollections,profile:{},update:{},editor:{}", profile, update, editor);
        } catch (Exception e) {
            throw ErpSyncDataException.wrap(e);
        }
    }

    private String getProfile() {//只支持foneshare和fstest
        ProcessInfo process = ConfigHelper.getProcessInfo();
        if (process != null && process.getProfile() != null) {
            if (process.getProfile().contains("foneshare")) {
                return "foneshare";
            } else if (process.getProfile().contains("fstest") || process.getProfile().contains("jacoco")) {
                return "fstest";
            }
        }
        log.info("not support update config file profile:{}", process);
        throw new ErpSyncDataException("not support update config file profile:" + process);
    }


    private String getProfileIncludeCloud() {//只支持foneshare和fstest
        ProcessInfo process = ConfigHelper.getProcessInfo();
        if (process != null && process.getProfile() != null) {
            if (process.getProfile().contains("foneshare")) {
                return "foneshare";
            } else if (process.getProfile().contains("fstest") || process.getProfile().contains("jacoco")) {
                return "fstest";
            }
        }
        return "cloud";
    }

    private void removeTenantEnv(List<String> tenantIds, Map<String, String> key2Value) {
        if (key2Value == null || CollectionUtils.isEmpty(key2Value.keySet())) {
            return;
        }
        for (String key : key2Value.keySet()) {
            String config = key2Value.get(key);
            for (String tenantId : tenantIds) {
                String startRegex = tenantId + tenantIdSeparator;
                String endRegex = tenantIdSeparator + tenantId;
                if (config.equals(tenantId)) {
                    config = config.replaceFirst(tenantId, "");
                } else if (config.startsWith(startRegex)) {
                    config = config.replaceFirst(startRegex, "");
                } else if (config.endsWith(endRegex)) {
                    int lastIndex = config.lastIndexOf(endRegex);
                    config = config.substring(0, lastIndex);
                } else {
                    config = config.replaceAll(tenantIdSeparator + tenantId + tenantIdSeparator, tenantIdSeparator);
                }
            }
            key2Value.put(key, config);
        }
    }

    private void addTenantEnv(List<String> tenantIds, ErpSyncDataBackStageEnvironmentEnum toEnv, Map<String, String> key2Value) {
        String key = toEnv.getConfigKey();
        if (key == null) {
            //normal没有配置不需要增加
            return;
        }
        String needAddConfig = key2Value.get(key);
        if (needAddConfig == null) {
            throw new ErpSyncDataException(I18NStringEnum.s3646, null).extra(toEnv.name());
        }
        StringBuilder stringBuilder = new StringBuilder(needAddConfig);
        boolean notNeedAddSeparator = StringUtils.isBlank(needAddConfig) || needAddConfig.endsWith(tenantIdSeparator);
        for (String tenantId : tenantIds) {
            if (!notNeedAddSeparator) {
                stringBuilder.append(tenantIdSeparator);
            }
            stringBuilder.append(tenantId);
            notNeedAddSeparator = false;
        }
        key2Value.put(key, stringBuilder.toString());
    }

    private Map<String, String> getValueByKey(List<String> keys) {
        Map<String, String> result = Maps.newHashMap();
        Scanner scanner = null;
        try {
            if (CollectionUtils.isEmpty(keys)) {
                return result;
            }
            String oldCmsContent = iConfigAdmin.get(eiConfigToken, getProfile(), eiConfig);
            scanner = new Scanner(oldCmsContent);
            String line = null;
            while (scanner.hasNextLine()) {
                line = scanner.nextLine();
                if (StringUtils.isBlank(line) || line.startsWith("#")) {
                    continue;
                }
                for (String key : keys) {
                    line = line.replaceAll(" ", "");//去掉所有空格
                    if (line.startsWith(key + "=")) {
                        String[] configs = line.split("=");//不能出现其他’=‘号
                        if (configs.length > 1) {
                            String config = configs[1];
                            if (StringUtils.isNotBlank(config)) {
                                result.put(key, config);
                            } else {
                                result.put(key, "");//默认空串
                            }
                        } else {
                            result.put(key, "");//默认空串
                        }
                        keys.remove(key);
                        break;
                    }
                }
                if (CollectionUtils.isEmpty(keys)) {
                    break;
                }
            }
        } catch (Exception e) {
            log.error("getValueByKey get exception,", e);
        } finally {
            if (scanner != null) {
                scanner.close();
            }
        }
        return result;
    }

}
