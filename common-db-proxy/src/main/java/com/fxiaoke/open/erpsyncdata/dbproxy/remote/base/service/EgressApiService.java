package com.fxiaoke.open.erpsyncdata.dbproxy.remote.base.service;

import com.fxiaoke.open.erpsyncdata.dbproxy.remote.base.result.EgressResult;
import com.fxiaoke.open.erpsyncdata.dbproxy.remote.result.GeoAddressResult;
import com.fxiaoke.retrofit2.http.GET;
import com.fxiaoke.retrofit2.http.Query;
import com.fxiaoke.retrofitspring.annotation.RetrofitConfig;

/**
 * <AUTHOR>
 * @date 2023/11/1 17:29:50
 * <p>
 * <a href="https://wiki.firstshare.cn/display/PlatformKnowledge/egress-api-service">egress-api-service</a>
 */
@RetrofitConfig(baseUrl = "EgressApiService", desc = "egress-api-service合并了一些公共服务，减少微服务模块和资源占用。")
public interface EgressApiService {
    @GET("api/v2/geocode/geo-address")
    EgressResult<GeoAddressResult> getGeoAddress(@Query("address") String address, @Query("cache") boolean cache);
}
