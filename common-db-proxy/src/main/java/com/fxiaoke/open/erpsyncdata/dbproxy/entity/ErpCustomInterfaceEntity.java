package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ObjApiName;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.HttpDataType;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.MapStringStringData;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * ERP自定义接口表
 *
 * <AUTHOR>
 * @date 2023.06.16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ObjApiName("objApiName")
@Table(name = "erp_custom_interface")
public class ErpCustomInterfaceEntity implements Serializable {
    private static final long serialVersionUID = 3739723344368834569L;

    @Id
    @Column(name = "id")
    private String id;

    /**
     * 企业id
     */
    @TenantID
    @Column(name = "tenant_id")
    private String tenantId;

    /**
     * 数据中心id
     */
    @Column(name = "data_center_id")
    private String dataCenterId;

    /**
     * 对象apiName
     */
    @Column(name = "obj_api_name")
    private String objApiName;

    /**
     * 接口类型
     */
    @Column(name = "interface_type")
    private ErpObjInterfaceUrlEnum interfaceType;

    /**
     * 接口URL
     */
    @Column(name = "url")
    private String url;

    /**
     * 接口请求方法，仅支持GET or POST请求
     */
    @Column(name = "request_method")
    private RequestMethod requestMethod;

    /**
     * 接口请求头，请求头格式：hashMap key-value，JSON存储
     */
    @Column(name = "request_headers")
    private MapStringStringData requestHeaders;

    /**
     * 接口请求体数据类型，目前仅支持XML和JSON
     */
    @Column(name = "request_data_type")
    private HttpDataType requestDataType;

    /**
     * 接口请求体
     */
    @Column(name = "request_body")
    private String requestBody;

    /**
     * 接口响应体数据类型，目前仅支持XML和JSON
     */
    @Column(name = "response_data_type")
    private HttpDataType responseDataType;

    /**
     * 接口响应数据示例，仅供参考，没有业务逻辑
     */
    @Column(name = "response_data_demo")
    private String responseDataDemo;

    /**
     * 主和从对象关联的xpath数据，JSON存储
     * 数据格式示例：
     * {
     *     "master_obj_api_name":"xpath",获取主对象ID的XPATH
     *     "master_obj_api_name@name":"xpath",获取主对象名称的XPATH
     *     "detail_1_obj_api_name":"xpath",
     *     "detail_2_obj_api_name":"xpath",
     *     "detail_n_obj_api_name":"xpath"
     * }
     */
    @Column(name = "xpath_data")
    private MapStringStringData xpathData;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Long updateTime;
}