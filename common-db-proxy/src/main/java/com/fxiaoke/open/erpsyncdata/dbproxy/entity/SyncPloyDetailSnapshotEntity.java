package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import cn.hutool.core.util.ObjectUtil;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import java.io.Serializable;
import javax.persistence.Table;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ObjApiName;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.IntegrationStreamNodesData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.MapStringStringData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyDetailData;
import lombok.Data;

/**
 * 同步快照实例
 */
@Data
@ObjApiName("sourceObjectApiName")
@Table(name = "sync_ploy_detail_snapshot")
public class SyncPloyDetailSnapshotEntity extends BaseEntity implements Serializable {
    /** 快照详情主键id */
    private String id;
    @TenantID
    /** 源企业账号id */
    private String sourceTenantId;
    /** 源企业主对象apiName */
    private String sourceObjectApiName;
    @TenantID
    /** 目标企业账号id */
    private String destTenantId;
    /** 目标企业主对象apiName */
    private String destObjectApiName;
    /** 策略状态 1.启用 2.停用 {@link  SyncPloyDetailStatusEnum} */
    private Integer status;
    /** 策略对象主键id */
    private String syncPloyId;
    /** 策略对象所含详情主键id */
    private String syncPloyDetailId;
    /** 策略对象数据封装 */
    private SyncPloyData syncPloyData;
    /** 策略对象所含详情数据封装 */
    private SyncPloyDetailData syncPloyDetailData;
    private String syncConditionsExpression;
    /** 从对象表达式 */
    private MapStringStringData detailObjectSyncConditionsExpressions = new MapStringStringData();

    /**
     * 判断是否有通知节点
     * @return
     */
    public boolean isContinueNotify(){
        if(ObjectUtil.isEmpty(this)||ObjectUtil.isEmpty(this.getSyncPloyDetailData().getIntegrationStreamNodes())){
            return false;
        }
        if(ObjectUtil.isEmpty(this.getSyncPloyDetailData().getIntegrationStreamNodes())){
          return false;
        }
        if(ObjectUtil.isNotEmpty(this.getSyncPloyDetailData().getIntegrationStreamNodes())){
            IntegrationStreamNodesData integrationStreamNodes = this.getSyncPloyDetailData().getIntegrationStreamNodes();
            if(ObjectUtil.isEmpty(integrationStreamNodes.getNotifyComplementNode())){
                return false;
            }
        }
        return true;
    }

    @Override
    public String toString() {
        return "SyncPloyDetailSnapshotEntity";
    }
}
