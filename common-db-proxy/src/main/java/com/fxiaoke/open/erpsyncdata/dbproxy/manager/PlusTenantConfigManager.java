package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import cn.hutool.core.convert.Convert;
import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.JetCacheName;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * tenant config加强方法
 * 以及委托了一些方法
 * 禁止写固定type的方法！！！
 * 尽量不要引入其他依赖
 *
 * <AUTHOR> (^_−)☆
 */
@Component
@Slf4j
@LogLevel(LogLevelEnum.TRACE)
public class PlusTenantConfigManager {
    @Getter
    private final TenantConfigurationManager tenantConfigurationManager;

    @Autowired
    private PlusTenantConfigManager self;

    public PlusTenantConfigManager(TenantConfigurationManager tenantConfigurationManager) {
        this.tenantConfigurationManager = tenantConfigurationManager;
    }


    public ErpTenantConfigurationEntity findNoCache(String tenantId, String dcId, TenantConfigurationTypeEnum type) {
        return tenantConfigurationManager.findNoCache(tenantId, dcId, type);
    }


    public boolean inWhiteList(String key, TenantConfigurationTypeEnum tenantConfigurationTypeEnum) {
        return tenantConfigurationManager.inWhiteList(key, tenantConfigurationTypeEnum);
    }


    /**
     * 获取配置，并转换。基础类型可用，，，请注意测试！！！
     */
    @Cached(name = JetCacheName.CONVERT_CONFIG,
            timeUnit = TimeUnit.MINUTES,
            expire = 20, localExpire = 2,
            syncLocal = true,
            cacheNullValue = true,
            cacheType = CacheType.BOTH,
            key = "args[0]+'.'+args[1]+'.'+args[2]")
    public <T> T getConfigAndConvert(String tenantId, String dataCenterId, TenantConfigurationTypeEnum type, T defaultValue, Function<String, T> convertFunc) {
        ErpTenantConfigurationEntity configurationEntity = findNoCache(tenantId, dataCenterId, type);
        if (configurationEntity == null || configurationEntity.getConfiguration() == null) {
            return defaultValue;
        }
        try {
            T convertValue = convertFunc.apply(configurationEntity.getConfiguration());
            if (convertValue == null) {
                log.warn("convert config is null,config:{}", configurationEntity);
                return defaultValue;
            }
            return convertValue;
        } catch (Exception e) {
            log.error("convert config error,config:{}", configurationEntity, e);
            return defaultValue;
        }
    }


    /**
     * 获取配置，并转换。基础类型可用，，，请注意测试！！！
     */
    public <T> T getTypeConfig(String tenantId, String dataCenterId, TenantConfigurationTypeEnum type, Type resultType, T defaultValue) {
        return self.getConfigAndConvert(tenantId, dataCenterId, type, defaultValue, config -> Convert.convert(resultType, config, defaultValue));
    }


    public <T> T getGlobalTypeConfig(TenantConfigurationTypeEnum type, Type resultType, T defaultValue) {
        return getTypeConfig("0", "0", type, resultType, defaultValue);
    }


    /**
     * 这个方法会清除本地缓存
     */
    //禁用CacheUpdate注解，没办法通知远程
    @CacheInvalidate(name = JetCacheName.CONVERT_CONFIG, key = "args[0]+'.'+args[1]+'.'+args[2]")
    public <T> void upsertConvertConfig(String tenantId, String dataCenterId, TenantConfigurationTypeEnum type, T newValue, Function<T, String> toStrFunc) {
        String newConfigStr = toStrFunc.apply(newValue);
        //更新
        tenantConfigurationManager.upsertConfigIgnoreChannel(tenantId, dataCenterId, type, newConfigStr);
    }


    @CacheInvalidate(name = JetCacheName.CONVERT_CONFIG, key = "args[0]+'.'+args[1]+'.'+args[2]")
    public void invalidConvertConfig(String tenantId, String dataCenterId, TenantConfigurationTypeEnum type) {
        log.info("invalidConvertConfig,tenantId:{},dataCenterId:{},type:{}", tenantId, dataCenterId, type);
    }


    @CacheInvalidate(name = JetCacheName.CONVERT_CONFIG, key = "args[0]+'.'+args[1]+'.'+args[2]")
    public void deleteConvertConfig(String tenantId, String dataCenterId, TenantConfigurationTypeEnum type) {
        ErpTenantConfigurationEntity noCache = findNoCache(tenantId, dataCenterId, type);
        if (noCache != null) {
            //根据id删除
            int delete = tenantConfigurationManager.deleteById(tenantId, noCache.getId());
            log.info("deleteConvertConfig,config:{},delete:{}", noCache, delete);
        } else {
            log.info("deleteConvertConfig not found,tenantId:{},dataCenterId:{},type:{}", tenantId, dataCenterId, type);
        }
    }

    /**
     * 获取配置，并转换。基础类型可用，，，请注意测试！！！
     */
    public <T> void upsertTypeConfig(String tenantId, String dataCenterId, TenantConfigurationTypeEnum type, T newValue) {
        self.upsertConvertConfig(tenantId, dataCenterId, type, newValue, v -> Convert.toStr(v));
    }

    /**
     * 更新公共配置
     */
    public <T> void upsertGlobalTypeConfig(TenantConfigurationTypeEnum type, T newValue) {
        upsertTypeConfig("0", "0", type, newValue);
    }

    /**
     * 获取配置，并使用Jackson转换。请注意测试！！！
     * 对象应当能通过jackson以及Kryo5序列化
     */
    public <T> T getObjConfig(String tenantId, String dataCenterId, TenantConfigurationTypeEnum type, TypeReference<T> tTypeReference) {
        return self.getConfigAndConvert(tenantId, dataCenterId, type, null, config -> JacksonUtil.fromJson(config, tTypeReference));
    }

    public <T> T getGlobalObjConfig(TenantConfigurationTypeEnum type, TypeReference<T> tTypeReference) {
        return getObjConfig("0", "0", type, tTypeReference);
    }


    /**
     * 获取配置，并转换。基础类型可用，，，请注意测试！！！
     */
    public <T> void upsertObjConfig(String tenantId, String dataCenterId, TenantConfigurationTypeEnum type, T newValue) {
        self.upsertConvertConfig(tenantId, dataCenterId, type, newValue, v -> JacksonUtil.toJson(v));
    }

    /**
     * 更新公共配置
     */
    public <T> void upsertGlobalObjConfig(TenantConfigurationTypeEnum type, T newValue) {
        upsertObjConfig("0", "0", type, newValue);
    }
}
