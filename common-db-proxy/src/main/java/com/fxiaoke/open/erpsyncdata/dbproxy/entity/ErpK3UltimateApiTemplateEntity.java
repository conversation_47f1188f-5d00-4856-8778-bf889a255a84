package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ObjApiName;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 云星空旗舰版api template表
 * <AUTHOR>
 * @date 2023-11-13
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ObjApiName("erpObjApiName")
@Table(name = "erp_k3_ultimate_api_template")
public class ErpK3UltimateApiTemplateEntity {
    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 企业ei
     */
    @TenantID
    private String tenantId;

    /**
     * 数据中心id
     */
    private String dataCenterId;

    /**
     * erp对象apiName
     */
    private String erpObjApiName;

    /**
     * 云星空旗舰版api template, {@see com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.template.K3UltimateApiTemplate}
     */
    private String apiTemplate;

   /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

}