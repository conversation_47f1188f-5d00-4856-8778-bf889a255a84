package com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncLogBaseInfo;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/12/6 11:06:52
 */
@Data
@ToString(callSuper = true)
public class CompareResultEntity extends IdEntity {
    /**
     * 企业
     */
    @TenantID
    private String tenantId;
    /**
     * 连接器id
     *
     * @see ErpConnectInfoEntity#getId()
     */
    private String dcId;
    /**
     * 源对象apiName
     */
    private String sourceObjApiName;
    /**
     *
     * 集成流中用于失败的函数apiName
     */
    private String destObjectApiName;

    /**
     * 源数据 crm 是name 。erp是数据id。
     */
    private String sourceDataId;
    /**
     * 不同的数据入参的map
     */

    private Map<String,Object> inputParamsMap;
    /**
     * 不同的数据返回的map
     */

    private Map<String,Object> outputParamsMap;

    /**
     * 类型type
     * @see com.fxiaoke.open.erpsyncdata.common.constant.SyncCompareConstant
     *
     */
    private String syncType;

    /**
     * 比对的结果
     */
    private boolean theSame=false;
    /**
     * 是否比对了
     */
    private boolean hasCompare=false;
    /**
     * 不一样的数据字段
     */
    private List<String> compareField;
    /**
     *创建时间
     */
    private Long createTime;
    /**
     *更新时间
     */
    private Long updateTime;
    /**
     *过期时间
     */
    private Date expireTime;
}
