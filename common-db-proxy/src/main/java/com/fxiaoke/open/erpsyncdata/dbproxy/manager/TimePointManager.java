package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import com.fxiaoke.common.IpUtil;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.ErpSyncDataLogDTO;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.TimePointRecord;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.TimePoint;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.autoconf.helper.ConfigHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/8/27
 */
@Component
@EnableAsync
@Slf4j
public class TimePointManager {

    @LogLevel(LogLevelEnum.TRACE)
    public void syncSend(TimePoint timePoint) {
        //先改为分发框架第一次聚合时间认为是发送mq时间
//        timePointDao.save(timePoint);
    }

    @LogLevel(LogLevelEnum.TRACE)
    public void syncCalculateCostAndSendBizLog(TimePointRecord timePointRecord) {
        try {
            calculateCostAndSendBizLog(timePointRecord);
        } catch (Exception e) {
            log.warn("syncCalculateCostAndSendBizLog error,", e);
        }
    }

    @LogLevel(LogLevelEnum.TRACE)
    public void calculateCostAndSendBizLog(TimePointRecord timePointRecord) {
        ErpSyncDataLogDTO.ErpSyncDataLogDTOBuilder dto = ErpSyncDataLogDTO.builder()
                .appName(ConfigHelper.getProcessInfo().getName())
                .traceId(timePointRecord.getTraceId()).tenantId(timePointRecord.getTenantId())
                .createTime(System.currentTimeMillis())
                .serverIp(IpUtil.getSiteLocalIp())
                .sourceObjApiName(timePointRecord.getObjApiName())
                .sourceDataId(timePointRecord.getSourceDataId())
                .sendDoDispatcherTime(timePointRecord.getLastSendMqTime())
                .parseTime(timePointRecord.getFirstParseTime())
                .dispatchMqWaitingCost((int) (timePointRecord.getFirstParseTime() - timePointRecord.getLastSendMqTime()))
                .listenTime(timePointRecord.getListenTime())
                .dispatchWaitingCost((int) (timePointRecord.getListenTime() - timePointRecord.getFirstParseTime()))
                .allCost((int) (timePointRecord.getAllFinishTime() - timePointRecord.getLastSendMqTime()))
                .allFinishTime(timePointRecord.getAllFinishTime());
        for (TimePointRecord.SyncDataTimePoint syncDataTimePoint : timePointRecord.getSyncDataTimePoints()) {
            List<Pair<String, Long>> split = syncDataTimePoint.getTimePoints();
            //每次同步记录
            int triggerCost = 0, cplTriggerCost = 0, processCost = 0, cplProcessCost = 0, writeCost = 0, cplWriteCost = 0, reverseWrite2CrmCost = 0;
            //使用当前同步的时间作为同步耗时，而不是从listen开始计算，但是all cost就会包含等待的。
            for (int i = 0; i < split.size() - 1; i++) {
                Pair<String, Long> cur = split.get(i);
                Pair<String, Long> next = split.get(i + 1);
                switch (cur.getKey()) {
                    case "streamBegin":
                        //这个触发时间由于多策略处理，后面的策略会把之前策略执行耗时算上
                        triggerCost = (int) (next.getValue() - cur.getValue());
                        break;
                    case "completeEventTrigger":
                        cplTriggerCost = (int) (next.getValue() - cur.getValue());
                        break;
                    case "doProcess":
                        processCost = (int) (next.getValue() - cur.getValue());
                        break;
                    case "completeProcess":
                        cplProcessCost = (int) (next.getValue() - cur.getValue());
                        break;
                    case "doWrite":
                        writeCost = (int) (next.getValue() - cur.getValue());
                        break;
                    case "reverseWrite2Crm":
                        reverseWrite2CrmCost = (int) (next.getValue() - cur.getValue());
                        break;
                    case "completeDataWrite":
                        cplWriteCost = (int) (next.getValue() - cur.getValue());
                        break;
                    default:
                }
            }
            //使用当前同步的时间作为同步耗时，而不是从listen开始计算，但是all cost就会包含等待的。
            long streamBeginTime = CollUtil.getFirst(split).getValue();
            long streamEndTime = CollUtil.getLast(split).getValue();
            dto.syncDataId(syncDataTimePoint.getSyncDataId())
                    .syncCost((int) (streamEndTime - streamBeginTime))
                    .triggerCost(triggerCost)
                    .cplTriggerCost(cplTriggerCost)
                    .processCost(processCost)
                    .cplProcessCost(cplProcessCost)
                    .writeCost(writeCost)
                    .reverseWrite2CrmCost(reverseWrite2CrmCost)
                    .cplWriteCost(cplWriteCost)
                    .count(ObjectUtil.defaultIfNull(syncDataTimePoint.getCount(), 1))
                    .reWriteFailed(syncDataTimePoint.getReWriteFailed())
                    .reWriteFailedMsg(syncDataTimePoint.getReWriteFailedMsg())
                    .afterSyncFailed(syncDataTimePoint.getAfterSyncFailed()).afterSyncFailedMsg(syncDataTimePoint.getAfterSyncFailedMsg())
                    .syncStepException(syncDataTimePoint.getSyncStepException()).syncStepExceptionMsg(syncDataTimePoint.getSyncStepExceptionMsg())
                    .throwable(syncDataTimePoint.getThrowable()).throwableMsg(syncDataTimePoint.getThrowableMsg());
            BizLogClient.send("biz-log-erpsyncdata", Pojo2Protobuf.toMessage(dto.build(), com.fxiaoke.log.ErpSyncDataLog.class).toByteArray());
            if (log.isDebugEnabled()) {
                log.info("send kafka biz-log-erpsyncdata,dto:{}", dto);
            }
        }
    }
}
