package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ObjApiName;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ObjApiName("objApiName")
@Table(name = "erp_db_proxy_config")
public class ErpDBProxyConfigEntity implements Serializable {

    private static final long serialVersionUID = 715754173597403535L;

    @Id
    @Column(name = "id")
    private String id;

    @TenantID
    @Column(name = "tenant_id")
    private String tenantId;

    @Column(name = "data_center_id")
    private String dataCenterId;

    @Column(name = "obj_api_name")
    private String objApiName;

    @Column(name = "db_key")
    private String dbKey;//dbkey

    @Column(name = "query_sql")
    private String querySql;//查询数据sql

    /**
     * 每次调用动态更新开始时间参数
     */
    @Column(name = "always_offset_zero")
    private boolean alwaysOffsetZero;

    @Column(name = "date_time_condition_field")
    private String dateTimeConditionField;

    @Column(name = "date_format")
    private String dateFormat;

    @Column(name = "query_id_sql")
    private String queryIdSql;


    @Column(name = "parent_obj_api_name")
    private String parentObjApiName;

    @Column(name = "insert_sql")
    private String insertSql;

    @Column(name = "update_sql")
    private String updateSql;

    @Column(name = "invalid_sql")
    private String invalidSql;

    @Transient
    private Map<String, ErpDBProxyConfigEntity> details=new HashMap<>();

    @Column(name = "create_time")
    private Long createTime;

    @Column(name = "update_time")
    private Long updateTime;

    @Column(name = "last_modify_by")
    private String lastModifyBy;

}
