package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "erp_u8_eai_config")
public class ErpU8EaiConfigEntity implements Serializable {

    private static final long serialVersionUID = -2436144165594820515L;
    @Id
    @Column(name = "id")
    private String id;

    @TenantID
    @Column(name = "tenant_id")
    private String tenantId;

    @Column(name = "obj_api_name")
    private String objApiName;

    @Column(name = "data_center_id")
    private String dataCenterId;

    @Column(name = "id_field")
    private String idField;//主键

    @Column(name = "db_key")
    private String dbKey;//dbkey

    @Column(name = "query_sql")
    private String querySql;//查询数据sql

    @Column(name = "date_time_condition_field")
    private String dateTimeConditionField;

    @Column(name = "label")
    private String label;

    @Column(name = "query_id_sql")
    private String queryIdSql;

    @Column(name = "use_combine")
    private Boolean useCombine=false;

    @Column(name = "parent_obj_api_name")
    private String parentObjApiName;

    @Column(name = "extend")
    private String extend;

    @Transient
    private Map<String, ErpU8EaiConfigEntity> details=new HashMap<>();

    @Column(name = "create_time")
    private Long createTime;

    @Column(name = "update_time")
    private Long updateTime;

    @Column(name = "last_modify_by")
    private String lastModifyBy;

}
