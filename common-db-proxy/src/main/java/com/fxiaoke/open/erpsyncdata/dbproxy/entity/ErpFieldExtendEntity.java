package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ObjApiName;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * erp对象字段扩展信息
 * 暂时只有K3Cloud使用
 * <AUTHOR> (^_−)☆
 * @date 2020/11/4
 */
@Data
@ObjApiName("objApiName")
@Table(name = "erp_field_extend")
public class ErpFieldExtendEntity implements Serializable {
    @Id
    private String id;

    @TenantID
    private String tenantId;

    /**
     * 真实apiName
     */
    private String objApiName;

    private String fieldApiName;

    private ErpFieldTypeEnum fieldDefineType;

    private String viewCode;

    private String viewExtend;

    private String saveCode;

    private String saveExtend;

    private String queryCode;

    private String erpFieldType;
    /**
     * 标识字段是否用来查询
     */
    private Boolean usedQuery;

    /**
     * 权重，默认值65536，即2的16次方
     */
    private Long priority;

    private Long createTime;

    private Long updateTime;

    /**
     * 数据中心id
     */
    private String dataCenterId;
}