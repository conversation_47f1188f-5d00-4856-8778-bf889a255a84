package com.fxiaoke.open.erpsyncdata.dbproxy.manager.custom;

import com.fxiaoke.open.erpsyncdata.dbproxy.dao.custom.ErpObjIdNumberMappingDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjIdNumberMappingEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

/**
 * K3C物料表管理器
 * <AUTHOR>
 * @date 20230313
 */
@Component
public class ErpObjIdNumberMappingManager {
    @Autowired
    private ErpObjIdNumberMappingDao erpObjIdNumberMappingDao;

    public int insert(String tenantId,String dcId,String objApiName,String dataId,String dataNumber,String dataName) {
        ErpObjIdNumberMappingEntity entity = new ErpObjIdNumberMappingEntity();
        entity.setId(UUID.randomUUID().toString());
        entity.setTenantId(tenantId);
        entity.setDcId(dcId);
        entity.setObjApiName(objApiName);
        entity.setDataId(dataId);
        entity.setDataNumber(dataNumber);
        entity.setDataName(dataName);
        long time = System.currentTimeMillis();
        entity.setCreateTime(time);
        entity.setUpdateTime(time);
        return erpObjIdNumberMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(entity);
    }

    public int updateDataNumber(String tenantId,String dcId,String objApiName,String dataId,String dataNumber) {
        return erpObjIdNumberMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .updateDataNumber(tenantId,dcId,objApiName, dataId,dataNumber);
    }

    public ErpObjIdNumberMappingEntity queryByDataId(String tenantId,String dcId,String objApiName,String dataId) {
        ErpObjIdNumberMappingEntity erpObjIdNumberMappingEntity = erpObjIdNumberMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .queryByDataId(tenantId,dcId,objApiName,dataId);
        return erpObjIdNumberMappingEntity;
    }

    public List<ErpObjIdNumberMappingEntity> queryByDataNumber(String tenantId,String dcId,String objApiName,String materialNumber) {
        List<ErpObjIdNumberMappingEntity> entityList = erpObjIdNumberMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .queryByDataNumber(tenantId,dcId,objApiName,materialNumber);
        return entityList;
    }
}
