package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.cache.impl.WeakCache;
import cn.hutool.core.util.StrUtil;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.StreamSimpleInfo;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjSplitTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ObjectMappingVo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 14:58 2022/4/25
 * @Desc:
 */
@Component
@Slf4j
public class SyncPloyDetailManager {
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;

    private final Long timeOutSecond = TimeUnit.MINUTES.toSeconds(10L);
    /**
     * 储存企业的集成流对象信息
     * key：tenantId，
     * value：源对象apiName->目标对象apiName
     */
    private final WeakCache<String, Set<ObjectMappingVo>> tenantStreamObjCache = new WeakCache<>(timeOutSecond);

    private static TimedCache<String, List<SyncPloyDetailEntity>> tenantEnableIntegrationStreamCache = CacheUtil.newTimedCache(1000 * 60 * 5);

    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;

    static {
        tenantEnableIntegrationStreamCache.schedulePrune(1000 * 60 * 3);
    }

    @LogLevel(LogLevelEnum.TRACE)
    @Cached(expire = 60, cacheType = CacheType.LOCAL)
    public SyncPloyDetailEntity getEntryById(String tenantId, String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        SyncPloyDetailEntity entity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, id);
        return entity;
    }

    /**
     * 检查是否存在集成流，有缓存。
     *
     * @param tenantId
     * @param srcObj
     * @param destObj
     * @return
     */
    public boolean checkStreamExist(String tenantId, String srcObj, String destObj) {
        if (!StrUtil.isAllNotEmpty(tenantId, srcObj, destObj)) {
            return false;
        }
        Set<ObjectMappingVo> pairs = getObjectMappingPairs(tenantId);
        return pairs.contains(ObjectMappingVo.of(srcObj, destObj));
    }

    public List<ObjectMappingVo> getStreamMappingByDest(String tenantId, String destObj) {
        List<ObjectMappingVo> result=Lists.newArrayList();
        if (!StrUtil.isAllNotEmpty(tenantId, destObj)) {
            return result;
        }
        Set<ObjectMappingVo> pairs = getObjectMappingPairs(tenantId);
        for(ObjectMappingVo objectMappingVo:pairs){
            if(destObj.equals(objectMappingVo.getDestObjectApiName())){
                result.add(objectMappingVo);
            }
        }
        return result;
    }

    private Set<ObjectMappingVo> getObjectMappingPairs(String tenantId) {
        Set<ObjectMappingVo> pairs = tenantStreamObjCache.get(tenantId, false);
        if (pairs == null) {
            pairs = new HashSet<>();
            //从数据库读取数据，并放入缓存，明细也要查
            List<SyncPloyDetailEntity> entities = adminSyncPloyDetailDao.setGlobalTenant(tenantId).listObjMappingByTenantId(tenantId);
            for (SyncPloyDetailEntity entity : entities){
                pairs.add(ObjectMappingVo.of(entity.getSourceObjectApiName(),entity.getDestObjectApiName()));
                if (entity.getDetailObjectMappings()!=null){
                    for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : entity.getDetailObjectMappings()) {
                        pairs.add(ObjectMappingVo.of(detailObjectMapping.getSourceObjectApiName(),detailObjectMapping.getDestObjectApiName()));
                    }
                }
            }
            tenantStreamObjCache.put(tenantId, pairs);
        }
        return pairs;
    }

    /**
     * 查询启用状态的集成流
     */
    @Cached(expire = 30,timeUnit = TimeUnit.MINUTES,cacheType = CacheType.LOCAL)
    public  List<Pair<String, String>> queryEnableIntegrationStream(String tenantId) {

        List<SyncPloyDetailEntity> enablePloyDetail = this.queryPartialFieldsEnableIntegrationStreamByCache(tenantId);
        List<Pair<String, String>> nameList = Lists.newArrayList();
        for (SyncPloyDetailEntity syncPloyDetailEntity : enablePloyDetail) {
            //先获取从对象source-dest
            for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : syncPloyDetailEntity.getDetailObjectMappings()) {
                Pair<String, String> detailNameMap = Pair.of(detailObjectMapping.getSourceObjectApiName(), detailObjectMapping.getDestObjectApiName());
                nameList.add(detailNameMap);
            }
            Pair<String, String> masterNameMap = Pair.of(syncPloyDetailEntity.getSourceObjectApiName(), syncPloyDetailEntity.getDestObjectApiName());
            nameList.add(masterNameMap);
        }
        return nameList;
    }

    /**
     * 企业启用的策略
     * 注意:这个接口只返回了部分字段
     * tenant_id,source_object_api_name,dest_object_api_name,detail_object_mappings,integration_stream_nodes
     */
    public  List<SyncPloyDetailEntity> queryPartialFieldsEnableIntegrationStreamByCache(String tenantId) {
        List<SyncPloyDetailEntity> ployDetailEntities = tenantEnableIntegrationStreamCache.get(tenantId, false);
        if(ployDetailEntities!=null){//
            return ployDetailEntities;
        }
        if(tenantEnableIntegrationStreamCache.size()==0){//初始化,针对定时任务的调用，整体初始化，整体失效
            synchronized (this){
                if(tenantEnableIntegrationStreamCache.size()==0){
                    List<String> allTenantIds = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(DataBaseBatchIndexUtil.notTenantId)).listTenantId();
                    //所有策略，策略属于配置，数量不多，如果数量暴涨，需要考虑内存压力
                    List<List<String>> partition = Lists.partition(allTenantIds, 500);//每批最大为500
                    for(List<String> eis:partition){
                        List<SyncPloyDetailEntity> enablePloyDetail = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                                .listByStatus(eis,SyncPloyDetailStatusEnum.ENABLE.getStatus());
                        Map<String, List<SyncPloyDetailEntity>> collect = enablePloyDetail.stream().filter(entity -> StringUtils.isNotBlank(entity.getTenantId()))
                                .collect(Collectors.groupingBy(entity -> entity.getTenantId()));
                        for(String ei:eis){
                            if(collect.containsKey(ei)){
                                tenantEnableIntegrationStreamCache.put(ei,collect.get(ei));
                            }else{
                                tenantEnableIntegrationStreamCache.put(ei,Lists.newArrayList());
                            }
                        }
                    }
                }
            }
            ployDetailEntities = tenantEnableIntegrationStreamCache.get(tenantId, false);
            if(ployDetailEntities!=null){
                return ployDetailEntities;
            }
        }
        ployDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listByTenantIdAndStatus(tenantId, SyncPloyDetailStatusEnum.ENABLE.getStatus());
        return ployDetailEntities;
    }

    @LogLevel(LogLevelEnum.TRACE)
    @Cached(expire = 60, cacheType = CacheType.LOCAL)
    public SyncPloyDetailEntity getEntityByTenantIdAndObjApiName(String tenantId, String sourceObjectApiName,String destObjectApiName) {

        SyncPloyDetailEntity entity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findOne(tenantId,sourceObjectApiName,destObjectApiName);

        return entity;
    }


    public List<SyncPloyDetailEntity> getAllPloyDetail(String tenantId, String splitObjApiName,String realObjApiName) {
        ErpObjectEntity query = new ErpObjectEntity();
        query.setTenantId(tenantId);
        query.setErpObjectApiName(splitObjApiName);
        List<ErpObjectEntity> erpFakeObjList = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
        if (CollectionUtils.isNotEmpty(erpFakeObjList)) {
            ErpObjectEntity fakeObj = erpFakeObjList.get(0);
            String dcId = fakeObj.getDataCenterId();
            ErpObjectRelationshipEntity relationshipQyery = new ErpObjectRelationshipEntity();
            relationshipQyery.setTenantId(tenantId);
            relationshipQyery.setErpRealObjectApiname(realObjApiName);
            relationshipQyery.setDataCenterId(dcId);
            relationshipQyery.setSplitType(ErpObjSplitTypeEnum.NOT_SPLIT);
            List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(relationshipQyery);
            if (CollectionUtils.isNotEmpty(erpObjectRelationshipEntities)) {
                List<String> fakeObjList = erpObjectRelationshipEntities.stream().map(ErpObjectRelationshipEntity::getErpSplitObjectApiname).collect(Collectors.toList());
                return adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                        .listByTenantIdAndSourceObjs(tenantId, fakeObjList, SyncPloyDetailStatusEnum.ENABLE.getStatus());
            }
        }
        return Lists.newArrayList();
    }

    public List<SyncPloyDetailEntity> getAllPloyDetailBySplitName(String tenantId, String splitObjApiName) {
        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listByTenantIdAndSourceObjs(tenantId, Lists.newArrayList(splitObjApiName), SyncPloyDetailStatusEnum.ENABLE.getStatus());

        return syncPloyDetailEntities;
    }

    /**
     * 注意:这个接口只返回了部分字段
     * tenant_id,source_object_api_name,dest_object_api_name,detail_object_mappings,integration_stream_nodes
     */
    public  List<SyncPloyDetailEntity> listPartialFieldsByTenantId(String tenantId) {
        return this.queryPartialFieldsEnableIntegrationStreamByCache(tenantId);
    }


    @Cached(expire = 60, cacheType = CacheType.LOCAL)
    public List<String> listEnableStreamIdsBySrcObj(String tenantId,String sourceDcId, String sourceObjectApiName,Integer status) {
        return adminSyncPloyDetailDao.listIdBySource(tenantId, sourceDcId, sourceObjectApiName, status);
    }

    public List<StreamSimpleInfo> listByCrmObjApiName(String tenantId, String crmObjApiName) {
        return adminSyncPloyDetailDao.listByCrmObjApiName(tenantId, crmObjApiName);
    }
}
