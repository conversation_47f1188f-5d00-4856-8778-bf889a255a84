package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.facishare.converter.EIEAConverter;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NTempFileUpload;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.easyexcel.CustomCellWriteHeightConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.easyexcel.CustomCellWriteWeightConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.BuildExcelFileResult;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ExcelSheetArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ExcelTemplateWriteHandler;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DBExcelUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.easyexcel.CustomCellWriteHandler;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/1/12 19:00 DB的文件服务,先临时copy之前模块的代码 //哪有临时，都是半永久ε(┬┬﹏┬┬)3
 * @Version 1.0
 */
@Service
@Slf4j
public class DBFileManager {

    @Autowired
    private NFileStorageService storageService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private I18NStringManager i18NStringManager;

    /**
     * 构建excel表格，并上传到临时文件，返回路径
     * 仅支持一次传输数据，单个sheet
     */
    public <R> Result<BuildExcelFileResult.Result> buildExcelFileResult(I18NStringManager i18NStringManager,
                                                                        String lang,
                                                                        String tenantId,
                                                                        BuildExcelFileResult.Arg<R> buildExcelArg) {
        String ea = buildExcelArg.getEa();
        if (StringUtils.isBlank(ea)) {
            ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(buildExcelArg.getTenantId()));
        }
        List<R> dataList = buildExcelArg.getDataList();
        if (CollectionUtils.isEmpty(dataList)) {
            return new Result<>(ResultCodeEnum.LIST_EMPTY);
        }
        //写excel
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        HorizontalCellStyleStrategy styleStrategy = buildExcelArg.getStyle();
        if (styleStrategy == null) {
            styleStrategy = DBExcelUtils.getDefaultStyle();
        }
        ExcelWriter excelWriter = null;
        try {
            final CustomCellWriteHandler customCellWriteHandler = new CustomCellWriteHandler(i18NStringManager, buildExcelArg.getTenantId(), lang);

            excelWriter = EasyExcel.write(outputStream, dataList.get(0).getClass())
                    .registerWriteHandler(styleStrategy)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(new ExcelTemplateWriteHandler(buildExcelArg.getFileName()))
                    .build();
            for (String sheetName : buildExcelArg.getSheetNames()) {
                WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).registerWriteHandler(customCellWriteHandler).build();
                excelWriter.write(dataList, writeSheet);
            }
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
        //上传文件系统
        String tnPath = this.uploadTnFile(ea, CommonConstant.SUPER_ADMIN_USER, "", outputStream.toByteArray());
        BuildExcelFileResult.Result result = new BuildExcelFileResult.Result();
        String fileName = StringUtils.appendIfMissing(buildExcelArg.getFileName(), ".xlsx");
        result.setFileName(fileName);
        result.setTnFilePath(tnPath);
        return new Result<>(result);
    }

    public Result<String> writeAndUploadExcel(String tenantId, String fileName, List<ExcelSheetArg> sheetArgs, String lang) {
        final Optional<String> illegalName = sheetArgs.stream()
                .map(ExcelSheetArg::getSheetName)
                .filter(this::checkExcelSheetNameIllegal)
                .findFirst();
        if (illegalName.isPresent()) {
            String firstIllegalName = illegalName.get();
            return Result.newErrorByI18N(ResultCodeEnum.EXCEL_SHEET_NAME_ILLEGAL,
                    String.format(ResultCodeEnum.EXCEL_SHEET_NAME_ILLEGAL.getErrMsg(),firstIllegalName),
                    I18NStringEnum.s1080.getI18nKey(), Lists.newArrayList(firstIllegalName));
        }

        ByteArrayOutputStream outputStream = createExcelStream(tenantId, lang, fileName, sheetArgs);

        String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
        final String npath = uploadTnFile(ea, CommonConstant.SUPER_ADMIN_USER, fileName, outputStream.toByteArray());
        return Result.newSuccess(npath);
    }

    private Pattern illegalSheetNamePattern = Pattern.compile("[:/\\\\*?]");
    private boolean checkExcelSheetNameIllegal(String name) {
//        确认输入的名称不多于31个字符。确认名称中不包含以下字符：：\/？*【或]。确认名称的第一个或者最后一个字符不能是单引号。确认工作表名称不为空。
        return StringUtils.isBlank(name) || name.startsWith("'") || name.endsWith("'") || illegalSheetNamePattern.matcher(name).find();
    }

    public ByteArrayOutputStream createExcelStream(String tenantId, String lang, final String fileName, final List<ExcelSheetArg> sheetArgs) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelWriter excelWriter = null;
        try {
            excelWriter = EasyExcelFactory.write(outputStream)
                    .registerWriteHandler(DBExcelUtils.getDefaultStyle())
                    .registerWriteHandler(new ExcelTemplateWriteHandler(fileName))
                    .registerWriteHandler(new CustomCellWriteWeightConfig(10))
                    .registerWriteHandler(new CustomCellWriteHeightConfig())
                    .build();
            int i = 0;
            for (ExcelSheetArg sheetArg : sheetArgs) {
                // LongestMatchColumnWidthStyleStrategy 使用了sheetNo，所以这里需要设置
                final ExcelWriterSheetBuilder sheetBuilder = EasyExcelFactory.writerSheet(sheetArg.getSheetName()).head(sheetArg.getClazz()).sheetNo(i++);
                if (CollectionUtils.isEmpty(sheetArg.getHeaders())) {
                    final List<List<String>> heads = Arrays.stream(FieldUtils.getAllFields(sheetArg.getClazz()))
                            .map(field -> field.getAnnotation(ExcelProperty.class))
                            .filter(Objects::nonNull)
                            .sorted(Comparator.comparingInt(ExcelProperty::index))
                            .map(annotation -> {
                                final String[] value = annotation.value();
                                final String key = value[0];
                                if (key.startsWith("erpdss.global")) {
                                    return i18NStringManager.get(key, lang, tenantId, key);
                                }
                                return key;
                            }).map(Lists::newArrayList)
                            .collect(Collectors.toList());
                    sheetArg.setHeaders(heads);
                }
                sheetBuilder.head(sheetArg.getHeaders());
                WriteSheet writeSheet = sheetBuilder.build();

                excelWriter.write(sheetArg.getDataList(), writeSheet);
            }
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
        return outputStream;
    }

    public String uploadTnFile(String ea, Integer userId, String fileName, byte[] bytes) {
        NTempFileUpload.Arg arg = new NTempFileUpload.Arg();
        arg.setBusiness(CommonConstant.ERP_SYNC_DATA_BUSINESS);
        if (!org.springframework.util.StringUtils.isEmpty(fileName)) {
            arg.setOriginName(fileName);
        }
        arg.setData(bytes);
        arg.setEa(ea);
        arg.setSourceUser("E." + userId);
        NTempFileUpload.Result result = storageService.nTempFileUpload(arg, ea);
        if (result != null && result.getTempFileName() != null) {
            return result.getTempFileName();
        } else {
            log.error("uploadTnFile get result null");
        }
        String tenantId = eieaConverter.enterpriseAccountToId(ea)+"";
        throw new ErpSyncDataException(ResultCodeEnum.FILE_UPLOAD_FAILED,tenantId);
    }

}
