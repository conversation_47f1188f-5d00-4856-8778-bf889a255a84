package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.CompositeIdExtend;
import com.fxiaoke.open.erpsyncdata.preprocess.model.IdFieldKey;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date: 14:51 2021/8/19
 * @Desc:
 */
@Component
@Slf4j
public class IdFieldKeyManager {
    @Autowired
    private ErpFieldManager erpFieldManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;

    @Cached(cacheType = CacheType.LOCAL, expire = 2, timeUnit = TimeUnit.MINUTES)
    public @NotNull IdFieldKey buildIdFieldKey(String tenantId, String dcId, String splitObjApiName, String realApiName) {
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dcId);
        return buildIdFieldKey(tenantId, dcId, connectInfo.getChannel(), splitObjApiName, realApiName);
    }


    /**
     * 优化缓存，统一走无channel的实现
     */
    private @NotNull IdFieldKey buildIdFieldKey(String tenantId, String dcId, ErpChannelEnum channel, String splitObjApiName, String realApiName) {
        IdFieldKey idFieldKey = new IdFieldKey();
        ErpObjectFieldEntity idField = erpFieldManager.findIdField(tenantId, splitObjApiName);
        if (idField == null) {
            log.warn("trace getIdField, ei:{}, erpobj:{} has not id field.", tenantId, splitObjApiName);
            throw new ErpSyncDataException(I18NStringEnum.s142.getText());
        }
        idFieldKey.setSplitObjApiName(splitObjApiName);
        idFieldKey.setIdFieldKey(idField.getFieldApiName());
        idFieldKey.setNumFieldKey(idField.getFieldApiName());
        idFieldKey.setFakeIdFieldKey(idField.getFieldApiName());
        idFieldKey.setCompositeIdExtend(CompositeIdExtend.getByIdField(idField.getFieldExtendValue()));
        if (Objects.requireNonNull(channel) == ErpChannelEnum.ERP_K3CLOUD) {
            idFieldKey.setIdFieldKey("id");
            ErpFieldExtendEntity idFieldExtend = erpFieldManager.findIdFieldExtendCache(tenantId, dcId, realApiName);
            boolean updateByNum = !StringUtils.equalsIgnoreCase(idFieldExtend.getViewCode(), "Id");
            idFieldKey.setUpdateByNum(updateByNum);
            if (updateByNum) {
                //不根据id更新时，主键必须设置为numField
                idFieldKey.setNumFieldKey(idFieldExtend.getViewCode());
            } else {
                ErpFieldExtendEntity erpFieldExtendEntity = erpFieldManager.queryNumFieldExtend(tenantId, dcId, realApiName);
                if (erpFieldExtendEntity != null) {
                    idFieldKey.setNumFieldKey(erpFieldExtendEntity.getQueryCode());
                } else {
                    idFieldKey.setNumFieldKey("id");
                }
            }
            return idFieldKey;
        }
        return idFieldKey;
    }
}
