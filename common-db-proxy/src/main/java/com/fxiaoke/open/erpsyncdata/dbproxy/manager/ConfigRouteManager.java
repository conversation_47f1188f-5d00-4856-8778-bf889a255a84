package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.Cached;
import com.facishare.paas.pod.mybatis.MyBatisRoutePolicy;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.HttpUrlUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.OkHttpUtils;
import com.fxiaoke.open.erpsyncdata.preprocess.model.HttpResponse;
import com.github.mybatis.tenant.TenantContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class ConfigRouteManager {
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private MyBatisRoutePolicy myBatisRoutePolicy;

    @Cached(expire = 10, timeUnit = TimeUnit.MINUTES)
    public Map<String, String> getTenant2Db() {
        Map<String, List<String>> dbRouteTenant = getDb2Tenants();
        Map<String, String> tenant2Db = new HashMap<>();
        dbRouteTenant.forEach((db, eis) -> {
            for (String ei : eis) {
                tenant2Db.put(ei, db);
            }
        });
        return tenant2Db;
    }

    public Map<String, List<String>> getDb2Tenants() {
        Map<String, List<String>> configRoute = tenantConfigurationManager.getConfigRouteTenant();
        Map<String, List<String>> jdbc2Tenant = new HashMap<>();
        configRoute.forEach((resourceId, eis) -> {
            TenantContext tenantContext = myBatisRoutePolicy.get(eis.get(0), false);
            String url = tenantContext.getUrl();
            //截断参数
            url = StrUtil.subAfter(url, "://", false);
            url = StrUtil.subBefore(url, "?", false);
            jdbc2Tenant.put(url, eis);
        });
        return jdbc2Tenant;
    }

    public Boolean configRoute(String tenantId,String resourceId) {
        Boolean result=postConfigRoute(tenantId,resourceId);
        if(result){
            Map<String, List<String>> configRoute=tenantConfigurationManager.getConfigRouteTenant();
            for(String sourceId:configRoute.keySet()){
                List<String> tenants=configRoute.get(sourceId);
                if(tenants.contains(tenantId)){//去掉
                    tenants.remove(tenantId);
                }
            }
            if(configRoute.containsKey(resourceId)){//加上
                configRoute.get(resourceId).add(tenantId);
            }else{
                configRoute.put(resourceId, Lists.newArrayList(tenantId));
            }
            result=tenantConfigurationManager.updateConfigRouteTenant(configRoute);
        }


        return result;
    }

    private Boolean postConfigRoute(String tenantId, String resourceId) {
        String url= HttpUrlUtils.buildConfigRouteUrl();
        /* 构造header */
        Map<String, String> headerMap = Maps.newHashMapWithExpectedSize(NumberUtils.INTEGER_ONE);
        headerMap.put("Content-type", "application/json;charset=utf-8");
        /* 构造body */
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(NumberUtils.INTEGER_TWO);
        paramMap.put("tenantId",tenantId);
        paramMap.put("resourceId",resourceId);
        HttpResponse response = OkHttpUtils.post(url, headerMap, JSONObject.toJSON(paramMap).toString());
        if(response!=null&&200==response.getCode()){
            JSONObject result=JSONObject.parseObject(response.getBody());
            if("OK".equals(result.getString("message"))){
                return true;
            }else{
                return false;
            }
        }else {
            log.warn("configRoute url:{},arg:{}, response={}",url,paramMap,response);
            return false;
        }
    }
}
