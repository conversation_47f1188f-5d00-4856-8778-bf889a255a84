package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldTypeContants;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncPloyDetailSnapshotDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisCacheManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class SyncPloyDetailSnapshotManager {
    @Autowired
    private SyncPloyDetailSnapshotDao syncPloyDetailSnapshotDao;
    @Autowired
    private RedisCacheManager redisCacheManager;
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    @Lazy
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private ErpSyncTimeManager erpSyncTimeManager;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private I18NStringManager i18NStringManager;

    @LogLevel(LogLevelEnum.TRACE)
    @Cached(expire = 60, cacheType = CacheType.LOCAL, localLimit = 5000)
    public Result2<Integer> hasSyncPloyDetailSnapshot(String sourceTenantId) {
        Integer count = syncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(sourceTenantId)).countBySourceTenantId(sourceTenantId, SyncPloyDetailStatusEnum.ENABLE.getStatus());
        return Result2.newSuccess(count);
    }

    @LogLevel(LogLevelEnum.TRACE)
    @Cached(expire = 30, cacheType = CacheType.LOCAL, localLimit = 5000)
    public Result2<String> getCrmMasterObjectApiName(String tenantId, String objectApiName) {
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        ObjectDescribe describeResult = objectDescribeService.getDescribe(headerObj, objectApiName).getData().getDescribe();
        for (FieldDescribe fieldDescribe : describeResult.getFields().values()) {
            if (fieldDescribe.getType().equals(FieldTypeContants.MASTER_DETAIL) && fieldDescribe.getIsActive()) {
                return Result2.newSuccess(fieldDescribe.getTargetApiName());
            }
        }
        return Result2.newSuccess();
    }

    /**
     * 判断crm数据是否有集成流
     *
     * @param sourceTenantId
     * @param sourceObjectApiName
     * @return
     */
    @LogLevel(LogLevelEnum.TRACE)
    @Cached(expire = 30, cacheType = CacheType.LOCAL, localLimit = 5000)
    public Result2<Boolean> crmDataIsMatchCondition(String sourceTenantId, String sourceObjectApiName) {
        Integer count = this.hasSyncPloyDetailSnapshot(sourceTenantId).getData();
        if (count != null && count > 0) {
            String sourceMasterObjectApiName = this.getCrmMasterObjectApiName(sourceTenantId, sourceObjectApiName).getData();
            if (!Strings.isNullOrEmpty(sourceMasterObjectApiName)) {
                sourceObjectApiName = sourceMasterObjectApiName;
            }
            List<SyncPloyDetailSnapshotEntity> list = syncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(sourceTenantId))
                    .listNewestBySourceTenantIdAndSrouceObjectApiName(sourceTenantId, sourceObjectApiName, SyncPloyDetailStatusEnum.ENABLE.getStatus());
            Boolean flag = list.size() > 0;
            return Result2.newSuccess(flag);
        } else {
            return Result2.newSuccess(false);
        }
    }

    @LogLevel(LogLevelEnum.TRACE)
    @Cached(expire = 60 * 5, cacheType = CacheType.LOCAL, localLimit = 2000)
    public SyncPloyDetailSnapshotEntity getEntryBySnapshotId(String tenantId, String snapshotId) {
        if (StringUtils.isBlank(snapshotId)) {
            return null;
        }
        SyncPloyDetailSnapshotEntity entity = syncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).get(tenantId, snapshotId);
        return entity;
    }

    public com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailSnapshotEntity getAdminEntryBySnapshotId(String tenantId, String snapshotId) {
        SyncPloyDetailSnapshotEntity entry = this.getEntryBySnapshotId(tenantId, snapshotId);
        if (entry == null) {
            return null;
        }
        return BeanUtil.deepCopy(entry, com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailSnapshotEntity.class);
    }


    public void setPloyDetailLastSyncTime(String tenantId, String ployDetailId, String sourceObjectApiName, long stopTime) {
        try {
            Long erpSyncTime = erpSyncTimeManager.getMinQueryMongoTime(tenantId, sourceObjectApiName);
//            crm没有ErpSyncTime
            Long lastSyncTime = Objects.isNull(erpSyncTime) ? stopTime : Math.min(erpSyncTime, stopTime);
            configCenterConfig.setPloyDetailLastSyncTime(tenantId, ployDetailId, lastSyncTime);
        } catch (Exception e) {
            log.warn("setPloyDetailLastSyncTime error, tenantId:{} ployDetailId:{}", tenantId, ployDetailId, e);
        }
    }


    @Cached(expire = 60, cacheType = CacheType.LOCAL, localLimit = 500)
    public List<SyncPloyDetailSnapshotEntity> getSyncPloyDetailSnapshotEntities(String tenantId, List<String> objApiNames, Integer status) {
        List<SyncPloyDetailSnapshotEntity> list = syncPloyDetailSnapshotDao.listBySourceTenantIdAndObjectApiNames(tenantId, objApiNames, status);
        return list;
    }
}
