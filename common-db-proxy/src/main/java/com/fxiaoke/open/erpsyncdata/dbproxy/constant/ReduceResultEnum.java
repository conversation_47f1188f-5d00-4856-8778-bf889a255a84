package com.fxiaoke.open.erpsyncdata.dbproxy.constant;

import lombok.Getter;

import java.util.List;
import java.util.function.BinaryOperator;

/**
 * <AUTHOR>
 * @date 2024/12/11 14:24:06
 */
@Getter
public enum ReduceResultEnum {
    Void((o1, o2) -> null),
    IntAdd(Integer::sum),
    LongAdd(Long::sum),
    ListAdd((BinaryOperator<List<Object>>) (l1, l2) -> {
        l1.addAll(l2);
        return l1;
    }),
    ;
    private final BinaryOperator<?> reduceFunc;

    <T> ReduceResultEnum(BinaryOperator<T> reduceFunc) {
        this.reduceFunc = reduceFunc;
    }
}
