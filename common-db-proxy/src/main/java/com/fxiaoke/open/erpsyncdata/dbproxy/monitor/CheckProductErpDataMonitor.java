package com.fxiaoke.open.erpsyncdata.dbproxy.monitor;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LocalDispatcherUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendAdminNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.github.jedis.support.MergeJedisCmd;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Tuple;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 检查企业在规定时间中是否有新数据产生
 *
 * 方案
 * 当有数据产生时,生成校验数据
 * 结束时间后5分钟检查有哪些数据没有生成
 *
 * 使用redis set类型处理
 * 开始前创建key(结束时间),过期时间为结束后1天
 * 产生数据后,key中加入数据(tenantId+apiName)
 * 结束时间后的1小时内做检查,检查后加入已完成检查标识
 *
 * <AUTHOR>
 * @date 2022/10/19 17:49:19
 */
@Component
@Slf4j
public class CheckProductErpDataMonitor implements InitializingBean, DisposableBean {

    @Autowired
    private MergeJedisCmd jedisSupport;

    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private I18NStringManager i18NStringManager;

    private LocalDispatcherUtil<String> localDispatcherUtil;

    private RLock lock;

    private LoadingCache<String, Map<ErpObject, TimeRang>> map = Caffeine.newBuilder()
            .initialCapacity(100)
            .maximumSize(1000)
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .build(this::initErpDataCheck);

    // redisKey前缀
    private static final String CHECK_ERP_PRODUCT_PREFIX = "ErpProductMonitor:";

    public static final String InitMember = createMember("-1", "");

    @Override
    public void afterPropertiesSet() {
        localDispatcherUtil = new LocalDispatcherUtil<>((key, list) -> {
            jedisSupport.sadd(key, list.stream().distinct().toArray(String[]::new));
        });

        lock = redissonClient.getLock("CheckProductErpDataMonitor");
    }

    /**
     * 需要将jvm中的数据上传
     */
    @Override
    public void destroy() throws Exception {
        localDispatcherUtil.processAll();
    }

    /**
     * 在保存数据前需要先创建key
     */
    public void checkInit(String tenantId) {
        if (sandboxTenant(tenantId)) {
            return;
        }

        final Map<ErpObject, TimeRang> erpDataTimeRangMap = map.get(tenantId);

        erpDataTimeRangMap.values().stream()
                .filter(TimeRang::needInit)
                .map(TimeRang::getEndTime)
                .distinct()
                .forEach(this::initKey);
    }

    /**
     * 有erp数据产生,保存到对应的redis中
     */
    public void erpDateProduct(final String tenantId, final List<ErpTempData> dataList) {
        final Map<ErpObject, TimeRang> erpDataTimeRangMap = map.get(tenantId);
        if (MapUtils.isEmpty(erpDataTimeRangMap)) {
            return;
        }

        dataList.stream()
                .map(data -> new ErpObject(tenantId, data.getObjApiName()))
                .distinct()
                .forEach(erpObject -> addErpObjectMember(erpObject, erpDataTimeRangMap.get(erpObject)));
    }

    public void checkErpDataProduct() {
        //防止并发
        if (!lock.tryLock()) {
            return;
        }

        try {
            final Map<ErpObject, TimeRang> collect = map.asMap().values().stream()
                    .flatMap(rangMap -> rangMap.entrySet().stream())
                    .filter(entry -> entry.getValue().needCheck())
                    .filter(entry -> needCheck(entry.getValue().getEndTime(), entry.getKey().getTenantId()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            // 所有企业已全部校验完成
            if (MapUtils.isEmpty(collect)) {
                return;
            }

            // 获取没有对象生成的数据
            final Map<ErpObject, TimeRang> notProductObject = collect.entrySet().stream()
                    .filter(entry -> !checkDataProduct(entry.getKey(), entry.getValue().getEndTime()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            if (MapUtils.isNotEmpty(notProductObject)) {
                // 添加到发消息列表
                addToNotifyList(notProductObject);
                // 发企信
                notifyCsmWarn();
            }

            // 保存处理好的企业,下次可以跳过校验
            cleanProcessTenantId(collect);
        } finally {
            lock.unlock();
        }
    }

    private void cleanProcessTenantId(final Map<ErpObject, TimeRang> collect) {
        // key:endTime
        final Map<Long, Set<ErpObject>> processedMap = collect.entrySet().stream()
                .collect(Collectors.groupingBy(entry -> entry.getValue().getEndTime(),
                        Collectors.mapping(Map.Entry::getKey, Collectors.toSet())));
        jedisSupport.pipeline(pipelineCmd ->
                processedMap.forEach((endtime, erpObjects) -> {
                    final String[] all = erpObjects.stream()
                            .map(erpObject -> createMember(erpObject.getTenantId(), "ALL"))
                            .distinct()
                            .toArray(String[]::new);
                    final String key = createKey(endtime);
                    // 企业已处理完成
                    pipelineCmd.sadd(key, all);
                    // 删除已处理的member,减少redis内存
                    pipelineCmd.srem(key, erpObjects.stream().map(this::createMember).toArray(String[]::new));
                }));
    }

    @Cached(expire = 30 * 60, cacheType = CacheType.LOCAL, postCondition = "!result")
    private boolean needCheck(final Long endTime, final String tenantId) {
        // key不存在说明已全部处理完了
        final String key = createKey(endTime);
        return jedisSupport.exists(key) &&
                // 该企业是否已校验过
                !jedisSupport.sismember(key, createMember(tenantId, "ALL"));
    }

    private boolean checkDataProduct(final ErpObject erpObject, final Long endTime) {
        return jedisSupport.sismember(createKey(endTime), createMember(erpObject));
    }

    private void addErpObjectMember(final ErpObject erpObject, final TimeRang timeRang) {
        if (Objects.nonNull(timeRang) && timeRang.isAcceptTime()) {
            localDispatcherUtil.produceDataCount1(createKey(timeRang.endTime), createMember(erpObject));
        }
    }

    private boolean sandboxTenant(String tenantId) {
        return eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId)).endsWith("_sandbox");
    }

    /**
     * cache用于防止多次创建redisKey
     */
    @Cached(expire = 30 * 60, cacheType = CacheType.LOCAL)
    private void initKey(Long endTime) {
        final String key = createKey(endTime);
        if (BooleanUtils.isTrue(jedisSupport.exists(key))) {
            return;
        }
        jedisSupport.sadd(key, InitMember);
        // 过期时间暂定为1天
        jedisSupport.expire(key, (endTime - System.currentTimeMillis() + TimeRang.oneDay) / 1000);
    }

    public static String createMember(final String tenantId, final String apiName) {
        return String.join(":", tenantId, apiName);
    }

    private String createMember(final ErpObject erpObject) {
        return createMember(erpObject.getTenantId(), erpObject.getApiName());
    }

    private String createKey(final Long endTime) {
        return CHECK_ERP_PRODUCT_PREFIX + DateFormatUtils.format(endTime, "MM/dd:HH/mm");
    }

    private Map<ErpObject, TimeRang> initErpDataCheck(final String tenantId) {
        // 格式:erpObjAPIName1:startTime1-endTime1,erpObjAPIName2:startTime2-endTime2,...
        final ErpTenantConfigurationEntity result = tenantConfigurationManager.findOne(tenantId, "0", "ALL", TenantConfigurationTypeEnum.ERP_TEMP_MONGO_NOT_GET_DATA_MONITOR.name());

        if ( Objects.isNull(result) || StringUtils.isBlank(result.getConfiguration())) {
            // 防止击穿
            return Collections.emptyMap();
        }

        final String configuration = result.getConfiguration();
        return Arrays.stream(configuration.split(","))
                .collect(Collectors.toMap(s -> {
                    final String apiName = StringUtils.substringBefore(s, ":");
                    return new ErpObject(tenantId, apiName);
                }, s -> {
                    final String time = StringUtils.substringAfter(s, ":");
                    final String[] times = time.split("-");
                    return new TimeRang(times[0], times[1]);
                }));
    }


    // ===================== notify manager =====================

    @Autowired
    private NotificationService notificationService;

    // 发送消息时间间隔
    private static final String SEND_MESSAGE_INTERVAL = String.valueOf(30 * 60 * 1000);

    // 通知次数上限为4
    private static final int sendMessageLimit = 4;

    private static final String SEND_MESSAGE_KEY = "erpProductAlarm";

    public static final String NOTIFY_DELIMITER = "/";

    /**
     * 加入到通知列表,使用zset,
     * key:写死
     * member:tenantId+apiName+时间(发消息需要)+发送间隔+通知上限
     * scope:createTime+sendTime(需要发送的时间+三位的发送企信次数)
     * 每次发送后scope加时间间隔 + 1,时间间隔暂时写死为半小时
     */
    public void addToNotifyList(final Map<ErpObject, TimeRang> collect) {
        final Map<String, Double> members = collect.entrySet().stream()
                .collect(Collectors.toMap(
                        entry -> createAlarmMember(entry.getKey(), entry.getValue()),
                        entry -> (double) entry.getValue().getEndTime() / 1000 * 1000
                ));

        jedisSupport.zadd(SEND_MESSAGE_KEY, members);
    }

    /**
     * 清除报警
     */
    public void cleanNotify(final String tenantId, final String objApiName) {
        final Map<ErpObject, TimeRang> erpObjectTimeRangMap = map.get(tenantId);
        if (MapUtils.isEmpty(erpObjectTimeRangMap)) {
            return;
        }

        if (StringUtils.isNotBlank(objApiName)) {
            final ErpObject erpObject = new ErpObject(tenantId, objApiName);
            final TimeRang timeRang = erpObjectTimeRangMap.get(erpObject);
            jedisSupport.zrem(SEND_MESSAGE_KEY, createAlarmMember(erpObject, timeRang));
        } else {
            erpObjectTimeRangMap.forEach((k, v) -> jedisSupport.zrem(SEND_MESSAGE_KEY, createAlarmMember(k, v)));
        }
    }

    public void notifyCsmWarn() {
        final Set<Tuple> allHandler = jedisSupport.zrangeByScoreWithScores(SEND_MESSAGE_KEY, 0, System.currentTimeMillis());
        if (CollectionUtils.isEmpty(allHandler)) {
            return;
        }

        // 需要添加时间的member
        Map<String, Integer> incMember = new HashMap<>();
        // 发送次数已达上限,可删除的数据
        Set<String> deleteMember = new HashSet<>();
        for (Tuple tuple : allHandler) {
            final String element = tuple.getElement();
            final String[] split = element.split(NOTIFY_DELIMITER);
            final Integer limit = Integer.valueOf(split[5]) - 1;
            if (limit <= Double.valueOf(tuple.getScore() % 1000).intValue()) {
                deleteMember.add(element);
            } else {
                final Integer interval = Integer.valueOf(split[4]);
                incMember.put(element, interval + 1);
            }

            sendMessage(split[0], split[1], split[2], split[3]);
        }

        if (CollectionUtils.isNotEmpty(deleteMember)) {
            jedisSupport.zrem(SEND_MESSAGE_KEY, deleteMember.toArray(new String[0]));
        }
        if (MapUtils.isNotEmpty(incMember)) {
            jedisSupport.pipeline(pipelineCmd -> incMember.forEach((member, inc) -> pipelineCmd.zincrby(SEND_MESSAGE_KEY, inc, member)));
        }
    }

    private void sendMessage(String tenantId, String apiName, String start, String end) {
        // 先不考虑多次通知的问题
        final String qixinMessage = createQixinMessage(tenantId, apiName, start, end);
        SendAdminNoticeArg arg = SendAdminNoticeArg.builder()
                .msg(qixinMessage)
                .msgTitle(i18NStringManager.getByEi(I18NStringEnum.s3729, tenantId))
                .tenantId(tenantId)
                .dcId("0")
                .sendSuperAdminIfNoSendTenantAdmin(true)
                .needFillPreDbName(false)
                .build()
                .addTraceInfo();
        notificationService.sendTenantAdminNotice(arg,
                AlarmRuleType.OTHER,
                AlarmRuleType.OTHER.getName(i18NStringManager,null,tenantId),
                AlarmType.OTHER,
                AlarmLevel.GENERAL);

        log.info("通知CSMErp数据没有变更 {}", qixinMessage);
    }

    private String createAlarmMember(final ErpObject erpObject, final TimeRang timeRang) {
        return String.join(NOTIFY_DELIMITER, erpObject.getTenantId(), erpObject.getApiName(), messageTimeFormat(timeRang.getStartTime()), messageTimeFormat(timeRang.getEndTime()), SEND_MESSAGE_INTERVAL, String.valueOf(sendMessageLimit));
    }

    private String createQixinMessage(final String tenantId, final String apiName, final String start, final String end) {
        return "tenantId:" + tenantId +
                " apiName:" + apiName +
                "\ntime:" + start +
                "~" + end;
    }

    private String messageTimeFormat(Long time) {
        return DateFormatUtils.format(time, "HH:mm");
    }

    @Data
    @AllArgsConstructor
    private static class ErpObject {
        private String tenantId;
        private String apiName;
        // private String dcId;
    }

    @Data
    private static class TimeRang {
        public static final Long oneDay = 1000L * 24 * 3600;

        // 过期时间暂时写死为2小时
        public static final long expire = 2 * 60 * 60 * 1000;

        private Long startTime;
        private Long endTime;
        /**
         * 当 now>expireTime 是,刷新TimeRang
         */
        private Long expireTime;

        public TimeRang(String start, String end) {
            startTime = getTime(start);
            endTime = getTime(end);
            // 当结束时间比开始时间小,说明跨天数了
            if (endTime <= startTime) {
                endTime += oneDay;
            }
            this.expireTime = endTime + expire;
        }

        private long getTime(final String time) {
            final String[] split = time.split(":");
            final Calendar instance = Calendar.getInstance();
            instance.set(Calendar.HOUR_OF_DAY, Integer.parseInt(split[0]));
            instance.set(Calendar.MINUTE, Integer.parseInt(split[1]));
            instance.set(Calendar.SECOND, 0);
            instance.set(Calendar.MILLISECOND, 0);
            return instance.getTimeInMillis();
        }

        /**
         * 检查是否该处理
         */
        public boolean isAcceptTime() {
            final long now = System.currentTimeMillis();
            if (now > expireTime) {
                reset();
            }

            return now > startTime && now < endTime;
        }

        private void reset() {
            expireTime = expireTime + oneDay;
            startTime = startTime + oneDay;
            endTime = endTime + oneDay;
        }

        /**
         * 提前半小时创建key
         */
        public boolean needInit() {
            final long now = System.currentTimeMillis();
            return now < startTime && startTime - now < 30 * 60 * 1000;
        }

        /**
         * 提前半小时创建key
         */
        public boolean needCheck() {
            final long now = System.currentTimeMillis();
            return now > endTime && now < expireTime;
        }
    }
}
