package com.fxiaoke.open.erpsyncdata.dbproxy.interceptor;

import cn.hutool.core.annotation.AnnotationUtil;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.common.util.LogUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ErasePasswordUtils;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ExceptionUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.support.AopUtils;

import java.lang.reflect.Method;

public class LogInterceptor {
    private Logger log;
    private int maxSize = 1000;

    public LogInterceptor() {
        this.log = LoggerFactory.getLogger(ConfigCenter.AOP_LOG_NAME);
    }

    @Deprecated
    public LogInterceptor(@Deprecated String logName) {
        new LogInterceptor();
    }

    public Object around(ProceedingJoinPoint point) throws Throwable {
        MethodSignature signature = (MethodSignature) point.getSignature();

        //检查类中方法的日志级别，跳过trace级别的日志打印
        Method method = signature.getMethod();
        LogLevelEnum logLevelEnum = LogLevelEnum.INFO;
        LogLevel logLevel = AnnotationUtil.getAnnotation(method, LogLevel.class);
        if (logLevel == null) {
            //支持类上面的注解
            logLevel = AnnotationUtil.getAnnotation(method.getDeclaringClass(), LogLevel.class);
        }
        if (logLevel != null) {
            logLevelEnum = logLevel.value();
        }

        String methodName = point.getSignature().getName();
        if (ConfigCenter.AOP_LOG_IGNORE_METHOD.contains(methodName)) {
            //忽略的方法，都不打印。
            return point.proceed();
        }
        String className = AopUtils.getTargetClass(point.getTarget()).getSimpleName();
        String fullClassName = point.getSignature().getDeclaringTypeName();
        long startTime = System.currentTimeMillis();
        try {
            Object result = point.proceed();

            long timeCost = System.currentTimeMillis() - startTime;
            boolean smallCostDao = (timeCost < 8000) && (fullClassName.contains("com.fxiaoke.open.erpsyncdata.dbproxy.dao") || fullClassName.contains("com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao"));
            if (smallCostDao) {//dao只打印8s以上的
                logLevelEnum = LogLevelEnum.DEBUG;
            }

            if (!LogUtil.needLog(log, logLevelEnum)) {
                return result;
            }

            if (LogUtil.needLog(log, logLevelEnum)) {
                String resultStr = "";
                try {
                    resultStr = (result != null) ? result.toString() : "";
                    if (resultStr.length() > maxSize) {
                        resultStr = resultStr.substring(0, maxSize);
                    }
                } catch (Exception e) {
                    resultStr = "result.toString() get exception:" + ExceptionUtil.getMessage(e);
                }
                //脱敏
                resultStr = ErasePasswordUtils.erase(resultStr);
                LogUtil.log(log, logLevelEnum, "{}-{}, cost:{} , args:{}, result:{}", className, methodName, timeCost, point.getArgs(), resultStr);
            }
            return result;
        } catch (ErpSyncDataException e) {
            log.info("{}-{}, cost:{} , args:{}, ErpSyncDataException:{}", className, methodName, (System.currentTimeMillis() - startTime), point.getArgs(), ExceptionUtil.getMessage(e));
            throw e;
        } catch (Throwable e) {
            log.warn("{}-{}, cost:{} , args:{}, Throwable:{}", className, methodName, (System.currentTimeMillis() - startTime), point.getArgs(), ExceptionUtil.getMessage(e), e);
            throw e;
        }
    }
}
