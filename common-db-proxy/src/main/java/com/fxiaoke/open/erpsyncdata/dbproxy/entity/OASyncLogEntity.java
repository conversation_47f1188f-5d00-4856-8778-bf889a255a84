package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * oa对接日志
 *
 * <AUTHOR>
 * @date 2021/3/17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "oa_sync_log")
public class OASyncLogEntity implements Serializable {
    private static final long serialVersionUID = 3739723344368834569L;

    @Id
    @Column(name = "id")
    private String id;

    /**
     * 企业id
     */
    @TenantID
    @Column(name = "tenant_id")
    private String tenantId;

    /**
     * 数据id
     */
    @Column(name = "data_id")
    private String dataId;

    /**
     * 数据id
     */
    @Column(name = "data_name")
    private String dataName;

    /**
     * 业务对象名
     */
    @Column(name = "object_name")
    private String objectName;
    /**
     * 数据
     */
    @Column(name = "data_json")
    private String dataJson;

    /**
     * 状态
     */
    @Column(name = "status")
    private String status;


    /**
     * 错误信息
     */
    @Column(name = "message")
    private String message;

    /**
     * 对象信息
     */
    @Column(name = "obj_api_name")
    private String objApiName;

    /**
     * 事件类型
     */
    @Column(name = "event_type")
    private String eventType;

    /**
     * 标题
     */
    @Column(name = "title")
    private String title;

    /**
     * 接收人
     */
    @Column(name = "receiver_id")
    private String receiverId;


    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Long updateTime;
}