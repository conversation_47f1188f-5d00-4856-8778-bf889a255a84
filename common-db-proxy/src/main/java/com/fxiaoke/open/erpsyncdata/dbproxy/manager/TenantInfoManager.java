package com.fxiaoke.open.erpsyncdata.dbproxy.manager;


import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.collection.CollUtil;
import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.BatchGetSimpleEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.crmrestapi.arg.v3.FindV3Arg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.result.v3.ObjectDataQueryListResult;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Service
@Slf4j
public class TenantInfoManager {
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    private static final TimedCache<String, Long> ei2ExpireTime = CacheUtil.newTimedCache(1000 * 60 * 60 * 2L);//两小时

    static {
        //定时清理，不然超时后不会自动清理。30分钟
        ei2ExpireTime.schedulePrune(TimeUnit.MINUTES.toMillis(30));
    }

    @LogLevel(LogLevelEnum.TRACE)
    public Long getExpireIntervalTimeByEi(String tenantId) {
        if (ei2ExpireTime.containsKey(tenantId)) {
            return ei2ExpireTime.get(tenantId, false);
        }
        Long expireTime = null;//默认30天
        try {
            Map<String, Long> result = tenantConfigurationManager.getTenantExpireTimeInterval();
            if (result.containsKey(tenantId)) {//特殊企业配置，
                expireTime = result.get(tenantId);
            } else {//按企业级别默认逻辑
                Map<String, SimpleEnterpriseData> ei2Ea = this.batchGetSimpleEnterprise(Lists.newArrayList(tenantId));
                if (ei2Ea.containsKey(tenantId) && ei2Ea.get(tenantId).getEnterpriseAccount().contains("sandbox")) {
                    expireTime = 1000 * 60 * 60 * 24 * 7L;//沙盒，默认7天
                    if (result.containsKey("sandbox")) {
                        expireTime = result.get("sandbox");
                    }
                } else {//非沙盒
                    List<String> level = this.getEnterpriseLevelValue(tenantId);
                    if (CollectionUtils.isNotEmpty(level)) {//多选字段，每个选项可以配置一个值，取大的
                        for (String l : level) {
                            if (result.containsKey(l)) {
                                if (expireTime == null) {
                                    expireTime = result.get(l);
                                } else {
                                    expireTime = result.get(l) > expireTime ? result.get(l) : expireTime;
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.info("getExpireIntervalTimeByEi e={}", e);
        }
        if (expireTime == null) {
            expireTime = 1000 * 60 * 60 * 24 * 30L;//默认30天
        }
        ei2ExpireTime.put(tenantId, expireTime);
        return expireTime;
    }

    @LogLevel(LogLevelEnum.TRACE)
    public Map<String, SimpleEnterpriseData> batchGetSimpleEnterprise(Collection<String> tenantIds) {
        Map<String, SimpleEnterpriseData> resultMap = new HashMap<>();
        try {
            BatchGetSimpleEnterpriseDataArg arg = new BatchGetSimpleEnterpriseDataArg();
            arg.setEnterpriseIds(tenantIds.stream().map(v -> Integer.valueOf(v)).collect(Collectors.toList()));
            BatchGetSimpleEnterpriseDataResult result = enterpriseEditionService.batchGetSimpleEnterpriseData(arg);
            if (result != null && result.getSimpleEnterpriseList() != null) {
                resultMap = result.getSimpleEnterpriseList().stream().collect(Collectors
                        .toMap(v -> String.valueOf(v.getEnterpriseId())
                                , u -> u, (i, o) -> i));
            }
        } catch (Exception e) {
            log.info("get enterprise info error", e);
        }
        return resultMap;
    }

    @LogLevel(LogLevelEnum.TRACE)
    public Map<String, List<String>> queryEnterpriseLevelValue(List<String> tenantIds) {
        Map<String, List<String>> tenantId2Level = new HashMap<>();
        List<List<String>> splitEis = CollUtil.split(tenantIds, 2000);
        for (List<String> eis : splitEis) {
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            HeaderObj headerObj = new HeaderObj(Integer.valueOf(ConfigCenter.FS_ENTERPRISE_ID), CrmConstants.SYSTEM_USER);
            searchTemplateQuery.addFilter(ConfigCenter.FS_ACCOUNT_TENANT_ID, eis, "IN");
            searchTemplateQuery.setPermissionType(0);
            searchTemplateQuery.setSearchSource("es");
            searchTemplateQuery.setLimit(2000);
            searchTemplateQuery.setNeedReturnCountNum(false);
            com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListResult> queryAccount;
            FindV3Arg findV3Arg = new FindV3Arg();
            findV3Arg.setSearchQueryInfo(GsonUtil.toJson(searchTemplateQuery));
            findV3Arg.setDescribeApiName(ObjectApiNameEnum.FS_ACCOUNT_OBJ.getObjApiName());
            //指定返回字段
            findV3Arg.setSelectFields(CollUtil.newArrayList(ConfigCenter.FS_ACCOUNT_ENTERPRISE_LEVEL_NEW, ConfigCenter.FS_ACCOUNT_TENANT_ID));
            //从客户对象获取
            try {
                queryAccount = objectDataServiceV3.queryList(headerObj, findV3Arg);
            } catch (Exception e) {
                return Maps.newHashMap();
            }
            log.info("query accountObj data arg:{},result:{}", searchTemplateQuery, queryAccount);
            if (queryAccount.isSuccess()
                    && queryAccount.getData() != null
                    && queryAccount.getData().getQueryResult() != null
                    && org.apache.commons.collections4.CollectionUtils.isNotEmpty(queryAccount.getData().getQueryResult().getDataList())) {
                List<com.fxiaoke.crmrestapi.common.data.ObjectData> dataList = queryAccount.getData().getQueryResult().getDataList();
                for (com.fxiaoke.crmrestapi.common.data.ObjectData objectData : dataList) {
                    Object levels = objectData.get(ConfigCenter.FS_ACCOUNT_ENTERPRISE_LEVEL_NEW);
                    if (levels instanceof List) {
                        //noinspection unchecked
                        tenantId2Level.putIfAbsent(objectData.getString(ConfigCenter.FS_ACCOUNT_TENANT_ID), (List<String>) levels);
                    }
                }
            }
        }
        return tenantId2Level;
    }

    @LogLevel(LogLevelEnum.TRACE)
    public List<String> getEnterpriseLevelValue(String tenantId) {
        List<String> level = Lists.newArrayList();
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(ConfigCenter.FS_ENTERPRISE_ID), CrmConstants.SYSTEM_USER);
        searchTemplateQuery.addFilter(ConfigCenter.FS_ACCOUNT_TENANT_ID, Lists.newArrayList(tenantId), "IN");
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setSearchSource("es");
        searchTemplateQuery.setLimit(2000);
        searchTemplateQuery.setNeedReturnCountNum(false);
        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListResult> queryAccount;
        FindV3Arg findV3Arg = new FindV3Arg();
        findV3Arg.setSearchQueryInfo(GsonUtil.toJson(searchTemplateQuery));
        findV3Arg.setDescribeApiName(ObjectApiNameEnum.FS_ACCOUNT_OBJ.getObjApiName());
        //指定返回字段
        findV3Arg.setSelectFields(CollUtil.newArrayList(ConfigCenter.FS_ACCOUNT_ENTERPRISE_LEVEL_NEW, ConfigCenter.FS_ACCOUNT_TENANT_ID));
        //从客户对象获取
        try {
            queryAccount = objectDataServiceV3.queryList(headerObj, findV3Arg);
        } catch (Exception e) {
            return level;
        }
        log.info("query accountObj data arg:{},result:{}", searchTemplateQuery, queryAccount);
        if (queryAccount.isSuccess()
                && queryAccount.getData() != null
                && queryAccount.getData().getQueryResult() != null
                && org.apache.commons.collections4.CollectionUtils.isNotEmpty(queryAccount.getData().getQueryResult().getDataList())) {
            List<com.fxiaoke.crmrestapi.common.data.ObjectData> dataList = queryAccount.getData().getQueryResult().getDataList();
            for (com.fxiaoke.crmrestapi.common.data.ObjectData objectData : dataList) {
                Object levels = objectData.get(ConfigCenter.FS_ACCOUNT_ENTERPRISE_LEVEL_NEW);
                if (levels != null && levels instanceof List) {
                    return (List<String>) levels;
                }
            }
        }
        return level;
    }
}
