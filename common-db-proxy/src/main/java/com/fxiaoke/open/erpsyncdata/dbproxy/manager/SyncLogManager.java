package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import cn.hutool.core.util.IdUtil;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHSyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.NumUtils;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SyncLogPageArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncLogBaseInfo;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Page;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

import static com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum.TEMP;

/**
 * 全链路日志
 * 请保持该Bean单例
 *
 * <AUTHOR> (^_−)☆
 * @date 2022/6/7
 */
@Slf4j
@Service
public class SyncLogManager {
    @Autowired
    private CHSyncLogManager chSyncLogManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private ConfigCenterConfig configCenterConfig;

    private Set<String> filterSyncLogApiNamesSet = new HashSet<>();

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("erp-sync-data-all", config -> {
            String[] filterSyncLogApiNamesArray = config.get("filterSyncLogApiNames", "SAL_MATERIALGROUP").split(",");
            filterSyncLogApiNamesSet.clear();
            for (String apiName : filterSyncLogApiNamesArray) {
                filterSyncLogApiNamesSet.add(apiName);
            }
        });
    }

    /**
     * 失败时打印日志
     */
    private void insert(String tenantId, SyncLog syncLog) {
        //目前所有企业共用一个队列
        try {
            if(filterSyncLog(tenantId, syncLog)) {
                return;
            }
            chSyncLogManager.insert(tenantId, syncLog);
        } catch (Exception e) {
            log.info("save sync log error,tenantId:{} syncLog:{}", tenantId, syncLog);
        }
    }

    /**
     * 初始化id，并保存到线程变量
     *
     * @param tenantId
     * @param realObjApiName
     * @return
     */
    public String initLogId(String tenantId, String realObjApiName) {
        String logId = getInitLogId(tenantId, realObjApiName);
        LogIdUtil.resetBaseLog(realObjApiName, logId, tenantConfigurationManager, eieaConverter);
        return logId;
    }
    public String resetBaseLog(String logId, String realObjApiName) {
        LogIdUtil.resetBaseLog(realObjApiName, logId, tenantConfigurationManager, eieaConverter);
        return logId;
    }


    /**
     * 仅生成id，不保存到线程变量
     *
     * @param tenantId
     * @param apiName
     * @return
     */
    @LogLevel(LogLevelEnum.TRACE)
    public String getInitLogId(String tenantId, String apiName) {
        String id;
        try {
            tenantId=eieaConverter.enterpriseIdToAccount(Integer.valueOf(tenantId));
            id = NumUtils.longTo62Str(IdUtil.getSnowflake().nextId());
        }catch (Exception e){
            id = com.fxiaoke.api.IdGenerator.get();
            log.info("snow flake id error,use mongoid,id:{}",id);
        }
        String logId = LogIdUtil.buildRootLogId(tenantId, apiName, id);
        return logId;
    }


    /**
     * 日志保存到object类型，entityData字段
     * 注意需要存在codec或补充
     * 异步
     *
     * @param tenantId
     * @param type
     * @param logObj
     */
    @LogLevel(LogLevelEnum.TRACE)
    public void saveErpTempLog(String tenantId, SyncLogTypeEnum type,Integer status,String logId, ErpTempData logObj) {
        try {
            saveLogWithLogId(tenantId, type,status, logId, logObj);
        } catch (Exception e) {
            log.error("save syncLog error", e);
        }
    }

    /**
     * 日志保存到String类型，data字段
     * 异步
     *
     * @param tenantId
     * @param type
     * @param logObjs
     */
    public void saveLog(String tenantId, SyncLogTypeEnum type,Integer status, Object... logObjs) {
        try {
            saveLogWithLogId(tenantId, type, status,LogIdUtil.get(), null, logObjs);
        } catch (Exception e) {
            log.error("save syncLog error", e);
        }
    }

    /**
     * 日志保存到String类型，data字段
     * 异步
     *
     * @param tenantId
     * @param type
     * @param logObjs
     */
    public void saveLogWithLogId(String tenantId, SyncLogTypeEnum type,Integer status, String logId, ErpTempData erpTempData,Object... logObjs) {
        try {
            if(ObjectUtils.isEmpty(logId)){
                logId=LogIdUtil.get();
            }
            syncSaveLogNoCatch(tenantId, type,status, logId, erpTempData, logObjs);
        } catch (Exception e) {
            log.error("save syncLog error", e);
        }
    }

    /**
     * 过滤日志
     * @param tenantId
     * @param syncLog
     * @return true: 这条日志不上报 biz_log_erp_sync_log_dist
     */
    private boolean filterSyncLog(String tenantId, SyncLog syncLog) {
        try {            //remark 包含  "数据没变化，不做同步", 则按照realObjApiName字段过滤
            if (TEMP==syncLog.getType() && syncLog.getErpTempData().getRemark().contains(I18NStringEnum.s643.getI18nValue())) {
                if (filterSyncLogApiNamesSet.contains(syncLog.getErpTempData().getObjApiName())) {
                    return true;
                } else {
                    return false;
                }
            }
        }catch (Exception e){
        }
        return false;
    }

    /**
     * 请保存操作在这个方法补充，统一保存入口，
     * 异步处理、分级储存、限速等统一调整该方法。
     */
    private void syncSaveLogNoCatch(String tenantId, SyncLogTypeEnum type, Integer status, String logId, ErpTempData erpTempData, Object... logObjs) {
        SyncLogBaseInfo baseLog = LogIdUtil.getBaseLogNoCreate();
        if (baseLog == null) {
            log.error("log id not init here,type:{}", type);
            return;
        }
        SyncLog syncLog = SyncLog.initSyncLog(
                logId,
                baseLog.getRealObjApiName(),
                baseLog.getSourceObjApiName(),
                baseLog.getStreamId(),
                type,
                status,
                logObjs
        );
        if (erpTempData != null) {
            syncLog.setErpTempData(erpTempData);
        }
        //先放到队列
        insert(tenantId, syncLog);
    }

    public Page<SyncLog> pageByFilters(SyncLogPageArg arg) {
        Page<SyncLog> pageResult = new Page<>();
        List<SyncLog> syncLogs = chSyncLogManager.pageByFilters(arg);
        long totalNum;
        if (syncLogs.size() + arg.getOffset() > 1000) {
            //超过1000时返回总数为实际查询到的总数
            totalNum = syncLogs.size() + arg.getOffset();
        } else {
            //总数限制查询1000条
            totalNum = countByFilters(arg);
        }
        pageResult.setData(syncLogs);
        pageResult.setTotalNum(totalNum);
        pageResult.setHasNext(syncLogs.size() == arg.getLimit());
        return pageResult;
    }

    public long countByFilters(SyncLogPageArg arg) {
        //总数限制查询1000条
        return chSyncLogManager.countByFilters(arg, 1000);
    }

    public List<String> getAllLogId(SyncLogPageArg arg) {
        return chSyncLogManager.getAllLogId(arg);
    }

    public Map<String, Integer> getCountByLogIds(final SyncLogPageArg countPageArg, final List<SyncLogTypeEnum> types) {
        return chSyncLogManager.getCountByLogIds(countPageArg, types);
    }


    /**
     * 会获取父的log,和子的Log
     *
     * @param tenantId
     * @param logId
     * @return
     */
    public List<SyncLog> listByLogId(String tenantId, String logId,Long startLogTime, Long endLogTime) {
        if(startLogTime==null){
            startLogTime=System.currentTimeMillis()-1000*60*60*24*7;
        }
        if(endLogTime==null){
            endLogTime=System.currentTimeMillis();
        }
        List<String> logIdLine = LogIdUtil.listLogIdLine(tenantId,logId);
        List<SyncLog> result = chSyncLogManager.listByLogIds(tenantId, logIdLine,startLogTime,endLogTime);
        //查询子级logid使用字符串的范围查询，由于.用来区分级别，以此区分，z char码值为122，{ char码值为123
        String gt = logId + ".";
        String lt = logId + ".{";
        List<SyncLog> children = chSyncLogManager.listBetweenLogId(tenantId, gt, lt,startLogTime,endLogTime);
        result.addAll(children);
        return result;
    }

    /**
     * 会获取父的log
     *
     * @param tenantId
     * @param logId
     * @return
     */
    public List<SyncLog> listParentByLogId(String tenantId, String logId,Long startLogTime, Long endLogTime) {
        if(startLogTime==null){
            startLogTime=System.currentTimeMillis()-1000*60*60*24*7;
        }
        if(endLogTime==null){
            endLogTime=System.currentTimeMillis();
        }
        List<String> logIdLine = LogIdUtil.listLogIdLine(tenantId,logId);
        List<SyncLog> result = chSyncLogManager.listByLogIds(tenantId, logIdLine,startLogTime,endLogTime);
        return result;
    }

    public SyncLog getLogById(String tenantId, ObjectId id, String type) {
        SyncLog syncLogs = chSyncLogManager.getById(tenantId, id, type);
        return syncLogs;
    }

}
