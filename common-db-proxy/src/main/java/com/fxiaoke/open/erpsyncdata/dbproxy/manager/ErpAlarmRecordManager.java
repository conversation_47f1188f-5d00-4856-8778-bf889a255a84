//package com.fxiaoke.open.erpsyncdata.dbproxy.manager;
//
//import com.facishare.organization.api.service.EmployeeProviderService;
//import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
//import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
//import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
//import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
//import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
//import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
//import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
//import com.fxiaoke.open.erpsyncdata.dbproxy.model.DataCenterModel;
//import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpAlarmRecordModel;
//import com.fxiaoke.open.erpsyncdata.dbproxy.model.PloyDetailModel;
//import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpAlarmRecordDao;
//import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpAlarmRecordEntity;
//import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
//import com.fxiaoke.open.erpsyncdata.dbproxy.util.EmployeeProviderServiceUtils;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.commons.lang3.time.DateFormatUtils;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.*;
//
///**
// * 告警记录管理器
// *
// * <AUTHOR>
// * @date 2023-11-23
// */
//@Component
//@Slf4j
//public class ErpAlarmRecordManager {
//    @Autowired
//    private ErpAlarmRecordDao erpAlarmRecordDao;
//    @Autowired
//    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
//    @Autowired
//    private ErpConnectInfoManager erpConnectInfoManager;
//    @Autowired
//    private EmployeeProviderService employeeProviderService;
//    @Autowired
//    private ErpConnectInfoDao erpConnectInfoDao;
//
//    public void insert(String tenantId,
//                       String dataCenterId,
//                       String ployDetailId,
//                       AlarmRuleType alarmRuleType,
//                       String alarmRuleName,
//                       AlarmType alarmType,
//                       AlarmLevel alarmLevel,
//                       String msg,
//                       List<Integer> userIdList) {
//        erpAlarmRecordDao.insert(tenantId,
//                dataCenterId,
//                ployDetailId,
//                alarmRuleType,
//                alarmRuleName,
//                alarmType,
//                alarmLevel,
//                System.currentTimeMillis(),
//                msg,
//                userIdList);
//    }
//
//    public ErpAlarmRecordModel getDataListByPage(String tenantId,
//                                                 String dcId,
//                                                 List<String> ployDetailIdList,
//                                                 AlarmType alarmType,
//                                                 AlarmLevel alarmLevel,
//                                                 Date startTime,
//                                                 Date endTime,
//                                                 int pageSize,
//                                                 int page) {
//        ErpAlarmRecordModel model = erpAlarmRecordDao.getDataListByPage(tenantId,
//                dcId,
//                ployDetailIdList,
//                alarmType,
//                alarmLevel,
//                startTime,
//                endTime,
//                pageSize,
//                page);
//
//        model.setDataList(new ArrayList<>());
//
//        if (CollectionUtils.isNotEmpty(model.getEntityList())) {
//            String format = "yyyy-MM-dd HH:mm:ss";
//            for (ErpAlarmRecordEntity alarmRecordEntity : model.getEntityList()) {
//                ErpAlarmRecordModel.Entry entry = new ErpAlarmRecordModel.Entry();
//                BeanUtils.copyProperties(alarmRecordEntity, entry);
//                entry.setTime(DateFormatUtils.format(alarmRecordEntity.getTime(), format));
//
//
//                if (StringUtils.isNotEmpty(alarmRecordEntity.getDataCenterId())
//                        && !StringUtils.startsWith(alarmRecordEntity.getDataCenterId(), "-")) {
//                    ErpConnectInfoEntity connectInfoEntity = erpConnectInfoManager.getByIdAndTenantId(tenantId,
//                            entry.getDataCenterId());
//                    if (connectInfoEntity != null) {
//                        entry.setDataCenterName(connectInfoEntity.getDataCenterName());
//                    } else {
//                        continue;
//                    }
//                }
//
//                SyncPloyDetailEntity ployDetailEntity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(DataBaseBatchIndexUtil.notTenantId))
//                        .getById(tenantId, alarmRecordEntity.getPloyDetailId());
//                if (ployDetailEntity != null) {
//                    PloyDetailModel ployDetailModel = new PloyDetailModel(ployDetailEntity.getId(),
//                            ployDetailEntity.getIntegrationStreamName());
//                    entry.setPloyDetailModel(ployDetailModel);
//                }
//
//                entry.setUserList(new ArrayList<>());
//                if (CollectionUtils.isNotEmpty(alarmRecordEntity.getUserIdList())) {
//                    entry.setUserList(EmployeeProviderServiceUtils.batchGetEmployeeDto(tenantId,alarmRecordEntity.getUserIdList(),employeeProviderService));
//                }
//
//                model.getDataList().add(entry);
//            }
//        }
//        model.getEntityList().clear();
//        if (CollectionUtils.isNotEmpty(model.getDataList())) {
//            model.setTotal(model.getDataList().size());
//        } else {
//            model.setTotal(0);
//        }
//        return model;
//    }
//
//    public List<DataCenterModel> getDcList(String tenantId) {
//        List<String> dcIdList = erpAlarmRecordDao.getDataListByDcId(tenantId);
//        if (CollectionUtils.isEmpty(dcIdList)) return new ArrayList<>();
//
//        List<ErpConnectInfoEntity> entityList = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
//                .listByIds(dcIdList);
//        Map<String, String> map = new LinkedHashMap<>();
//        for (ErpConnectInfoEntity entity : entityList) {
//            if (!StringUtils.equalsIgnoreCase(entity.getTenantId(), tenantId)) continue;
//            if (map.containsKey(entity.getId())) continue;
//            map.put(entity.getId(), entity.getDataCenterName());
//        }
//        List<DataCenterModel> dataList = new ArrayList<>();
//        for (String key : map.keySet()) {
//            dataList.add(new DataCenterModel(key, map.get(key)));
//        }
//        return dataList;
//    }
//
//    public long clearData() {
//        long count = erpAlarmRecordDao.deleteMany(null);
//        return count;
//    }
//}
