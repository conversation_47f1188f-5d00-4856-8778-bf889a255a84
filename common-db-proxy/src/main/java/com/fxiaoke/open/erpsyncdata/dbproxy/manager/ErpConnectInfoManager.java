package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.facishare.uc.api.model.fscore.SimpleEnterprise;
import com.facishare.uc.api.model.fscore.arg.GetSimpleEnterpriseArg;
import com.facishare.uc.api.model.fscore.result.GetSimpleEnterpriseResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.BaseConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SystemParams;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.Connector;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ConnectInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.DataCenterInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.util.AllConnectorUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/4/6
 */
@Component
@Slf4j
public class ErpConnectInfoManager {

    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager;

    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;

    /**
     * 获取企业所有数据中心连接信息
     *
     * @param tenantId
     * @return
     */
    @Cached(expire = 30, cacheType = CacheType.LOCAL)
    public List<ErpConnectInfoEntity> listByTenantId(String tenantId) {
        final List<String> allErpDcIds = erpConnectInfoDao.listErpDcIdByTenantId(tenantId);
        return allErpDcIds.stream()
                .map(id -> erpConnectInfoManager.getByIdAndTenantId(tenantId, id))
                .collect(Collectors.toList());
    }

    public ErpConnectInfoEntity getFirstNumErpDcByTenantId(String tenantId) {
        return erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getFirstNumErpDcByTenantId(tenantId);
    }

    public ErpConnectInfoEntity getOrCreateCrmDc(String tenantId) {
        return erpConnectInfoManager.getOrCreateCrmDc(tenantId, null);
    }

    @LogLevel(LogLevelEnum.TRACE)
    @Cached(name = "erpConnectInfo.", expire = 24*60*60, cacheType = CacheType.BOTH, syncLocal = true,key = "args[0]+'.'+args[1]")
    public ErpConnectInfoEntity getByIdAndTenantId(String tenantId,String id) {
        return erpConnectInfoDao.getByIdAndTenantId(tenantId,id);
    }

    @CacheInvalidate(name = "erpConnectInfo.", key = "args[0]+'.'+args[1].id")
    @CacheInvalidate(name = "SystemParams.", key = "args[0]+'.'+args[1].id")
    @CacheInvalidate(name = "crmConnectInfo.", key = "#tenantId", condition = "#entity.channel.name()=='CRM'")
    public int updateById(String tenantId, ErpConnectInfoEntity entity) {
        return erpConnectInfoDao.updateById(entity);
    }

    public void deleteByTenantId(String tenantId) {
        int count = erpConnectInfoDao
                .setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .deleteByTenantId(tenantId);
        log.info("ErpConnectInfoManager.deleteByTenantId,count={}",count);
    }

    @Cached(name = "crmConnectInfo.", expire = 24*60*60, cacheType = CacheType.BOTH, syncLocal = true,key = "#tenantId", postCondition = "#result!=null")
    public ErpConnectInfoEntity getOrCreateCrmDc(String tenantId, String lang) {
        ErpConnectInfoEntity erpConnectInfoEntity = erpConnectInfoDao.getCRMConnectInfo(tenantId, ErpChannelEnum.CRM.name());
        if (Objects.nonNull(erpConnectInfoEntity)) {
            return erpConnectInfoEntity;
        }

        ErpConnectInfoEntity crmConnectInfoEntity = new ErpConnectInfoEntity();
        crmConnectInfoEntity.setId(IdGenerator.get());
        crmConnectInfoEntity.setTenantId(tenantId);
        crmConnectInfoEntity.setEnterpriseName(getEnterpriseName(tenantId));
        crmConnectInfoEntity.setChannel(ErpChannelEnum.CRM);
        crmConnectInfoEntity.setDataCenterName(i18NStringManager.get(I18NStringEnum.s417, lang, tenantId));
        crmConnectInfoEntity.setCreateTime(System.currentTimeMillis());
        crmConnectInfoEntity.setUpdateTime(crmConnectInfoEntity.getCreateTime());
        crmConnectInfoEntity.setNumber(800);
        erpConnectInfoDao.insertIgnore(crmConnectInfoEntity);
        return erpConnectInfoDao.getByIdAndTenantId(tenantId, crmConnectInfoEntity.getId());
    }

    private String getEnterpriseName(String tenantId) {
        GetSimpleEnterpriseArg getSimpleEnterpriseArg = new GetSimpleEnterpriseArg();
        getSimpleEnterpriseArg.setEnterpriseId(Integer.parseInt(tenantId));
        GetSimpleEnterpriseResult simpleEnterpriseResult = enterpriseEditionService.getSimpleEnterprise(getSimpleEnterpriseArg);
        SimpleEnterprise simpleEnterprise = simpleEnterpriseResult.getSimpleEnterprise();
        if (Objects.isNull(simpleEnterprise) || Objects.isNull(simpleEnterprise.getEnterpriseName())) {
            return tenantId;
        }
        return simpleEnterprise.getEnterpriseName();
    }

    //对接系统名称，在通用连接器的链接页面上来说，除了老客户，新客户是必填字段。
    @Cached(expire = 24*60*60, cacheType = CacheType.LOCAL, localLimit = 1000)
    public String getOutSystemName(String tenantId, String snapshotId) {
        //没有填对接的具体系统名字的，统一为 "ERP", 不叫外部ERP接口，是为了避免多语翻译
        String outSystemName = "ERP";
        try {
            SyncPloyDetailSnapshotEntity syncPloyEntity = syncPloyDetailSnapshotManager.getEntryBySnapshotId(tenantId,snapshotId);
            String datacenterId = syncPloyEntity.getSyncPloyDetailData().getDestDataCenterId();
            List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listByTenantId(tenantId);
            for (ErpConnectInfoEntity entity : erpConnectInfoEntities) {
                if (!entity.getId().equals(datacenterId)) {
                    continue;
                }
                switch (entity.getChannel()) {
                    case STANDARD_CHANNEL:
                        ConnectInfoResult.ConnectParams connectParams =
                                ErpChannelEnum.STANDARD_CHANNEL.getNewConnectParam(entity.getConnectParams());
                        if (Objects.nonNull(connectParams.getStandard())) {
                            outSystemName = connectParams.getStandard().getSystemName();
                        }
                        break;
                    case ERP_DB_PROXY:
                        outSystemName = "DB";
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            log.error("tenantId:{} get getOutSystemName exception ", tenantId, e);
        }
        return outSystemName;
    }

    @Cached(cacheType = CacheType.BOTH,syncLocal = true, name = "SystemParams.", expire = 30, key = "args[0]+'.'+args[1]")
    public SystemParams getSystemParams(String tenantId, String dcId) {
        //这里不会走缓存
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dcId);
        BaseConnectParam connectParam = connectInfo.getChannel().getConnectParam(connectInfo.getConnectParams());
        return connectParam.getSystemParams();
    }

    public List<Connector> listConnectorsByTenantId(String tenantId) {
        List<ErpConnectInfoEntity> entities = erpConnectInfoManager.listByTenantId(tenantId);
        List<Connector> connectors = entities.stream().map(v -> AllConnectorUtil.getByChannelAndConnectParam(v.getChannel(), v.getConnectParams())).collect(Collectors.toList());
        return connectors;
    }

    /**
     * 请统一调用该方法，别再copy！！！！！！！！！！！！！！！！！！！！！！！！！！！！
     * ！！！！！！！！！！！！！！！
     */
    public DataCenterInfoResult entity2DcInfo(ErpConnectInfoEntity erpConnectInfoEntity) {
        DataCenterInfoResult dcResult = new DataCenterInfoResult();
        BeanUtils.copyProperties(erpConnectInfoEntity, dcResult);
        if (ErpChannelEnum.CRM.equals(erpConnectInfoEntity.getChannel()) || StringUtils.isNotBlank(erpConnectInfoEntity.getConnectParams())) {
            dcResult.setHasConnect(true);
        }
        if (ErpChannelEnum.CRM.equals(erpConnectInfoEntity.getChannel())){
            dcResult.setHasInited(true);
            dcResult.setExpired(false);
            dcResult.setHasPurchased(true);
        }
        //尝试从连接器信息获取connectorName，有则复制到dcInfo上
        String connectStr = erpConnectInfoEntity.getConnectParams();
        ErpChannelEnum channel = erpConnectInfoEntity.getChannel();
        if (StringUtils.isNotEmpty(connectStr)) {
            final BaseConnectParam connectParam = channel.getConnectParam(connectStr);
            dcResult.setConnectorName(connectParam.getConnectorName());
            dcResult.setIconUrl(connectParam.getIconUrl());
            Connector connector = AllConnectorUtil.getByChannelAndConnectParam(channel, connectStr);
            dcResult.setConnectorKey(connector.getKey());
        }

        return dcResult;
    }

    public int findNextNum(String tenantId, Integer connectorId) {
        int minNum = connectorId * 100;
        List<Integer> existNums = erpConnectInfoDao.existNum(tenantId, connectorId);
        Integer nextNum = minNum;
        for (int i = 0; i < 100; i++) {
            if (!existNums.contains(nextNum)) {
                break;
            }
            nextNum++;
        }
        if (nextNum >= minNum + 100) {
            //越界
            throw new ErpSyncDataException("Connector exceeds limit");
        }
        return nextNum;
    }
}
