package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ObjApiName;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.ListStringData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 11:27 2021/8/12
 * @Desc:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ObjApiName("objApiName")
@Table(name = "erp_history_data_task")
public class ErpHistoryDataTaskEntity implements Serializable {
    private static final long serialVersionUID = 3739723344368836569L;

    @Id
    @Column(name = "id")
    private String id;

    /**
     * 企业id
     */
    @TenantID
    @Column(name = "tenant_id")
    private String tenantId;

    /**
     * 任务类型:1.按时间，2.按ids
     * @see com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpHistoryDataTaskTypeEnum
     */
    @TenantID
    @Column(name = "task_type")
    private Integer taskType;

    /**
     *任务编码
     */
    @Column(name = "task_num")
    private String taskNum;

    /**
     * 任务名称
     */
    @Column(name = "task_name")
    private String taskName;


    /**
     * 对象apiName
     */
    @Column(name = "obj_api_name")
    private String objApiName;

    /**
     * 真实对象apiName
     */
    @Column(name = "real_obj_api_name")
    private String realObjApiName;

    /**
     * 数据ids
     */
    @Column(name = "data_ids")
    private String dataIds;

    /**
     * K3自定义条件
     */
    @Column(name = "filter_string")
    public String filterString;

    /**
     * 开始时间
     */
    @Column(name = "start_time")
    private Long startTime;

    /**
     * 最后轮询开始时间
     */
    @Column(name = "last_query_start_time")
    private Long lastQueryStartTime;

    /**
     * 结束时间
     */
    @Column(name = "end_time")
    private Long endTime;

    /**
     * 是否中断
     */
    @Column(name = "need_stop")
    private Boolean needStop;

    /**
     * 每次获取条数
     */
    @Column(name = "limit")
    private Long limit;

    /**
     * 偏移量
     */
    @Column(name = "offset")
    private Long offset;

    /**
     * 任务状态
     * @see com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpHistoryDataTaskStatusEnum
     */
    @Column(name = "task_status")
    private Integer taskStatus;

    /**
     * 累计轮询数据
     */
    @Column(name = "total_data_size")
    private Long totalDataSize;

    /**
     * 累计耗时
     */
    @Column(name = "total_cost_time")
    private Long totalCostTime;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * traceId
     */
    @Column(name = "trace_id")
    private String traceId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Long updateTime;
    /**
     * 创建人
     */
    @Column(name = "creator")
    private Integer creator;
    /**
     * 开始执行的时间
     */
    @Column(name = "execute_time")
    private Long executeTime;

    /**
     * 相关的集成流id
     */
    @Column(name = "related_ploy_detail_id")
    private ListStringData relatedPloyDetailId;


    /**
     * 数据中心id
     */
    @Column(name = "data_center_id")
    private String dataCenterId;

    /**
     * 分片间隔（ms）
     */
    @Column(name = "splitting_interval_ms")
    private Long splittingIntervalMs;

    /**
     * 数据优先级，默认空时，是50，高于增量为10，低于增量为90
     */
    @Column(name = "priority")
    private Integer priority;
}