package com.fxiaoke.open.erpsyncdata.dbproxy.constant;

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/5/20 19:42
 * 重试的数据类型 ,还是需要定义重试的service-method
 * @desc
 */
@AllArgsConstructor
@Getter
public enum RetryDataEnum  {

    PAAS_ORIGIN_DATA_RETRY_MQ("PAAS_ORIGIN_DATA_RETRY_MQ","paasmq的数据重试","task",List.class,"com.fxiaoke.open.erpsyncdata.crmmq.consumer.PaasObjectDataMqConsumer.batchProcessMessage", Integer.class),

    PAAS_DATA_RETRY_MQ("PAAS_DATA_RETRY_MQ","paasmq的数据重试,已经是经过batchprocess,在发送mq的时候失败","all",List.class,"com.fxiaoke.open.erpsyncdata.preprocess.service.AllModelDubboService.batchSendEventData2DispatcherMqByContext",Result2.class),

    ERP_POLLING_INTERFACE_MQ("ERP_POLLING_INTERFACE","erp的数据轮询mq","",List.class,"com.fxiaoke.open.erpsyncdata.preprocess.service.AllModelDubboService.syncDataMain",Result2.class),

    POLLING_TEMP_DATA_ARG("POLLING_TEMP_DATA_ARG","临时库轮询失败","",List.class,"com.fxiaoke.open.erpsyncdata.preprocess.service.ProbeErpDataService.retryPollingTempFail",Result2.class),

    RETRY_BULK_UPDATE_BY_SOURCE("RETRY_BULK_UPDATE_BY_SOURCE","重试中间表的插入","",List.class,"com.fxiaoke.open.erpsyncdata.writer.manager.SyncWriteMainManager.bulkUpdateBySource",Result2.class),

    RETRY_BULK_UPDATE_BY_SYNC_DATA_ID("RETRY_BULK_UPDATE_BY_SYNC_DATA_ID","重试中间表的插入","",List.class,"com.fxiaoke.open.erpsyncdata.writer.manager.SyncWriteMainManager.bulkUpdateBySyncDataId",Result2.class),

    ;

    private String dataType;

    private String desc;

    private String erpModule;

    private Class<?> argType;//方法的入参

    private String handlerMethod;//不考虑方法重载

    private Class<?> resultType;





}
