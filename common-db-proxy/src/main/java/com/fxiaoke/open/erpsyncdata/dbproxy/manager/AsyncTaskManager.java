package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.fasterxml.jackson.databind.JavaType;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.AsyncTaskResult;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisCacheManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ParallelUtils;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ExceptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.params.SetParams;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Consumer;

import static com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant.REDIS_ASYNC_TASK_CACHE;

/**
 * 异步任务
 *
 * <AUTHOR> (^_−)☆
 */
@Service
@Slf4j
public class AsyncTaskManager {
    @Autowired
    private RedisCacheManager redisCacheManager;

    /**
     * 异步执行任务，在指定时间内完成则直接返回结果，否则序列化后存入Redis
     *
     * @param taskKey              任务唯一标识
     * @param taskFunc             要执行的任务
     * @param waitSecond           执行等待时间（秒）
     * @param executeTimeOutSecond 执行超时时长（秒）
     * @param resultExpireSecond   结果过期时间（秒）
     * @param <R>                  返回结果类型
     * @return 任务执行结果或null（如果任务正在执行中）
     */
    public <R> AsyncTaskResult<R> executeTask(String tenantId, String taskKey, String taskName, long waitSecond,
                                              long executeTimeOutSecond, long resultExpireSecond,
                                              Consumer<AsyncTaskResult<R>> consumer) {
        AsyncTaskResult<R> asyncTaskResult = AsyncTaskResult.create(taskKey, taskName);
        String resultKey = REDIS_ASYNC_TASK_CACHE + tenantId + ":" + taskKey;
        //启动任务,写入执行中的缓存，等于同时加锁了，超时时间是执行超时时长
        SetParams nx = SetParams.setParams().ex(executeTimeOutSecond).nx();
        boolean setBeginRunningCache = redisCacheManager.setCache(resultKey, JacksonUtil.toJson(asyncTaskResult), nx);
        if (!setBeginRunningCache) {
            // resultKey如果复用的，可能有任务执行中
            return asyncTaskResult;
        }
        // 开始异步执行
        CompletableFuture<Void> rFuture = ParallelUtils.runAsyncBackgroundTask(() -> {
            consumer.accept(asyncTaskResult);
            // 成功执行后，执行finish防止consumer未正确修改错误码
            asyncTaskResult.finish();
        });

        // 尝试直接获取结果，成功则直接返回，executeTimeOutSecond后超时
        try {
            rFuture.get(waitSecond, TimeUnit.SECONDS);
            redisCacheManager.delCache(resultKey);
            return asyncTaskResult;
        } catch (ExecutionException e) {
            // 执行异常，直接抛出异常
            log.info("AsyncTaskManager execute task failed, taskKey:{}", taskKey, e);
            asyncTaskResult.error(ExceptionUtil.getSimpleMessage(e));
            redisCacheManager.delCache(resultKey);
            return asyncTaskResult;
        } catch (InterruptedException | TimeoutException ignore) {
            // 等待结果超时，但是后台线程仍继续执行，直接继续执行
        }

        // 另外创建一个线程用于超时销毁的，也就是说，执行这个方法，可能占用两个线程
        ParallelUtils.runAsyncBackgroundTask(() -> {
            try {
                rFuture.get(executeTimeOutSecond, TimeUnit.SECONDS);
            } catch (ExecutionException e) {
                // 执行异常
                log.info("AsyncTaskManager execute task failed, taskKey:{}", taskKey, e);
                asyncTaskResult.error(ExceptionUtil.getSimpleMessage(e));
            } catch (InterruptedException | TimeoutException e) {
                // 超时，中断任务
                log.info("AsyncTaskManager execute task timeout, taskKey:{}", taskKey);
                asyncTaskResult.error(ResultCodeEnum.ASYNC_TASK_EXECUTE_TIMEOUT);
                rFuture.cancel(true);
            } finally {
                // 无论是成功、异常、执行超时，都记录结果到缓存
                redisCacheManager.setCache(resultKey, JacksonUtil.toJson(asyncTaskResult), resultExpireSecond);
            }
        });

        return asyncTaskResult;
    }

    public <R> AsyncTaskResult<R> getAsyncTaskResultCache(String tenantId, String taskKey, Class<R> dataType) {
        return getAsyncTaskResultCache(tenantId, taskKey, JacksonUtil.constructType(dataType));
    }

    public <R> AsyncTaskResult<R> getAsyncTaskResultCache(String tenantId, String taskKey, JavaType dataType) {
        String resultKey = REDIS_ASYNC_TASK_CACHE + tenantId + ":" + taskKey;
        String cacheResult = redisCacheManager.getCache(resultKey);
        if (cacheResult != null) {
            JavaType resultType = JacksonUtil.get().getTypeFactory().constructParametricType(AsyncTaskResult.class, dataType);
            log.info("getAsyncTaskResultCache, taskKey:{}, cacheResult:{}", taskKey, cacheResult);
            AsyncTaskResult<R> cachedTask = JacksonUtil.fromJson(cacheResult, resultType);
            if (cachedTask.isSuccess()) {
                // 如果是成功，解析一下结果
                return cachedTask;
            }
            return cachedTask;
        } else {
            // 不存在key，直接抛出异常
            AsyncTaskResult<R> errorResult = new AsyncTaskResult<>();
            errorResult.setTaskKey(taskKey);
            errorResult.error("not found task,maybe it is expired!");
            return errorResult;
        }
    }
}