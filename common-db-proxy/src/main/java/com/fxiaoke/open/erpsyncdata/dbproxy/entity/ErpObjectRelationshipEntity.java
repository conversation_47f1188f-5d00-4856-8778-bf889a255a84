package com.fxiaoke.open.erpsyncdata.dbproxy.entity;


import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ObjApiName;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjSplitTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * erp对拆分信息
 * <AUTHOR> (^_−)☆
 * @date 2020/8/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ObjApiName("erpSplitObjectApiname")
@Table(name = "erp_object_relationship")
public class ErpObjectRelationshipEntity implements Serializable{
    @Id
    @Column(name = "id")
    private String id;

    /**
    * 企业id
    */
    @TenantID
    @Column(name = "tenant_id")
    private String tenantId;

    /**
     * 数据中心id
     */
    private String dataCenterId;

    /**
    * 渠道
    */
    @Column(name = "channel")
    private ErpChannelEnum channel;

    /**
     * 拆分批次号*/
    @Column(name = "split_seq")
    private Integer splitSeq;

    /**
    * erp实际对象apiName
    */
    @Column(name = "erp_actual_object_apiname")
    private String erpRealObjectApiname;

    /**
    * erp拆分对象apiName
    */
    @Column(name = "erp_split_object_apiname")
    private String erpSplitObjectApiname;

    /**
     * 拆分类型
     */
    @Column(name = "split_type")
    private ErpObjSplitTypeEnum splitType;

    /**
    * 创建时间
    */
    @Column(name = "create_time")
    private Long createTime;

    /**
    * 修改时间
    */
    @Column(name = "update_time")
    private Long updateTime;

}