package com.fxiaoke.open.erpsyncdata.dbproxy.constant;

import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;

/**
 * <AUTHOR>
 * @Date: 20:20 2023/5/25
 * @Desc:  跟SyncDataMappingsEntity对象属性保持一致
 */
public interface SyncDataMappingFieldKey {

    /** 源企业主对象apiName */
    String sourceObjectApiName="sourceObjectApiName";
    /** 源数据id */
    String sourceDataId="sourceDataId";
    /** 源数据名称 */
    String sourceDataName="sourceDataName";
    /** 目标主对象apiName */
    String destObjectApiName="destObjectApiName";
    /** 目标数据id */
    String destDataId="destDataId";
    /** 目标数据名称 */
    String destDataName="destDataName";

    /** 日志备注 */
    String remark="remark";

    /** 主对象源数据id */
    String masterDataId="masterDataId";
}
