package com.fxiaoke.open.erpsyncdata.dbproxy.entity.data;

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;

@Data
public class SyncObjectAndTenantMappingData implements Serializable {
    private String sourceTenantId;
    private String sourceObjectApiName;
    private String destTenantId;
    private String destObjectApiName;

    public static SyncObjectAndTenantMappingData newInstance(String sourceTenantId, String sourceObjectApiName, String destTenantId, String destObjectApiName) {
        SyncObjectAndTenantMappingData objectApiNameMappingData = new SyncObjectAndTenantMappingData();
        objectApiNameMappingData.setSourceTenantId(sourceTenantId);
        objectApiNameMappingData.setSourceObjectApiName(sourceObjectApiName);
        objectApiNameMappingData.setDestTenantId(destTenantId);
        objectApiNameMappingData.setDestObjectApiName(destObjectApiName);
        return objectApiNameMappingData;
    }


    public static SyncObjectAndTenantMappingData newInstance(String tenantId, String sourceObjectApiName, String destObjectApiName) {
        SyncObjectAndTenantMappingData objectApiNameMappingData = new SyncObjectAndTenantMappingData();
        objectApiNameMappingData.setSourceTenantId(tenantId);
        objectApiNameMappingData.setSourceObjectApiName(sourceObjectApiName);
        objectApiNameMappingData.setDestTenantId(tenantId);
        objectApiNameMappingData.setDestObjectApiName(destObjectApiName);
        return objectApiNameMappingData;
    }

    public String toString() {
        return  this.sourceObjectApiName + "-" + this.destObjectApiName;
    }

    public static SyncObjectAndTenantMappingData newInstance(String tenantId, SyncPloyDetailEntity ployDetail, String sourceObjectApiName, I18NStringManager i18NStringManager) {
        if (ployDetail.getSourceObjectApiName().equals(sourceObjectApiName)) {
            return SyncObjectAndTenantMappingData.newInstance(tenantId,
                    ployDetail.getSourceObjectApiName(),
                    tenantId,
                    ployDetail.getDestObjectApiName());
        }
        for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : ployDetail.getDetailObjectMappings()) {
            if (detailObjectMapping.getSourceObjectApiName().equals(sourceObjectApiName)) {
                return SyncObjectAndTenantMappingData.newInstance(tenantId,
                        detailObjectMapping.getSourceObjectApiName(),
                        tenantId,
                        detailObjectMapping.getDestObjectApiName());
            }
        }
        throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s121, tenantId, sourceObjectApiName));
    }
}
