package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.BatchGetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.result.BatchGetEmployeeDtoResult;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.DataCenterModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.DataIntegrationNotificationModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.PloyDetailModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.UserModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.DataIntegrationNotificationDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataIntegrationNotificationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.NotificationType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class DataIntegrationNotificationManager {
    @Autowired
    private DataIntegrationNotificationDao dataIntegrationNotificationDao;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private EmployeeProviderService employeeProviderService;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;

    public void insert(String tenantId,
                        String dataCenterId,
                        List<String> ployDetailIdList,
                        AlarmRuleType alarmRuleType,
                        String alarmRuleName,
                        AlarmType alarmType,
                        AlarmLevel alarmLevel,
                        String msg,
                        List<Integer> userIdList,
                        List<String> notifyType) {
        if(alarmType==AlarmType.SUPER_ADMIN) return;
        if(StringUtils.isEmpty(dataCenterId)) {
            log.info("DataIntegrationNotificationManager.insert,dataCenterId is null,msg={}",msg);
            return;
        }
        if(CollectionUtils.isEmpty(ployDetailIdList)) {
            log.info("DataIntegrationNotificationManager.insert,ployDetailIdList is empty,msg={}",msg);
            return;
        }

        List<Integer> userIdList2 = CollectionUtils.isNotEmpty(userIdList) ? userIdList : new ArrayList<>();

        dataIntegrationNotificationDao.insert(tenantId,
                dataCenterId,
                ployDetailIdList,
                alarmRuleType,
                alarmRuleName,
                alarmType,
                alarmLevel,
                msg,
                userIdList2,
                notifyType);
    }

    public DataIntegrationNotificationModel getDataListByPage(String tenantId,
                                                              String dataCenterId,
                                                              List<String> ployDetailIdList,
                                                              Integer userId,
                                                              NotificationType notificationType,
                                                              AlarmType alarmType,
                                                              AlarmLevel alarmLevel,
                                                              Date startTime,
                                                              Date endTime,
                                                              int pageSize,
                                                              int page) {
        DataIntegrationNotificationModel model = dataIntegrationNotificationDao.getDataListByPage(tenantId,
                dataCenterId,
                ployDetailIdList,
                userId,
                notificationType,
                alarmType,
                alarmLevel,
                startTime,
                endTime,
                pageSize,
                page);

        model.setDataList(new ArrayList<>());

        if(CollectionUtils.isNotEmpty(model.getEntityList())) {
            for(DataIntegrationNotificationEntity notificationEntity : model.getEntityList()) {
                DataIntegrationNotificationModel.Entry entry = new DataIntegrationNotificationModel.Entry();
                BeanUtils.copyProperties(notificationEntity,entry);
                entry.setId(notificationEntity.getId().toString());

                ErpConnectInfoEntity connectInfoEntity = erpConnectInfoManager.getByIdAndTenantId(tenantId,
                        entry.getDataCenterId());
                if (connectInfoEntity == null) {
                    continue;
                }
                entry.setDataCenterName(connectInfoEntity.getDataCenterName());

                List<SyncPloyDetailEntity> ployDetailEntityList = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(DataBaseBatchIndexUtil.notTenantId))
                        .listByIds(notificationEntity.getPloyDetailIdList());
                if(CollectionUtils.isEmpty(ployDetailEntityList)) {
                    continue;
                }

                entry.setPloyDetailList(new ArrayList<>());
                for(SyncPloyDetailEntity ployDetailEntity : ployDetailEntityList) {
                    PloyDetailModel ployDetailModel = new PloyDetailModel(ployDetailEntity.getId(),
                            ployDetailEntity.getIntegrationStreamName());
                    entry.getPloyDetailList().add(ployDetailModel);
                }

                entry.setUserList(new ArrayList<>());
                if(CollectionUtils.isNotEmpty(notificationEntity.getUserIdList())) {
                    BatchGetEmployeeDtoArg arg = new BatchGetEmployeeDtoArg();
                    arg.setEnterpriseId(Integer.valueOf(tenantId));
                    arg.setRunStatus(RunStatus.ALL);
                    arg.setEmployeeIds(notificationEntity.getUserIdList());
                    BatchGetEmployeeDtoResult result = employeeProviderService.batchGetEmployeeDto(arg);
                    if(result!=null && CollectionUtils.isNotEmpty(result.getEmployeeDtos())) {
                        for(EmployeeDto employeeDto : result.getEmployeeDtos()) {
                            UserModel userModel = new UserModel(employeeDto.getEmployeeId(),
                                    employeeDto.getName());
                            entry.getUserList().add(userModel);
                        }
                    }
                }

                model.getDataList().add(entry);
            }
        }
        model.getEntityList().clear();
        model.setEntityList(null);
        return model;
    }

    public List<PloyDetailModel> getPloyDetailListInProgress(String tenantId,
                                                             String dataCenterId,
                                                             List<String> ployDetailIdList,
                                                             AlarmType alarmType) {
        List<DataIntegrationNotificationEntity> entityList = dataIntegrationNotificationDao.distinct(tenantId, dataCenterId, ployDetailIdList);

        List<PloyDetailModel> dataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(entityList)) {
            for (DataIntegrationNotificationEntity entity : entityList) {
                if (!alarmType.equals(entity.getAlarmType())) continue;
                List<SyncPloyDetailEntity> ployDetailEntityList = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(DataBaseBatchIndexUtil.notTenantId))
                        .listByIds(entity.getPloyDetailIdList());
                if (CollectionUtils.isEmpty(ployDetailEntityList)) continue;

                SyncPloyDetailEntity ployDetailEntity = ployDetailEntityList.get(0);
                dataList.add(new PloyDetailModel(ployDetailEntity.getId(), ployDetailEntity.getIntegrationStreamName()));
            }
        }
        return dataList;
    }

    public List<DataCenterModel> getDcList(String tenantId) {
        List<String> dcIdList = dataIntegrationNotificationDao.getDataListByDcId(tenantId);
        if(CollectionUtils.isEmpty(dcIdList)) return new ArrayList<>();

        List<ErpConnectInfoEntity> entityList = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listByIds(tenantId, dcIdList);
        Map<String,String> map = new LinkedHashMap<>();
        for(ErpConnectInfoEntity entity : entityList) {
            if(!StringUtils.equalsIgnoreCase(entity.getTenantId(),tenantId)) continue;
            if(map.containsKey(entity.getId())) continue;
            map.put(entity.getId(),entity.getDataCenterName());
        }
        List<DataCenterModel> dataList = new ArrayList<>();
        for(String key : map.keySet()) {
            dataList.add(new DataCenterModel(key,map.get(key)));
        }
        return dataList;
    }

    public long clearData() {
        long count = dataIntegrationNotificationDao.deleteMany(null);
        return count;
    }

    public long deleteByPloyDetailId(String tenantId, String ployDetailId) {
        long count = dataIntegrationNotificationDao.deleteByPloyDetailId(tenantId,ployDetailId);
        log.info("DataIntegrationNotificationManager.deleteByPloyDetailId,tenantId:{},ployDetailId:{},count:{}", tenantId, ployDetailId, count);
        return count;
    }

    public long updateAlarmStatus(String tenantId, String ployDetailId, AlarmType alarmType, boolean recover) {
        long count = dataIntegrationNotificationDao.updateAlarmStatus(tenantId, ployDetailId, alarmType, recover);
        log.info("DataIntegrationNotificationManager.deleteByAlarmType,tenantId:{},ployDetailId:{},count:{}", tenantId, ployDetailId, count);
        return count;
    }

    public boolean hasData(String tenantId, Integer userId) {
        ErpTenantConfigurationEntity entity = tenantConfigurationManager.findGlobal(TenantConfigurationTypeEnum.DATA_INTEGRATION_NOTIFICATION_ENTRY_WHITE_LIST.name());
        if(entity == null) {
            return false;
        }

        if(StringUtils.containsIgnoreCase(entity.getConfiguration(),tenantId) || StringUtils.equalsIgnoreCase(entity.getConfiguration(),"*")) {
            return true;
        }

        return false;
    }
}
