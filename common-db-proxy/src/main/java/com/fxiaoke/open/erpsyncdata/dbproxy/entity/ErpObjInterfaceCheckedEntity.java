package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ObjApiName;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * ERP对象接口选择表
 *
 * <AUTHOR>
 * @date 2023.08.02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ObjApiName("objApiName")
@Table(name = "erp_obj_interface_checked")
public class ErpObjInterfaceCheckedEntity implements Serializable {
    private static final long serialVersionUID = 3739723344368834569L;

    @Id
    @Column(name = "id")
    private String id;

    /**
     * 企业id
     */
    @TenantID
    @Column(name = "tenant_id")
    private String tenantId;

    /**
     * 数据中心id
     */
    @Column(name = "data_center_id")
    private String dataCenterId;

    /**
     * 对象apiName
     */
    @Column(name = "obj_api_name")
    private String objApiName;

    /**
     * 接口URL类型
     */
    @Column(name = "interface_url")
    private ErpObjInterfaceUrlEnum interfaceUrl;

    /**
     * 选中的接口类型
     */
    @Column(name = "checked_interface_type")
    private ErpObjInterfaceTypeEnum checkedInterfaceType;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Long updateTime;
}