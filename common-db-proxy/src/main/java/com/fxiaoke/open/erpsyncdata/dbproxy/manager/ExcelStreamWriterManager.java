package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.easyexcel.CustomCellWriteHeightConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.easyexcel.CustomCellWriteWeightConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ExcelSheetArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ExcelTemplateWriteHandler;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DBExcelUtils;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.reflect.FieldUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ExcelStreamWriterManager {

    private final File tempFile;
    private final ExcelWriter excelWriter;
    private final String tenantId;
    private final String lang;
    private final String fileName;
    private I18NStringManager i18NStringManager;

    // 缓存单个 sheet 的 WriteSheet 对象，保证重复写入是同一个 sheet
    private WriteSheet writeSheet = null;

    public ExcelStreamWriterManager(String tenantId, String lang, String fileName, I18NStringManager i18NStringManager) throws Exception {
        this.tenantId = tenantId;
        this.lang = lang;
        this.fileName = fileName;
        this.i18NStringManager = i18NStringManager;

        // 创建临时文件，避免内存占用
        this.tempFile = File.createTempFile("export-" + tenantId + System.currentTimeMillis() % 1000, ".xlsx");
        FileOutputStream fos = new FileOutputStream(tempFile);

        this.excelWriter = EasyExcelFactory.write(fos)
                .registerWriteHandler(DBExcelUtils.getDefaultStyle())
                .registerWriteHandler(new ExcelTemplateWriteHandler(fileName))
                .registerWriteHandler(new CustomCellWriteWeightConfig(10))
                .registerWriteHandler(new CustomCellWriteHeightConfig())
                .build();
    }

    /**
     * 对单个sheet的追加写入。
     * 调用多次，将多批数据追加到同一个sheet。
     */
    public Boolean writeSheetBatch(ExcelSheetArg sheetArg) {
        try {
            if (writeSheet == null) {
                // 第一次调用，初始化 WriteSheet 和表头
                if (CollectionUtils.isEmpty(sheetArg.getHeaders())) {
                    final List<List<String>> heads = Arrays.stream(FieldUtils.getAllFields(sheetArg.getClazz()))
                            .map(field -> field.getAnnotation(ExcelProperty.class))
                            .filter(Objects::nonNull)
                            .sorted(Comparator.comparingInt(ExcelProperty::index))
                            .map(annotation -> {
                                final String[] value = annotation.value();
                                final String key = value[0];
                                if (key.startsWith("erpdss.global")) {
                                    return i18NStringManager.get(key, lang, tenantId, key);
                                }
                                return key;
                            })
                            .map(Collections::singletonList)
                            .collect(Collectors.toList());
                    sheetArg.setHeaders(heads);
                }

                writeSheet = EasyExcelFactory.writerSheet(0, sheetArg.getSheetName())
                        .head(sheetArg.getHeaders())
                        .build();
            }

            // 分批写入数据，追加到同一个 sheet
            excelWriter.write(sheetArg.getDataList(), writeSheet);
            return true;
        } catch (Exception e) {
            log.error("write sheet error,e=", e);
            return false;
        }
    }

    /**
     * 结束写入，释放资源，返回Excel文件
     */
    public File finish() {
        excelWriter.finish();
        return tempFile;
    }

    /**
     * 清理临时文件
     */
    public void cleanup() {
        if (tempFile != null && tempFile.exists()) {
            if (!tempFile.delete()) {
                log.warn("delete temp file error: {}", tempFile.getAbsolutePath());
            }
        }
    }

    /**
     * 查看临时文件大小
     */
    public long getTempFileSize() {
        if (this.tempFile.exists() && this.tempFile.isFile()) {
            return this.tempFile.length();
        }
        return 0;
    }
}