package com.fxiaoke.open.erpsyncdata.dbproxy.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import lombok.AllArgsConstructor;

/**
 * 云星空旗舰版事件类型枚举
 * <AUTHOR>
 * @date 2023-11-14
 */
@AllArgsConstructor
public enum K3UltimateEventTypeEnum {
    SAVE("保存", I18NStringEnum.s368.getI18nKey()),
    SUBMIT("提交", I18NStringEnum.s2216.getI18nKey()),
    UN_SUBMIT("反提交", I18NStringEnum.s2217.getI18nKey()),
    AUDIT("审核", I18NStringEnum.s2212.getI18nKey()),
    UN_AUDIT("反审核", I18NStringEnum.s2218.getI18nKey());

    private final String name;
    private final String i18nKey;

    public String getName(I18NStringManager i18NStringManager, String lang, String tenantId) {
        return i18NStringManager.get(i18nKey,lang,tenantId,name);
    }
}
