package com.fxiaoke.open.erpsyncdata.dbproxy.monitor;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/9/23
 */
public class SyncTrace {

    private final static ThreadLocal<SyncInfo> syncInfoHolder = new ThreadLocal<>();

    public static void set(SyncInfo syncInfo) {
        syncInfoHolder.set(syncInfo);
    }

    public static SyncInfo get() {
        return syncInfoHolder.get();
    }

    public static void remove(){
        syncInfoHolder.remove();
    }


    @NoArgsConstructor
    @Builder
    @Data
    @AllArgsConstructor
    public static class SyncInfo {
        private String syncDataId;
        private InterfaceMonitorData interfaceMonitorData;
    }
}
