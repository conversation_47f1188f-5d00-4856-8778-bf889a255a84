package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjSplitTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.AssertUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjectTypeEnum.REAL_OBJECT;
import static com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjectTypeEnum.SPLIT_OBJECT;

/**
 * erp对象处理层，可增加缓存
 *
 * <AUTHOR> (^_−)☆
 * @date 2021/3/15
 */
@Service
@Slf4j
@DependsOn("jcCacheManager")
public class ErpObjManager {
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    /**
     * 仅支持中间对象调用
     * 本地缓存，失败则取objAPiName
     *
     * @param tenantId
     * @param objApiName
     * @return
     */
    @Cached(cacheType = CacheType.LOCAL, expire = 120)
    public String getErpObjName(String tenantId, String objApiName) {
        ErpObjectEntity byObjApiName = erpObjManager.getErpObj(tenantId, objApiName);
        if (byObjApiName != null && byObjApiName.getErpObjectName() != null) {
            return byObjApiName.getErpObjectName();
        }
        log.warn("this obj not found,tenantId:{},objaApiName:{}", tenantId, objApiName);
        return objApiName;
    }

    /**
     * 真实对象和中间对象都可以调用
     * 本地缓存，失败则取objAPiName
     *
     * @param tenantId
     * @param dcId
     * @param objApiName
     * @return
     */
    @Cached(cacheType = CacheType.LOCAL, expire = 120)
    public String getErpObjName(String tenantId, String dcId, String objApiName) {
        ErpObjectEntity byObjApiName = erpObjManager.getErpObj(tenantId, dcId, objApiName);
        if (byObjApiName != null && byObjApiName.getErpObjectName() != null) {
            return byObjApiName.getErpObjectName();
        }
        log.warn("this obj not found,tenantId:{},objaApiName:{}", tenantId, objApiName);
        return objApiName;
    }

    /**
     * 根据真实apiName获取所有的虚拟apiName、不传数据中心id
     * <br>
     * 这个方法不规范，没传dcId，尽量不要调用。
     *
     * @param tenantId
     * @param actualApiName
     * @return
     */
    @Deprecated
    @Cached(cacheType = CacheType.BOTH, expire = 120)
    public List<ErpObjectRelationshipEntity> getSplitObjRelations(String tenantId, String actualApiName) {
        List<ErpObjectRelationshipEntity> byObjApiName = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getSplitObjApiName(tenantId, actualApiName);
        return byObjApiName;
    }

    /**
     * 获取对象
     * 暂时没哟清除缓存处理
     *
     * @param objApiName 中间对象apiName
     */
    @Cached(name = "erpObj.", key = "args[0]+'.'+args[1]", cacheType = CacheType.BOTH, expire = 600)
    public ErpObjectEntity getErpObj(String tenantId, String objApiName) {
        return erpObjectDao.getByObjApiName(tenantId, null, objApiName);
    }

    /**
     * 获取对象
     * dcId可以传null。
     *
     * @param objApiName 中间对象apiName
     */
    @Cached(name = "erpObj.", key = "args[0]+'.'+args[1]+'.'+args[2]", cacheType = CacheType.BOTH, expire = 600)
    public ErpObjectEntity getErpObj(String tenantId, String dcId, String objApiName) {
        return erpObjectDao.getByObjApiName(tenantId, dcId, objApiName);
    }


    /**
     * 获取中间对象,这个缓存在dao上
     *
     * @param tenantId
     * @param objApiName 中间对象APIName
     * @return
     */
    public ErpObjectRelationshipEntity getRelation(String tenantId, String objApiName) {
        ErpObjectRelationshipEntity bySplit = erpObjectRelationshipDao.findBySplit(tenantId, objApiName);
        if (bySplit == null) {
            throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s149.getI18nKey(),
                    tenantId,
                    i18NStringManager.getByEi2(I18NStringEnum.s149, tenantId,objApiName),
                    Lists.newArrayList(objApiName)),null,null);
        }
        return bySplit;
    }

    public String getRealObjApiName(String tenantId, String objApiName) {
        return getRelation(tenantId, objApiName).getErpRealObjectApiname();
    }

    /**
     * 获取对象关系
     */
    @Cached(cacheType = CacheType.LOCAL, expire = 120)
    public List<ErpObjectRelationshipEntity> listRelationsByRealApiName(String tenantId, String dcId, String realObjApiName) {
        return erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findByRealObjectApiName(tenantId, dcId, realObjApiName);
    }

    /**
     * 获取主对象的中间对象
     */
    @Cached(cacheType = CacheType.BOTH, expire = 120)
    public List<ErpObjectRelationshipEntity> listMasterRelationsByRealApiName(String tenantId, String dcId, String realObjApiName) {
        return erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findMasterByRealObjectApiName(tenantId, dcId, realObjApiName);
    }

    /**
     * 获取中间对象apiName
     * 仅支持主对象
     */
    public String getMasterSplitObjApiName(String tenantId, String dcId, String realObjApiName) {
        List<ErpObjectRelationshipEntity> masterRelations = erpObjManager.listMasterRelationsByRealApiName(tenantId, dcId, realObjApiName);
        AssertUtil.isTrue(masterRelations.size() == 1, I18NStringEnum.s236,tenantId);
        return masterRelations.get(0).getErpSplitObjectApiname();
    }

    /**
     * 真实对象和中间主对象apiNameMap
     *
     * @return key：真实对象apiName，value：中间主对象apiName集合
     */
    @Cached(name = "realMainSplitObjApiNamesMap.", key = "args[0]+'.'+args[1]", cacheType = CacheType.BOTH, expire = 500)
    public Map<String, Set<String>> getRealMainSplitObjApiNamesMap(String tenantId, String dcId) {
        List<ErpObjectRelationshipEntity> relationshipEntities = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findNotSplit(tenantId, dcId);
        if (CollectionUtils.isEmpty(relationshipEntities)) {
            return Maps.newHashMap();
        }
        Map<String, Set<String>> collect = relationshipEntities.stream().collect(Collectors.groupingBy(ErpObjectRelationshipEntity::getErpRealObjectApiname,
                Collectors.mapping(ErpObjectRelationshipEntity::getErpSplitObjectApiname, Collectors.toSet())));
        return collect;
    }


    /**
     * 中间主对象apiNameMap和真实对象的Map
     *
     * @return key：中间主对象apiName，value：真实对象apiName
     */
    public Map<String, String> getSplitObjApiNameToRealObjApiNameMap(String tenantId, String dcId) {
        Map<String, Set<String>> realMainSplitObjApiNamesMap = erpObjManager.getRealMainSplitObjApiNamesMap(tenantId, dcId);
        if (realMainSplitObjApiNamesMap.isEmpty()) {
            return Maps.newHashMap();
        }
        Map<String, String> result = new HashMap<>();
        realMainSplitObjApiNamesMap.forEach((k, v) -> v.stream()
                .filter(Objects::nonNull)
                .forEach(v1 -> result.put(v1, k)));        return result;
    }


    /**
     * 检查需要单独发送明细的对象。
     * 目前只有K3C的孙表.
     * 缓存不实时更新
     */
    @Cached(cacheType = CacheType.LOCAL, expire = 120, localLimit = 2000)
    public Set<String> getDetailsNeedSendAlone(String tenantId, String mainObjApiName) {
        //查找当前对象真实对象和批次
        ErpObjectRelationshipEntity relation = getRelation(tenantId, mainObjApiName);
        if (relation == null) {
            throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s150.getI18nKey(),
                    tenantId,
                    i18NStringManager.getByEi2(I18NStringEnum.s3702, tenantId,mainObjApiName),
                    Lists.newArrayList(mainObjApiName)),null,null);
        }
        List<ErpObjectRelationshipEntity> allSplitObj = erpObjectRelationshipDao.findByRealObjectApiName(tenantId, relation.getDataCenterId(), relation.getErpRealObjectApiname());
        List<String> collect = allSplitObj.stream()
                .filter(v -> Objects.equals(v.getSplitSeq(), relation.getSplitSeq())
                        && Objects.equals(v.getSplitType(), ErpObjSplitTypeEnum.SUB_DETAIL_LOOKUP_DETAIL))
                .map(v -> v.getErpSplitObjectApiname())
                .collect(Collectors.toList());
        return Sets.newHashSet(collect);
    }

    /**
     * 获取主对象的排序
     * 因为批次和拆分逻辑允许出现多种对象创建集成流，为区分logId，增加序号。
     *
     * @param tenantId
     * @return key:ObjApiName value:序列
     */
    @Cached(name = "erpMainObjSeqMap.", key = "args[0]", cacheType = CacheType.BOTH, expire = 120, localLimit = 1000)
    public Map<String, Integer> getMainSeqMap(String tenantId) {
        Map<String, Integer> result = new HashMap<>();
        List<ErpObjectRelationshipEntity> relations = erpObjectRelationshipDao.listByTenantId(tenantId);
        try {
            //分组排序
            Map<String, List<ErpObjectRelationshipEntity>> groupMap = relations.stream()
                    //筛选可以作为集成流对接的对象
                    .filter(v -> ErpObjSplitTypeEnum.TYPES_ALLOW_INDEPENDENT_STREAM.contains(v.getSplitType()))
                    //排序
                    .sorted(Comparator.comparingInt(v -> v.getSplitType().ordinal()))
                    //按真实对象和数据中心分组
                    .collect(Collectors.groupingBy(v -> v.getErpRealObjectApiname() + v.getDataCenterId(), Collectors.toList()));
            for (List<ErpObjectRelationshipEntity> group : groupMap.values()) {
                for (int i = 0; i < group.size(); i++) {
                    result.putIfAbsent(group.get(i).getErpSplitObjectApiname(), i);
                }
            }
        } catch (Exception e) {
            log.info("get main seq throw exception", e);
        }
        return result;
    }


    public List<ErpObjectEntity> queryByErpObjApiNamesList(String tenantId,
                                                           String dcId,
                                                           Collection<String> erpObjectApiNameCollection) {
        return erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .queryByApiNames2(tenantId, dcId, erpObjectApiNameCollection);
    }

    public List<ErpObjectEntity> getDetailErpObjEntityList(String tenantId, String dataCenterId, String mainObjApiName) {
        List<ErpObjectRelationshipEntity> relationshipEntityList = erpObjManager.listRelationsByRealApiName(tenantId, dataCenterId, mainObjApiName);
        List<String> splitObjApiNameList = relationshipEntityList.stream()
                .filter(entity -> entity.getSplitType() != ErpObjSplitTypeEnum.NOT_SPLIT)
                .map(ErpObjectRelationshipEntity::getErpSplitObjectApiname)
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(splitObjApiNameList))
            return new ArrayList<>();

        List<ErpObjectEntity> erpObjectEntityList = erpObjManager.queryByErpObjApiNamesList(tenantId, dataCenterId, splitObjApiNameList);
        return erpObjectEntityList;
    }

    public ErpObjectEntity getDetailEntity(String tenantId, String dataCenterId, String mainObjApiName, String detailObjApiName) {
        List<ErpObjectEntity> list = getDetailErpObjEntityList(tenantId, dataCenterId, mainObjApiName);
        if(CollectionUtils.isNotEmpty(list)) {
            for(ErpObjectEntity entity : list) {
                if(StringUtils.equalsIgnoreCase(entity.getErpObjectExtendValue(),detailObjApiName))
                    return entity;
            }
        }
        return null;
    }

    @Cached(expire = 15, cacheType = CacheType.LOCAL, localLimit = 1000, timeUnit = TimeUnit.SECONDS)
    public String validateErpObjApiName(String tenantId, String objApiName, String dataCenterId) {
        ErpObjectRelationshipEntity relationshipEntity = new ErpObjectRelationshipEntity();
        relationshipEntity.setTenantId(tenantId);
        relationshipEntity.setDataCenterId(dataCenterId);
        relationshipEntity.setErpRealObjectApiname(objApiName);
        relationshipEntity.setSplitType(ErpObjSplitTypeEnum.NOT_SPLIT);
        List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities =
                erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                        .queryList(relationshipEntity);
        if (org.springframework.util.CollectionUtils.isEmpty(erpObjectRelationshipEntities)) {
            return null;
        }
        return erpObjectRelationshipEntities.get(0).getErpSplitObjectApiname();
    }



    @Cached(expire = 15, cacheType = CacheType.LOCAL, localLimit = 1000, timeUnit = TimeUnit.SECONDS)
    //返回值：map <真实对象apiname, 拆分对象apiname>
    public Map<String,String> getK3ObjRealApiNameMap(String tenantId, String dataCenterId, String erpRealObjectApiname) {
        //map 真实apiname-虚拟apiname
        Map<String,String> apiNameMap = Maps.newHashMap();
        //根据真实apiname+dataCenterId获取对应的erpobjfield
        ErpObjectRelationshipEntity query = new ErpObjectRelationshipEntity();
        query.setTenantId(tenantId);
        query.setDataCenterId(dataCenterId);
        query.setErpRealObjectApiname(erpRealObjectApiname);
        query.setSplitSeq(1);//K3渠道apiname不能重复
        //根据真实的apiname拿到相关的对象列表
        List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);

        for (ErpObjectRelationshipEntity erpObjectRelationshipEntity : erpObjectRelationshipEntities) {
            ErpObjectEntity erpObj = erpObjManager.getErpObj(tenantId, dataCenterId, erpObjectRelationshipEntity.getErpSplitObjectApiname());
            if(SPLIT_OBJECT.equals(erpObj.getErpObjectType())){
                if(ObjectUtils.isEmpty(erpObj.getErpObjectExtendValue())){
                    apiNameMap.put(erpRealObjectApiname,erpObj.getErpObjectApiName());
                    continue;
                }
                //getErpObjectExtendValue 是erp对象的entry, 和erp拆分对象apiname在erp_object表中对应。
                apiNameMap.put(erpObj.getErpObjectExtendValue(),erpObj.getErpObjectApiName());
            }

            if(REAL_OBJECT.equals(erpObj.getErpObjectType())){
                apiNameMap.put(erpObj.getErpObjectApiName(),erpObjectRelationshipEntity.getErpSplitObjectApiname());//主对象的
            }
        }
        return apiNameMap;
    }
}
