package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldExtendDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.InitErpObjectFieldsArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 字段绑定扩展
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/11/20
 */
@Service
@Slf4j
public class ErpFieldManager {
    @Autowired
    private ErpFieldExtendDao erpFieldExtendDao;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private ErpObjManager erpObjManager;

    @LogLevel(LogLevelEnum.DEBUG)
    @Cached(expire = 30,cacheType = CacheType.LOCAL)
    public List<ErpFieldExtendEntity> queryAllFieldExtend(String tenantId, String dataCenterId, String objApiName) {
        List<ErpFieldExtendEntity> result = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .queryByObjApiName(tenantId, dataCenterId, objApiName);
        return result;
    }

    @LogLevel(LogLevelEnum.DEBUG)
    @Cached(expire = 30, cacheType = CacheType.LOCAL)
    public List<ErpFieldExtendEntity> getAllNeedQueryFieldExtend(String tenantId, String dataCenterId, String objApiName) {
        List<ErpFieldExtendEntity> result = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getAllNeedQueryFieldExtend(tenantId, dataCenterId, objApiName);
        //从代码获取，暂时只是k3的
        Set<String> needQueryCodeSet = getNeedQueryCodePresetK3(objApiName);
        try {
            ErpTenantConfigurationEntity configurationEntity = tenantConfigurationManager.findOne("all", "0", ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.OBJECT_FIXED_FIELD_NAME.name());
            if (ObjectUtils.isNotEmpty(configurationEntity) && StringUtils.isNotEmpty(configurationEntity.getConfiguration())) {
                //配置的字段的queryCode
                Map<String, List<String>> configurationMap = JacksonUtil.fromJson(configurationEntity.getConfiguration(), new TypeReference<Map<String, List<String>>>() {
                });
                List<String> needQueryCodeList = configurationMap.get(objApiName);
                if (CollectionUtils.isNotEmpty(needQueryCodeList)) {
                    needQueryCodeSet.addAll(needQueryCodeList);
                }
            }
            if (!needQueryCodeSet.isEmpty()) {
                List<ErpFieldExtendEntity> erpFieldExtendEntities = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).
                        queryByObjQueryCode(tenantId, dataCenterId, objApiName, needQueryCodeSet);
                result.addAll(erpFieldExtendEntities);
                List<ErpFieldExtendEntity> distinctResult = result.stream().distinct().collect(Collectors.toList());
                return distinctResult;
            }
        } catch (Exception e) {
            log.error("getAllNeed queryFixed tenantId,fail:{}", tenantId, e);
        }
        return result;
    }

    /**
     * 改配置很麻烦的，支持在代码预置。注意一定是一定有的字段，不要把企业特殊的字段加上来。
     */
    public Set<String> getNeedQueryCodePresetK3(String objApiName){
        Set<String> needQueryCodeSet = new LinkedHashSet<>();
        if ("ENG_BOM.TreeEntity".equals(objApiName)){
            //物料清单明细
            needQueryCodeSet.add("FNUMERATOR");
            needQueryCodeSet.add("FDENOMINATOR");
        }
        return needQueryCodeSet;
    }

    @LogLevel(LogLevelEnum.TRACE)
    @Cached(expire = 30,cacheType = CacheType.LOCAL)
    public List<ErpObjectFieldEntity> queryAllField(String tenantId,String dataCenterId, String objApiName) {
        ErpObjectFieldEntity arg = ErpObjectFieldEntity.builder()
                .tenantId(String.valueOf(tenantId))
                .dataCenterId(dataCenterId)
                .erpObjectApiName(objApiName).build();
        List<ErpObjectFieldEntity> result = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(arg);
        return result;
    }

    @LogLevel(LogLevelEnum.DEBUG)
    @Cached(expire = 30,cacheType = CacheType.LOCAL)
    public List<ErpObjectFieldEntity> findByObjApiNameAndType(String tenantId
            , String erpObjectApiName
            ,  Collection<ErpFieldTypeEnum> fieldDefineTypeCollection){
        return erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findByObjApiNameAndType(tenantId, erpObjectApiName, fieldDefineTypeCollection);
    }

    /**
     * @param tenantId
     * @param objApiName 真实apiName
     * @return
     */
    @LogLevel(LogLevelEnum.DEBUG)
    //@Cached(expire = 120,name = "FieldExtendMap-", key = "#tenantId + ':' + #objApiName",cacheType = CacheType.LOCAL)
    public Map<String, ErpFieldExtendEntity> queryFieldMap(String tenantId,String dataCenterId, String objApiName) {
        List<ErpFieldExtendEntity> result = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .queryByObjApiName(tenantId, dataCenterId, objApiName);
        Map<String, ErpFieldExtendEntity> map = result.stream().collect(
                Collectors.toMap(ErpFieldExtendEntity::getFieldApiName, u -> u, (q, w) -> q));
        return map;
    }

    @Cached(expire = 120,cacheType = CacheType.LOCAL)
    public List<ErpFieldExtendEntity> queryIdField(String tenantId, String dataCenterId, String objApiName) {
        List<ErpFieldExtendEntity> result = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .queryIdFieldByObjApiName(tenantId,dataCenterId, objApiName);
        return result;
    }

//    @CacheInvalidate(name = "FieldExtendMap-", key = "#arg.tenantId + ':' + #arg.objApiName")
    public void upsertExtend(ErpFieldExtendEntity arg,boolean isMainAttribute){
        arg.setObjApiName(arg.getObjApiName());
        ErpFieldExtendEntity one = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(arg.getTenantId()))
                .findOne(arg.getTenantId(),arg.getDataCenterId(), arg.getObjApiName(), arg.getFieldApiName());
        if (one == null){
            String id = idGenerator.get();
            Long now = System.currentTimeMillis();
            arg.setId(id);
            arg.setPriority(65536L);
            arg.setCreateTime(now);
            arg.setUpdateTime(now);
            erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(arg.getTenantId())).insert(arg);
        }else {
            if("e12".equals(one.getErpFieldType())&&!isMainAttribute){//清空主属性e12
                arg.setErpFieldType("");
            }
            erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(arg.getTenantId())).updateByApiName(arg);
        }
    }

    public void copyFieldByEi(String originalTenantId, String targetTenantId,String targetDcId,String objApiName,String suffixName,ErpChannelEnum erpChannelEnum) {
        Long now = System.currentTimeMillis();
        //复制字段
        ErpObjectFieldEntity erpObjectFieldEntity = new ErpObjectFieldEntity();
        erpObjectFieldEntity.setTenantId(originalTenantId);
        erpObjectFieldEntity.setChannel(erpChannelEnum);
        if (objApiName != null) {
            erpObjectFieldEntity.setErpObjectApiName(objApiName);
        }
        List<ErpObjectFieldEntity> erpObjectFieldEntities = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(originalTenantId))
                .queryList(erpObjectFieldEntity);
        List<ErpObjectFieldEntity> newFields = BeanUtil.deepCopyList(erpObjectFieldEntities, ErpObjectFieldEntity.class);
        for (ErpObjectFieldEntity objectFieldEntity : newFields) {
            objectFieldEntity.setTenantId(targetTenantId);
            objectFieldEntity.setDataCenterId(targetDcId);
            objectFieldEntity.setErpObjectApiName(objectFieldEntity.getErpObjectApiName()+suffixName);
            objectFieldEntity.setId(idGenerator.get());
            if(ErpFieldTypeEnum.object_reference.equals(objectFieldEntity.getFieldDefineType())
            ||ErpFieldTypeEnum.master_detail.equals(objectFieldEntity.getFieldDefineType())){//查找关联或者主从,关联对象apiName增加后缀
                objectFieldEntity.setFieldExtendValue(objectFieldEntity.getFieldExtendValue()+suffixName);
            }
            objectFieldEntity.setCreateTime(now);
            if (ErpFieldTypeEnum.select_one.equals(objectFieldEntity.getFieldDefineType())) {
                objectFieldEntity.setFieldExtendValue("[]");
            }
        }
        //分批插入
        List<List<ErpObjectFieldEntity>> split = ListUtil.split(newFields, 100);
        for (List<ErpObjectFieldEntity> splitList : split) {
            erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(targetTenantId)).batchInsert(splitList);
        }
    }

    public void copyFieldExtendByEi(String originalTenantId, String targetTenantId,String targetDcId, String objApiName) {
        //因为K3的虚拟对象主对象增加了.BillHead结尾，所以需要移除。
        objApiName = StringUtils.removeEndIgnoreCase(objApiName, ".BillHead");
        Long now = System.currentTimeMillis();
        //复制字段扩展
        ErpFieldExtendEntity queryFieldExtend = new ErpFieldExtendEntity();
        queryFieldExtend.setTenantId(originalTenantId);
        if (objApiName != null) {
            queryFieldExtend.setObjApiName(objApiName);
        }
        List<ErpFieldExtendEntity> erpFieldExtendEntities = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(originalTenantId)).queryList(queryFieldExtend);
        List<ErpFieldExtendEntity> newExtents = BeanUtil.deepCopyList(erpFieldExtendEntities, ErpFieldExtendEntity.class);
        for (ErpFieldExtendEntity newExtent : newExtents) {
            newExtent.setTenantId(targetTenantId);
            newExtent.setId(com.fxiaoke.api.IdGenerator.get());
            newExtent.setDataCenterId(targetDcId);
            newExtent.setCreateTime(now);
            newExtent.setUpdateTime(now);
        }
        List<List<ErpFieldExtendEntity>> split = ListUtil.split(newExtents, 100);
        for (List<ErpFieldExtendEntity> fieldExtendEntities : split) {
            erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(targetTenantId)).batchInsert(fieldExtendEntities);
        }
    }

    public void deleteFieldExtend(String tenantId,String dataCenterId,String objApiName,String fieldApiName){
        erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteFieldExtendOne(tenantId,dataCenterId, objApiName, fieldApiName);
    }

    public void deleteField(String tenantId,String dataCenterId, String objectApiName) {
        erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByObjApiName(tenantId, objectApiName,dataCenterId);
        //因为K3的虚拟对象主对象增加了.BillHead结尾，所以需要移除。
        String realObjApiName = StringUtils.removeEndIgnoreCase(objectApiName, ".BillHead");
        erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByTenantIdAndObjApiName(tenantId,dataCenterId, realObjApiName);
    }

    /**
     * 从某个企业复制配置到另一个企业
     * 后期优化批量插入
     *
     * @param initErpObjectFieldsArg
     */
    public void copyByEi(InitErpObjectFieldsArg initErpObjectFieldsArg,String suffixName) {
        String defaultTenantId = StringUtils.defaultIfBlank(initErpObjectFieldsArg.getDefaultTenantId(), ConfigCenter.STORE_DEFAULT_K3_OBJ_TENANT_ID);
        String targetTenantId = initErpObjectFieldsArg.getTargetTenantId();
        String objApiName = initErpObjectFieldsArg.getObjApiName();
        String targetDcId=initErpObjectFieldsArg.getDataCenterId();
        if (initErpObjectFieldsArg.getNeedRemoveFirst()) {
            erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(targetTenantId)).deleteByObjApiName(targetTenantId, objApiName+suffixName,targetDcId);
            if("".equals(suffixName)){//当suffixName为""时，只有一个数据中心，这是才需要清除字段扩展表
                //因为K3的虚拟对象主对象增加了.BillHead结尾，所以需要移除。
                String realObjApiName = objApiName.split(".BillHead")[0];
                erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(targetTenantId))
                        .deleteByTenantIdAndObjApiName(targetTenantId,targetDcId, realObjApiName);
            }
        }
        copyFieldByEi(defaultTenantId, targetTenantId,targetDcId, objApiName,suffixName,initErpObjectFieldsArg.getChannel());
        //extend表有dcId,必须复制
        copyFieldExtendByEi(defaultTenantId, targetTenantId, targetDcId, objApiName);
    }

    /**
     * 不一定是主属性字段 <p/>
     * 当主键字段不是真实Id时，则返回主键字段
     * 方主键字段是真实id是，返回e12字段,当e12字段为空时，也返回主键字段
     *
     * @param objApiName 真实对象apiName
     */
    @LogLevel(LogLevelEnum.DEBUG)
    @Cached(expire = 30, cacheType = CacheType.LOCAL)
    public ErpFieldExtendEntity queryNumFieldExtend(String tenantId, String dataCenterId, String objApiName) {
        ErpFieldExtendEntity idFieldExtend = findIdFieldExtend(tenantId, dataCenterId, objApiName);
        if (StrUtil.equalsIgnoreCase(idFieldExtend.getViewCode(), "id")) {
            ErpFieldExtendEntity numField = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .getNumFieldByObjApiName(tenantId, dataCenterId, objApiName);
            if (numField != null) {
                return numField;
            }
        }
        return idFieldExtend;
    }

    /**
     * 查找id字段，只能且必返回一条数据
     * 有缓存，页面请求请勿调用。这句注释根本没人会关注。。。
     *
     * @param tenantId
     * @param erpSplitObjectApiName
     * @return
     */
    @LogLevel(LogLevelEnum.DEBUG)
    @Cached(expire = 120,cacheType = CacheType.LOCAL,localLimit = 3000)
    public ErpObjectFieldEntity findIdField(String tenantId, String erpSplitObjectApiName){
        return erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findIdField(tenantId,erpSplitObjectApiName);
    }

    public ErpObjectFieldEntity findIdField2(String tenantId, String dataCenterId, String erpSplitObjectApiName){
        return erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findIdField2(tenantId, dataCenterId, erpSplitObjectApiName);
    }

    /**
     * 根据ERP真实对象apiName找到对应的主对象id字段entity
     * @param tenantId
     * @param dataCenterId
     * @param erpRealObjApiName
     * @return
     */
    public ErpObjectFieldEntity findMasterIdField(String tenantId, String dataCenterId, String erpRealObjApiName) {
        String masterSplitObjApiName = erpObjManager.getMasterSplitObjApiName(tenantId, dataCenterId, erpRealObjApiName);
        ErpObjectFieldEntity idField = findIdField2(tenantId, dataCenterId, masterSplitObjApiName);
        return idField;
    }

    /**
     * 查找id扩展字段，有缓存
     *
     * @param objectApiName 真实对象apiname
     */
    @Cached(expire = 120, cacheType = CacheType.LOCAL, localLimit = 3000)
    public ErpFieldExtendEntity findIdFieldExtendCache(final String tenantId, final String dataCenterId, final String objectApiName) {
        return erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findByDefineType(tenantId, dataCenterId, objectApiName, ErpFieldTypeEnum.id).stream().findFirst().orElse(null);
    }

    /**
     * 查找id扩展字段,无缓存
     *
     * @param objectApiName 真实对象apiname
     */
    public ErpFieldExtendEntity findIdFieldExtend(final String tenantId, final String dataCenterId, final String objectApiName) {
        return erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findByDefineType(tenantId, dataCenterId, objectApiName, ErpFieldTypeEnum.id).stream().findFirst().orElse(null);
    }

    /**
     * 查找主从字段，
     *
     * @param tenantId
     * @param erpObjectApiName
     * @return
     */
    @LogLevel(LogLevelEnum.DEBUG)
    @Cached(expire = 120, cacheType = CacheType.LOCAL, localLimit = 3000)
    public ErpObjectFieldEntity findMasterDetailField(String tenantId, String erpObjectApiName, String extendObjectApiName) {
        List<ErpObjectFieldEntity> masterDetailFields = erpObjectFieldDao.queryMasterDetailField(tenantId, erpObjectApiName, extendObjectApiName);
        if (masterDetailFields.isEmpty()) {
            log.warn("not found master detail,{},{},{}", tenantId, erpObjectApiName, extendObjectApiName);
            return null;
        }
        if (masterDetailFields.size() > 1) {
            log.warn("found more than one master detail field,{}", masterDetailFields);
        }
        return masterDetailFields.get(0);
    }

    public void checkK3detailField(String tenantId,String dcId,String realApiName){
        //查找对象
        List<ErpObjectEntity> erpObjectEntities = erpObjectDao.queryByRealObjApiName(tenantId, dcId, realApiName);
        //只允许一个批次，extendValue中为允许的detail字段
        Set<String> validDetailField = erpObjectEntities.stream().map(v -> v.getErpObjectExtendValue()).filter(v -> StrUtil.isNotBlank(v)).collect(Collectors.toSet());
        //删掉fieldExtend，field删不删也无所谓了。
        ErpFieldExtendEntity query = new ErpFieldExtendEntity();
        query.setTenantId(tenantId);
        query.setObjApiName(realApiName);
        query.setFieldDefineType(ErpFieldTypeEnum.detail);
        List<ErpFieldExtendEntity> existDetailField = erpFieldExtendDao.queryList(query);
        for (ErpFieldExtendEntity detailField : existDetailField) {
            if (!validDetailField.contains(detailField.getFieldApiName())){
                deleteFieldExtend(tenantId,dcId,realApiName,detailField.getFieldApiName());
                log.info("delete detail field,{}",detailField);
            }
        }
    }

    public ErpObjectFieldEntity findByFieldApiName(String dcId, String tenantId, String objApiName, String fieldApiName) {
        ErpObjectFieldEntity query=new ErpObjectFieldEntity();
        query.setDataCenterId(dcId);
        query.setTenantId(tenantId);
        query.setErpObjectApiName(objApiName);
        query.setFieldApiName(fieldApiName);
        List<ErpObjectFieldEntity> list=erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
        if(CollectionUtils.isNotEmpty(list)){
            return list.get(0);
        }
        return null;
    }

    /**
     * 增量添加字段,只加不改
     */
    public void incrementalInsertErpObjectField(final String tenantId, final String dataCenterId, final String objectApiName, final List<ErpObjectFieldEntity> formMeatData) {
        ErpObjectFieldEntity fieldEntity = new ErpObjectFieldEntity();
        fieldEntity.setTenantId(tenantId);
        fieldEntity.setDataCenterId(dataCenterId);
        fieldEntity.setChannel(ErpChannelEnum.ERP_LINKEDIN);
        fieldEntity.setErpObjectApiName(objectApiName);
        final List<ErpObjectFieldEntity> erpObjectFieldEntities = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(fieldEntity);
        final Set<String> collect = erpObjectFieldEntities.stream()
                .map(ErpObjectFieldEntity::getFieldApiName)
                .collect(Collectors.toSet());

        final List<ErpObjectFieldEntity> add = formMeatData.stream()
                .filter(erpObjectFieldEntity -> !collect.contains(erpObjectFieldEntity.getFieldApiName()))
                .collect(Collectors.toList());

        erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).batchInsert(add);
    }
}
