<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpAlarmRuleDao">
  <delete id="deleteByDataCenterId">
    delete from erp_alarm_rule
    where tenant_id=#{tenantId}
    and data_center_id=#{dataCenterId}
  </delete>

  <select id="findData" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpAlarmRuleEntity">
    select * from erp_alarm_rule
    where tenant_id=#{tenantId}
    <if test="dataCenterId != null and dataCenterId != ''">
      and data_center_id=#{dataCenterId}
    </if>
    <if test="alarmRuleType != null">
      and alarm_rule_type=#{alarmRuleType}
    </if>
    <if test="alarmType != null">
      and alarm_type=#{alarmType}
    </if>
     order by update_time asc
    <if test="limit != null and offset != null">
      limit #{limit} offset #{offset}
    </if>
  </select>

  <select id="count" resultType="java.lang.Integer">
    select count(*) from erp_alarm_rule
    where tenant_id=#{tenantId}
    <if test="dataCenterId != null and dataCenterId != ''">
      and data_center_id=#{dataCenterId}
    </if>
    <if test="alarmRuleType != null">
      and alarm_rule_type=#{alarmRuleType}
    </if>
    <if test="alarmType != null">
      and alarm_type=#{alarmType}
    </if>
  </select>
</mapper>