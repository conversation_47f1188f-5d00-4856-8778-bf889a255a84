<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjGroovyDao">
    <sql id="Base_Column_List">
        id,
        tenant_id,
        data_center_id,
        obj_api_name,
        url,
        func_api_name,
        groovy_script,
        create_time,
        update_time
    </sql>
    <resultMap id="BaseResultMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjGroovyEntity">
        <!--@mbg.generated-->
        <!--@Table erp_obj_groovy-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="data_center_id" jdbcType="VARCHAR" property="dataCenterId"/>
        <result column="obj_api_name" jdbcType="VARCHAR" property="objApiName"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="func_api_name" jdbcType="VARCHAR" property="funcApiName"/>
        <result column="groovy_script" jdbcType="LONGVARCHAR" property="groovyScript"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
    </resultMap>

    <select id="getByTenantIdAndApiNameAndUrl" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_obj_groovy
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        and data_center_id=#{dcId,jdbcType=VARCHAR}
        and obj_api_name=#{apiName,jdbcType=VARCHAR}
        and url=#{url,jdbcType=VARCHAR}
    </select>

    <select id="queryByObjectApiName"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjGroovyEntity">
        select
        <include refid="Base_Column_List"/>
        from erp_obj_groovy
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        and data_center_id=#{dcId,jdbcType=VARCHAR}
        and obj_api_name in
        <foreach item="item" index="index" collection="objApiNameList"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="queryGroovyTenantId"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjGroovyEntity">
        select
        <include refid="Base_Column_List"/>
        from erp_obj_groovy
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
    </select>

    <!--auto generated by MybatisCodeHelper on 2022-03-17-->
    <delete id="deleteByTenantId">
        delete from erp_obj_groovy
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
    </delete>


    <select id="queryByObjectApiNames2"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjGroovyEntity">
        select
        <include refid="Base_Column_List"/>
        from erp_obj_groovy
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        and data_center_id=#{dcId,jdbcType=VARCHAR}
        <if test="objApiNameList != null and objApiNameList.size() != 0">
            and obj_api_name in
            <foreach item="item" index="index" collection="objApiNameList"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
</mapper>