<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao">
    <resultMap id="BaseResultMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity">
        <!--@mbg.generated-->
        <!--@Table erp_object-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="data_center_id" jdbcType="VARCHAR" property="dataCenterId"/>
        <result column="channel" jdbcType="VARCHAR" property="channel"/>
        <result column="erp_object_type" jdbcType="VARCHAR" property="erpObjectType"/>
        <result column="erp_object_apiname" jdbcType="VARCHAR" property="erpObjectApiName"/>
        <result column="erp_object_name" jdbcType="VARCHAR" property="erpObjectName"/>
        <result column="erp_object_extend_value" jdbcType="LONGVARCHAR" property="erpObjectExtendValue"/>
        <result column="delete_status" jdbcType="BOOLEAN" property="deleteStatus"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, tenant_id, channel, erp_object_type, erp_object_apiname, erp_object_name,
        data_center_id,erp_object_extend_value,delete_status, create_time, update_time
    </sql>

<!--auto generated by MybatisCodeHelper on 2020-09-11-->
    <select id="queryByApiNames" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_object
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        and erp_object_apiname in
        <foreach item="item" index="index" collection="erpObjectApiNameCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

<!--auto generated by MybatisCodeHelper on 2020-12-22-->
    <delete id="deleteByTenantIdAndErpObjectApiName">
        delete from erp_object
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        and erp_object_apiname=#{erpObjectApiName,jdbcType=VARCHAR}
        and data_center_id=#{dataCenterId,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteByTenantIdAndDcId">
        delete from erp_object
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        and data_center_id=#{dataCenterId,jdbcType=VARCHAR}
    </delete>

<!--auto generated by MybatisCodeHelper on 2021-03-15-->
    <select id="getByObjApiName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_object
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        <if test="dcId != null and dcId != ''">
            and data_center_id = #{dcId}
        </if>
        and erp_object_apiname=#{erpObjectApiName,jdbcType=VARCHAR}
        limit 1;
    </select>

    <delete id="deleteByTenantId">
        delete from erp_object
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
    </delete>

<!--auto generated by MybatisCodeHelper on 2022-11-29-->
    <select id="listByTenantId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_object
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
    </select>
    <select id="queryByRealObjApiName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_object
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        and data_center_id = #{dcId}
        and erp_object_apiname in
        (
        select erp_split_object_apiname
        from erp_object_relationship
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        and data_center_id = #{dcId} and erp_actual_object_apiname = #{realObjApiName} )
    </select>

    <select id="queryByApiNames2" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_object
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        and data_center_id = #{dcId}
        and erp_object_apiname in
        <foreach item="item" index="index" collection="erpObjectApiNameCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="queryAllObjExtendDTOByDc" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpObjExtendDto">
        select rel.erp_actual_object_apiname realObjApiName,
               obj.erp_object_apiname        splitObjApiName,
               obj.erp_object_name           objName,
               obj.erp_object_extend_value   extentValue,
               rel.split_type                splitType,
               rel.split_seq                 splitSeq
        from erp_object_relationship rel
                 left join erp_object obj
                           on rel.tenant_id = obj.tenant_id
                               and rel.data_center_id = obj.data_center_id
                               and rel.erp_split_object_apiname = obj.erp_object_apiname
        where rel.tenant_id = #{tenantId}
        <if test="dcId != null and dcId != ''">
            and rel.data_center_id = #{dcId}
        </if>
    </select>

<!--auto generated by MybatisCodeHelper on 2023-04-11-->
    <select id="listByDcId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_object
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        <if test="dataCenterId != null and dataCenterId != ''">
            and data_center_id=#{dataCenterId,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="listErpNotSplitObjByDcIds" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity">
        select
        <include refid="Base_Column_List"/>
        from erp_object
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        and erp_object_apiname in
        (
        select erp_split_object_apiname
        from erp_object_relationship
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        <if test="dataCenterIds != null and dataCenterIds.size > 0">
            and data_center_id in
            <foreach item="item" index="index" collection="dataCenterIds"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        and split_type = 'NOT_SPLIT'
        )
    </select>
</mapper>