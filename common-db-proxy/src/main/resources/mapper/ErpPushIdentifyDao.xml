<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpPushIdentifyDao">
  <resultMap id="BaseResultMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpPushIdentifyEntity">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="token" jdbcType="VARCHAR" property="token" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id,tenant_id,object_api_name,operation_type,version,token,operation_type,create_time,updateTime
  </sql>

  <select id="findByTokenByTenantIdAndVersion" resultMap="BaseResultMap">
    select * from erp_push_identify t where t.tenant_id=#{tenantId} and t.version=#{version}
  </select>
  <select id="findByTokenByTenantId" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpPushIdentifyEntity">
    select * from erp_push_identify t where t.tenant_id=#{tenantId}
  </select>

  <select id="getByTenantId" resultMap="BaseResultMap">
    select *
    from erp_push_identify t
    where t.tenant_id = #{tenantId}
    limit 1;
  </select>
</mapper>