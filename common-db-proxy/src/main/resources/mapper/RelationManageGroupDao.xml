<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.RelationManageGroupDao">
    <delete id="deleteByTenantIdAndId">
        DELETE FROM relation_manage_group
        WHERE tenant_id = #{tenantId,jdbcType=VARCHAR}
          and id = #{id,jdbcType=VARCHAR}
    </delete>

    <select id="getAllTemplateIds" resultType="java.lang.String">
        SELECT DISTINCT template_id
        FROM relation_manage_group
    </select>

    <select id="queryAllByTenantId"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.RelationManageGroupEntity">
        SELECT *
        FROM relation_manage_group
        WHERE tenant_id = #{tenantId,jdbcType=VARCHAR}
    </select>
    <select id="queryAllByTemplateId"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.RelationManageGroupEntity">
        SELECT *
        FROM relation_manage_group
        WHERE template_id = #{tenantId,jdbcType=VARCHAR}
    </select>
    <select id="getById" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.RelationManageGroupEntity">
        SELECT *
        FROM relation_manage_group
        WHERE tenant_id = #{tenantId,jdbcType=VARCHAR}
          and id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="isTemplateId" resultType="java.lang.Boolean">
        SELECT EXISTS(
            select 1
            from relation_manage_group
            where template_id = #{tenantId,jdbcType=VARCHAR}
        )
    </select>
    <select id="getUpStreamIdByTemplateId" resultType="java.lang.String">
        select tenant_id
        from relation_manage_group
        where template_id = #{tenantId,jdbcType=VARCHAR}
        limit 1;
    </select>
    <select id="queryByTemplateIdAndDcId"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.RelationManageGroupEntity">
        select *
        from relation_manage_group
        where template_id = #{tenantId,jdbcType=VARCHAR}
          and dc_id = #{dcId,jdbcType=VARCHAR}
    </select>
    <select id="queryByTenantIdAndDcId"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.RelationManageGroupEntity">
        select *
        from relation_manage_group
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
          and dc_id = #{dcId,jdbcType=VARCHAR}
    </select>

    <update id="refreshUpdateTime">
        update relation_manage_group
        set update_time = (SELECT EXTRACT(epoch FROM now()) * 1000)
        where template_id = #{templateId}
          and id = #{id}
    </update>
</mapper>