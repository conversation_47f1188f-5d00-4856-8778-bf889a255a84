<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.OAConnectInfoDao">
    <sql id="Base_Column_List">
        id,
        tenant_id,
        enterprise_name,
        connect_params,
        create_time,
        update_time
    </sql>
    <resultMap id="BaseResultMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.OAConnectInfoEntity">
        <!--@mbg.generated-->
        <!--@Table erp_connect_info-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="enterprise_name" jdbcType="VARCHAR" property="enterpriseName"/>
        <result column="connect_params" jdbcType="LONGVARCHAR" property="connectParams"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
    </resultMap>

    <!--auto generated by MybatisCodeHelper on 2020-08-31-->
    <select id="getByTenantId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from oa_connect_info
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
    </select>

    <select id="listTenantId" resultType="java.lang.String">
        select tenant_id from oa_connect_info
    </select>

    <select id="getByTenantIdWithCache" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from oa_connect_info
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
    </select>
</mapper>