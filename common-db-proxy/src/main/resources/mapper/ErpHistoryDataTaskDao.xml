<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpHistoryDataTaskDao">
    <resultMap id="BaseResultMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity">
        <!--@mbg.generated-->
        <!--@Table erp_history_data_task-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="task_type" jdbcType="INTEGER" property="taskType"/>
        <result column="task_num" jdbcType="VARCHAR" property="taskNum"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <result column="obj_api_name" jdbcType="VARCHAR" property="objApiName"/>
        <result column="real_obj_api_name" jdbcType="VARCHAR" property="realObjApiName"/>
        <result column="data_ids" jdbcType="LONGVARCHAR" property="dataIds"/>
        <result column="start_time" jdbcType="BIGINT" property="startTime"/>
        <result column="last_query_start_time" jdbcType="BIGINT" property="lastQueryStartTime"/>
        <result column="end_time" jdbcType="BIGINT" property="endTime"/>
        <result column="need_stop" jdbcType="BOOLEAN" property="needStop"/>
        <result column="limit" jdbcType="BIGINT" property="limit"/>
        <result column="offset" jdbcType="BIGINT" property="offset"/>
        <result column="task_status" jdbcType="INTEGER" property="taskStatus"/>
        <result column="total_data_size" jdbcType="BIGINT" property="totalDataSize"/>
        <result column="total_cost_time" jdbcType="BIGINT" property="totalCostTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="trace_id" jdbcType="VARCHAR" property="traceId"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="creator" jdbcType="INTEGER" property="creator"/>
        <result column="execute_time" property="executeTime"/>
        <result column="related_ploy_detail_id" property="relatedPloyDetailId"/>
        <result column="splitting_interval_ms" property="splittingIntervalMs"/>
        <result column="priority" property="priority"/>
    </resultMap>
    <update id="updateRemarkAndStopById">
                        update erp_history_data_task
                        set remark=#{remark},
                        need_stop=#{needStop}
                        where id=#{id}
    </update>
    <delete id="deleteByDataCenterId">
        delete from erp_history_data_task
        where data_center_id=#{dataCenterId}
        and tenant_id=#{tenantId}
    </delete>
    <select id="listByStatus" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity">
        select * from erp_history_data_task
        where tenant_id=#{tenantId}
        <if test="status!=null">
            and task_status in
            <foreach item="item" index="index" collection="status"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>


    <select id="countByStatus" resultType="Integer">
        select count(1) from erp_history_data_task
        where tenant_id=#{tenantId}
        <if test="status!=null">
            and task_status in
            <foreach item="item" index="index" collection="status"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="listByRealObj"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity">
        select *
        from erp_history_data_task
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        and real_obj_api_name = #{erpRealObjApiName}
        and data_center_id=#{dataCenterId}
        order by update_time desc
    </select>


    <select id="viewAllListByDcIdAndActualObjStatus"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity">
        select *
        from erp_history_data_task
        where  tenant_id = #{tenantId,jdbcType=VARCHAR}
        <if test="dataCenterIds !=null and dataCenterIds.size > 0">
            and data_center_id in
            <foreach item="item" index="index" collection="dataCenterIds"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="erpObjApiNames !=null and erpObjApiNames.size > 0">
            and real_obj_api_name in
            <foreach item="item" index="index" collection="erpObjApiNames"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="status!=null">
            and task_status in
            <foreach item="item" index="index" collection="status"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="taskName !=null">
            and task_name like #{taskName}
        </if>
        <choose>
            <when test="sequence != null and sequence ==1">
                order by update_time desc
            </when>
            <when test="sequence != null and sequence ==2">
                order by create_time
            </when>
            <when test="sequence != null and sequence ==3">
                order by execute_time
            </when>
            <otherwise>
                order by update_time desc
            </otherwise>
        </choose>
        offset #{offset} limit #{limit}
    </select>
    <select id="viewAllCountByDcIdAndActualObjStatus" resultType="java.lang.Integer">
        select count(*)
        from erp_history_data_task
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        <if test="dataCenterIds !=null and dataCenterIds.size > 0">
            and data_center_id in
            <foreach item="item" index="index" collection="dataCenterIds"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="erpObjApiNames !=null and erpObjApiNames.size > 0">
            and real_obj_api_name in
            <foreach item="item" index="index" collection="erpObjApiNames"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="status!=null">
            and task_status in
            <foreach item="item" index="index" collection="status"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="taskName !=null">
            and task_name like #{taskName}
        </if>

    </select>
    <select id="listBySplitObj"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity">
        select *
        from erp_history_data_task
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        <if test="erpSplitObjApiNames!=null and erpSplitObjApiNames.size > 0">
            and obj_api_name in
            <foreach item="item" index="index" collection="erpSplitObjApiNames"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="listByIds" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity">
        select *
        from erp_history_data_task
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        <if test="taskIds!=null and taskIds.size > 0">
            and id in
            <foreach item="item" index="index" collection="taskIds"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="listByDataCenterId"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity">
        select *
        from erp_history_data_task
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        and data_center_id=#{dataCenterId}
    </select>
    <update id="stopTask">
        update erp_history_data_task
        set need_stop=#{needStop}
        where tenant_id=#{tenantId}
        and id=#{id}
    </update>

<!--auto generated by MybatisCodeHelper on 2023-05-18-->
    <update id="updateById">
        update erp_history_data_task
        <include refid="updateEntitySql"></include>
        where id=#{updated.id,jdbcType=VARCHAR}
    </update>

    <sql id="updateEntitySql">
        <set>
            <if test="updated.tenantId != null">
                tenant_id = #{updated.tenantId,jdbcType=VARCHAR},
            </if>
            <if test="updated.taskType != null">
                task_type = #{updated.taskType,jdbcType=INTEGER},
            </if>
            <if test="updated.taskNum != null">
                task_num = #{updated.taskNum,jdbcType=VARCHAR},
            </if>
            <if test="updated.taskName != null">
                task_name = #{updated.taskName,jdbcType=VARCHAR},
            </if>
            <if test="updated.objApiName != null">
                obj_api_name = #{updated.objApiName,jdbcType=VARCHAR},
            </if>
            <if test="updated.realObjApiName != null">
                real_obj_api_name = #{updated.realObjApiName,jdbcType=VARCHAR},
            </if>
            <if test="updated.dataIds != null">
                data_ids = #{updated.dataIds,jdbcType=LONGVARCHAR},
            </if>
            <if test="updated.filterString != null">
                filter_string = #{updated.filterString,jdbcType=VARCHAR},
            </if>
            <if test="updated.startTime != null">
                start_time = #{updated.startTime,jdbcType=BIGINT},
            </if>
            <if test="updated.lastQueryStartTime != null">
                last_query_start_time = #{updated.lastQueryStartTime,jdbcType=BIGINT},
            </if>
            <if test="updated.endTime != null">
                end_time = #{updated.endTime,jdbcType=BIGINT},
            </if>
            <if test="updated.needStop != null">
                need_stop = #{updated.needStop,jdbcType=BOOLEAN},
            </if>
            <if test="updated.limit != null">
                "limit" = #{updated.limit,jdbcType=BIGINT},
            </if>
            <if test="updated.offset != null">
                "offset" = #{updated.offset,jdbcType=BIGINT},
            </if>
            <if test="updated.taskStatus != null">
                task_status = #{updated.taskStatus,jdbcType=INTEGER},
            </if>
            <if test="updated.totalDataSize != null">
                total_data_size = #{updated.totalDataSize,jdbcType=BIGINT},
            </if>
            <if test="updated.totalCostTime != null">
                total_cost_time = #{updated.totalCostTime,jdbcType=BIGINT},
            </if>
            <if test="updated.remark != null">
                remark = #{updated.remark,jdbcType=VARCHAR},
            </if>
            <if test="updated.traceId != null">
                trace_id = #{updated.traceId,jdbcType=VARCHAR},
            </if>
            <if test="updated.createTime != null">
                create_time = #{updated.createTime,jdbcType=BIGINT},
            </if>
            <if test="updated.updateTime != null">
                update_time = #{updated.updateTime,jdbcType=BIGINT},
            </if>
            <if test="updated.creator != null">
                creator = #{updated.creator,jdbcType=INTEGER},
            </if>
            <if test="updated.executeTime != null">
                execute_time = #{updated.executeTime},
            </if>
            <if test="updated.dataCenterId != null">
                data_center_id = #{updated.dataCenterId},
            </if>
            <if test="updated.relatedPloyDetailId != null">
                related_ploy_detail_id = #{updated.relatedPloyDetailId },
            </if>
            <if test="updated.splittingIntervalMs != null">
                splitting_interval_ms = #{updated.splittingIntervalMs},
            </if>
            <if test="updated.priority != null">
                priority = #{updated.priority},
            </if>
        </set>
    </sql>


    <update id="updateStatusByIdsAndOldStatus">
        update erp_history_data_task
        set task_status=#{newStatus},
        update_time=#{updateTime}
        where id =#{taskId}
        and task_status=#{oldStatus}
    </update>

    <update id="updateEntityById">
        update erp_history_data_task
        <include refid="updateEntitySql"></include>
        where id=#{updated.id,jdbcType=VARCHAR}
    </update>
    <update id="updateTaskDataCenterId">
        update erp_history_data_task
        set data_center_id=#{dataCenterId},
        related_ploy_detail_id=#{relatedPloyDetailId}
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        and obj_api_name= #{erpSplitObjApiName,jdbcType=VARCHAR}

    </update>
    <update id="restartEntity">
        update erp_history_data_task
        set  start_time = #{updated.startTime,jdbcType=BIGINT},
        last_query_start_time = #{updated.lastQueryStartTime,jdbcType=BIGINT},
        end_time = #{updated.endTime,jdbcType=BIGINT},
        need_stop = #{updated.needStop,jdbcType=BOOLEAN},
        "limit" = #{updated.limit,jdbcType=BIGINT},
        "offset" = #{updated.offset,jdbcType=BIGINT},
        task_status = #{updated.taskStatus,jdbcType=INTEGER},
        task_num = #{updated.taskNum,jdbcType=VARCHAR},
        total_data_size = #{updated.totalDataSize,jdbcType=BIGINT},
        total_cost_time = #{updated.totalCostTime,jdbcType=BIGINT},
        execute_time = #{updated.executeTime},
        update_time = #{updated.updateTime,jdbcType=BIGINT}
        where id=#{updated.id,jdbcType=VARCHAR}
    </update>
    <select id="countByDcIdAndUpdateTime" resultType="java.lang.Integer">
        select count(id)
        from erp_history_data_task
        where
        tenant_id=#{tenantId,jdbcType=VARCHAR}
        and data_center_id=#{dataCenterId}
        and update_time>#{deadLineTime}
    </select>
    <select id="listByTaskIds" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity">
        select *
        from erp_history_data_task
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        <if test="taskIds!=null and taskIds.size > 0">
            and id in
            <foreach item="item" index="index" collection="taskIds"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
</mapper>