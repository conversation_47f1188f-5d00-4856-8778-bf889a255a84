<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpSyncTimeDao">
    <resultMap id="BaseResultMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpSyncTimeEntity">
        <!--@mbg.generated-->
        <!--@Table erp_sync_time-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="object_api_name" jdbcType="VARCHAR" property="objectApiName"/>
        <result column="operation_type" jdbcType="SMALLINT" property="operationType"/>
        <result column="last_sync_time" jdbcType="BIGINT" property="lastSyncTime"/>
        <result column="last_query_mongo_time" jdbcType="BIGINT" property="lastQueryMongoTime"/>
        <result column="priority" jdbcType="INTEGER" property="priority"/>
        <result column="polling_interval" jdbcType="LONGVARCHAR" property="pollingInterval"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, tenant_id, object_api_name, operation_type, last_sync_time,last_query_mongo_time, priority,polling_interval,
        create_time,
        update_time
    </sql>


    <!--auto generated by MybatisCodeHelper on 2020-08-22-->
    <select id="listSyncExtentByTenantId" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpSyncExtentDTO">
        select e.id,
               e.tenant_id,
               e.object_api_name,
               e.operation_type,
               e.last_sync_time,
               e.priority,
               e.polling_interval,
               e.create_time,
               e.update_time,
               e.last_query_mongo_time,
               d.id                    as snapshotId,
               d.sync_ploy_detail_data as syncPloyDetailData,
               d.create_time as snapshotCreateTime
        from erp_sync_time e
                     left join sync_ploy_detail_snapshot d
                on e.tenant_id = d.source_tenant_id and e.object_api_name = d.source_object_api_name
        where e.tenant_id = #{tenantId,jdbcType=VARCHAR}
          and d.sync_ploy_detail_data::jsonb ->> 'sourceTenantType' = '2'
          and d.status = 1
        order by priority, create_time;
    </select>

    <!--auto generated by MybatisCodeHelper on 2021-12-08-->
    <select id="getByUnique" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_sync_time
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
          and object_api_name = #{objectApiName,jdbcType=VARCHAR}
          and operation_type = #{operationType,jdbcType=SMALLINT}
    </select>

<!--auto generated by MybatisCodeHelper on 2021-12-08-->
    <delete id="deleteByTenantIdAndId">
        delete from erp_sync_time
        where tenant_id=#{tenantId,jdbcType=VARCHAR} and id=#{id,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteByTenantId">
        delete from erp_sync_time
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteByTenantIdAndObjectApiName">
        delete from erp_sync_time
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        and object_api_name in
        <foreach item="item" index="index" collection="objectApiNames" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <!--auto generated by MybatisCodeHelper on 2022-07-11-->
    <select id="listByTenantIdAndObjectApiName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_sync_time
        where tenant_id=#{tenantId,jdbcType=VARCHAR} and object_api_name=#{objectApiName,jdbcType=VARCHAR}
    </select>

    <select id="batchListByTenantIdAndObjectApiName"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpSyncTimeEntity">
        select
        <include refid="Base_Column_List"/>
        from erp_sync_time
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
          and object_api_name in
        <foreach item="item" index="index" collection="objectApiNames" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <!--auto generated by MybatisCodeHelper on 2022-07-11-->
    <update id="updateLastQueryMongoTimeById">
        update erp_sync_time
        set last_query_mongo_time=#{updatedLastQueryMongoTime,jdbcType=BIGINT}
        where id=#{id,jdbcType=VARCHAR}
    </update>

<!--auto generated by MybatisCodeHelper on 2022-09-20-->
    <update id="updatePollingInterval">
        update erp_sync_time
        set polling_interval=#{updatedPollingInterval,jdbcType=LONGVARCHAR}
        where tenant_id=#{tenantId,jdbcType=VARCHAR} and id=#{id,jdbcType=VARCHAR}
    </update>
    <update id="updateType">
        update erp_sync_time
        set operation_type=#{type,jdbcType=LONGVARCHAR}
        where tenant_id=#{tenantId,jdbcType=VARCHAR} and id=#{id,jdbcType=VARCHAR}
    </update>

    <select id="listSyncExtent" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpSyncExtentDTO">
        select e.id,
               e.tenant_id,
               e.object_api_name,
               e.operation_type,
               e.last_sync_time,
               e.priority,
               e.polling_interval,
               e.create_time,
               e.update_time,
               e.last_query_mongo_time,
               d.id                    as snapshotId,
               d.sync_ploy_detail_data as syncPloyDetailData,
               d.create_time as snapshotCreateTime
        from erp_sync_time e
                 left join sync_ploy_detail_snapshot d
                           on e.object_api_name = d.source_object_api_name
        where e.tenant_id = #{tenantId,jdbcType=VARCHAR}
          and d.source_tenant_id = #{ployDetailTenantId,jdbcType=VARCHAR}
          and d.sync_ploy_detail_data::jsonb ->> 'sourceTenantType' = '2'
          and d.status = 1
        order by priority, create_time;
    </select>
</mapper>