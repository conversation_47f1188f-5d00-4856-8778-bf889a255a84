<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao">
    <resultMap id="BaseResultMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        <!--@mbg.generated-->
        <!--@Table sync_data_mappings-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="source_tenant_id" jdbcType="VARCHAR" property="sourceTenantId"/>
        <result column="source_object_api_name" jdbcType="VARCHAR" property="sourceObjectApiName"/>
        <result column="source_data_id" jdbcType="VARCHAR" property="sourceDataId"/>
        <result column="source_data_name" jdbcType="VARCHAR" property="sourceDataName"/>
        <result column="dest_object_api_name" jdbcType="VARCHAR" property="destObjectApiName"/>
        <result column="dest_tenant_id" jdbcType="VARCHAR" property="destTenantId"/>
        <result column="dest_data_name" jdbcType="VARCHAR" property="destDataName"/>
        <result column="last_sync_data_id" jdbcType="VARCHAR" property="lastSyncDataId"/>
        <result column="last_sync_status" jdbcType="SMALLINT" property="lastSyncStatus"/>
        <result column="last_source_data_vserion" jdbcType="BIGINT" property="lastSourceDataVserion"/>
        <result column="dest_data_id" jdbcType="VARCHAR" property="destDataId"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="is_created" jdbcType="BOOLEAN" property="isCreated"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="isDeleted"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="master_data_id" jdbcType="VARCHAR" property="masterDataId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        <!-- 不查询source_tenant_id和dest_tenant_id，但还会插入-->
        id,
        tenant_id,
        tenant_id as source_tenant_id,
        tenant_id as dest_tenant_id,
        source_object_api_name,
        source_data_id,
        source_data_name,
        dest_object_api_name,
        dest_data_name,
        last_sync_data_id,
        last_sync_status,
        last_source_data_vserion,
        dest_data_id,
        create_time,
        update_time,
        is_created,
        remark,
        is_deleted,
        master_data_id
    </sql>
    <sql id="fields_without_remark">
        <!--@mbg.generated-->
        <!-- 不查询remark-->
        id,
        tenant_id,
        tenant_id as source_tenant_id,
        tenant_id as dest_tenant_id,
        source_object_api_name,
        source_data_id,
        source_data_name,
        dest_object_api_name,
        dest_data_name,
        last_sync_data_id,
        last_sync_status,
        last_source_data_vserion,
        dest_data_id,
        create_time,
        update_time,
        is_created,
        is_deleted,
        master_data_id
    </sql>

    <resultMap
            id="mappingCreatedVo"
            type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.MappingCreatedData">
        <result column="source_data_id" property="sourceDataId"/>
        <result column="is_created" property="isCreated"/>
    </resultMap>

    <resultMap id="tableRowCountDto" type="com.fxiaoke.open.erpsyncdata.preprocess.model.TableRowCountDto">
        <result column="tableName" property="tableName"/>
        <result column="rowCount" property="rowCount"/>
    </resultMap>
    <delete id="truncateTable">
        truncate Table sync_data_mappings
    </delete>

    <select id="getByUninKey" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select <include refid="Base_Column_List"/>
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and source_object_api_name = #{sourceObjectApiName}
          and source_data_id = #{sourceDataId}
          and dest_object_api_name = #{destObjectApiName}
          AND is_deleted = false
    </select>

    <select id="getByUninKeyReverse" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select <include refid="Base_Column_List"/>
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and source_object_api_name = #{destObjectApiName}
          and dest_data_id = #{sourceDataId}
          and dest_object_api_name = #{sourceObjectApiName}
          AND is_deleted = false
    </select>

    <select id="getByUninKeyByDestId" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select <include refid="Base_Column_List"/>
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and source_object_api_name = #{sourceObjectApiName}
          and dest_data_id = #{destDataId}
          and dest_object_api_name = #{destObjectApiName}
          AND is_deleted = false
    </select>

    <update id="updateBySyncDataId">
        update sync_data_mappings
        set last_sync_status = #{syncStatus},
        <if test="destDataName != null">
            dest_data_name = #{destDataName},
        </if>
        <if test="remark != null">
            remark = #{remark},
        </if>
        update_time = #{updateTime}
        where tenant_id = #{tenantId}
          and last_sync_data_id = #{syncDataId}
          AND is_deleted = false
    </update>

    <update id="bulkUpdateBySyncDataId">
        update sync_data_mappings m
        <set>
            last_sync_status = v.last_sync_status,
            remark           = v.remark,
            update_time      = (SELECT EXTRACT(epoch FROM now()) * 1000),
            <if test="updateDestName">
                dest_data_name = v.dest_data_ame,
            </if>
        </set>
        from (
        values
        <foreach collection="args" separator="," item="item">
            (#{item.syncDataId}, #{item.lastSyncStatus}, #{item.destDataName}, #{item.remark})
        </foreach>
        ) as v(sync_data_id, last_sync_status, dest_data_ame, remark)
        where tenant_id = #{tenantId}
          and is_deleted = false
          and m.last_sync_data_id = v.sync_data_id;
    </update>

    <update id="bulkUpdateDestBySourceArgs">
        update sync_data_mappings m
        <set>
            is_created       = true,
            last_sync_status = v.last_sync_status,
            remark           = v.remark,
            update_time      = (SELECT EXTRACT(epoch FROM now()) * 1000),
            <if test="updateDestIdAndName">
                dest_data_name = v.dest_data_ame,
                dest_data_id   = v.dest_id,
            </if>
        </set>
        from (
        values
        <foreach collection="args" separator="," item="arg">
            (#{arg.sourceObjectApiName}, #{arg.destObjectApiName}, #{arg.sourceDataId}, #{arg.destDataId},
             #{arg.lastSyncStatus}, #{arg.destDataName}, #{arg.remark})
        </foreach>
        ) as v(src_obj, dest_obj, src_id, dest_id, last_sync_status, dest_data_ame, remark)
        where tenant_id = #{tenantId}
          and is_deleted = false
          and source_object_api_name = v.src_obj
          and dest_object_api_name = v.dest_obj
          and source_data_id = v.src_id
    </update>

    <update id="batchUpdateMasterDataIdBySyncDataIdList">
        update sync_data_mappings
        set master_data_id = #{masterDataId},
        update_time      = (SELECT EXTRACT(epoch FROM now()) * 1000)
        where tenant_id = #{tenantId}
          and last_sync_data_id
            in
        <foreach collection="syncDataIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND is_deleted = false
    </update>

    <update id="batchUpdateMasterDataId">
        update sync_data_mappings
        set master_data_id = #{masterDataId},
        update_time      = (SELECT EXTRACT(epoch FROM now()) * 1000)
        where tenant_id = #{tenantId}
        and id
        in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND is_deleted = false
    </update>

    <update id="updateById">
        update sync_data_mappings
        set last_sync_data_id        = #{lastSyncDataId},
        <if test="sourceDataName != null">
            source_data_name = #{sourceDataName},
        </if>
            last_source_data_vserion = #{lastSourceDataVserion},
            update_time              = #{updateTime},
            last_sync_status         = #{lastSyncStatus}
        where tenant_id = #{tenantId}
          and id = #{id}
    </update>

    <update id="updateCreatedById">
        update sync_data_mappings
        set is_created  = #{isCreated},
            update_time = (SELECT EXTRACT(epoch FROM now()) * 1000)
        where tenant_id = #{tenantId}
          and id = #{id}
    </update>


    <update id="updateByUniKey">
        update sync_data_mappings
        set is_created = #{isCreated},
        <if test="destDataId != null">
            dest_data_id = #{destDataId},
        </if>
        <if test="destDataName != null">
            dest_data_name = #{destDataName},
        </if>
        <if test="lastSyncStatus != null">
            last_sync_status = #{lastSyncStatus},
        </if>
        update_time = (SELECT EXTRACT(epoch FROM now()) * 1000)
        where tenant_id = #{tenantId}
          and source_object_api_name = #{sourceObjectApiName}
          and source_data_id = #{sourceDataId}
          and dest_object_api_name = #{destObjectApiName}
          AND is_deleted = false
    </update>


    <update id="updateLastSyncStatusById">
        update sync_data_mappings
        set update_time      = #{updateTime},
            last_sync_status = #{lastSyncStatus},
            remark           = #{remark}
        where tenant_id = #{tenantId}
          and id = #{id}
          AND is_deleted = false
    </update>

    <update id="mergeDestDataId">
        UPDATE sync_data_mappings
        SET dest_data_id=#{newDestDataId},
            update_time = #{updateTime}
        WHERE tenant_id = #{tenantId}
          and source_object_api_name = #{sourceObjectApiName}
          and dest_data_id = #{oldDestDataId}
          and dest_object_api_name = #{destObjectApiName}
          AND id IN
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
        AND is_deleted = false
    </update>

    <update id="mergeSourceDataId">
        UPDATE sync_data_mappings
        SET source_data_id=#{newSourceDataId},
            update_time = #{updateTime}
        WHERE tenant_id = #{tenantId}
          and source_object_api_name = #{sourceObjectApiName}
          and source_data_id = #{oldSourceDataId}
          and dest_object_api_name = #{destObjectApiName}
          AND id IN
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
        AND is_deleted = false
    </update>


    <select id="listByDestInfo" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select <include refid="Base_Column_List"/>
        from sync_data_mappings
        WHERE tenant_id = #{tenantId}
          and dest_object_api_name = #{destObjectApiName}
          AND dest_data_id IN
        <foreach collection="destDataIds" open="(" close=")" separator="," item="destDataId">
            #{destDataId}
        </foreach>
        AND is_deleted = false
    </select>

    <select id="listBySourceInfo" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select <include refid="Base_Column_List"/>
        from sync_data_mappings
        WHERE tenant_id = #{tenantId}
          and source_object_api_name = #{sourceObjectApiName}
          AND source_data_id IN
        <foreach collection="sourceDataIds" open="(" close=")" separator="," item="sourceDataId">
            #{sourceDataId}
        </foreach>
        AND is_deleted = false
    </select>
    <select id="findDetailByMasterId" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select <include refid="Base_Column_List"/>
        from sync_data_mappings
        WHERE tenant_id = #{tenantId}
        <if test="sourceDetailApiName != null">
            and source_object_api_name = #{sourceDetailApiName}
        </if>
        <if test="destDetailApiName != null">
            and dest_object_api_name = #{destDetailApiName}
        </if>
          and master_data_id = #{maserDataId}
        and is_deleted = false;
    </select>

    <delete id="deleteByTenantIdAndId">
        delete
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and id = #{id}
    </delete>

    <select id="listCreatedBySource" resultMap="mappingCreatedVo">
        select source_data_id, is_created
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and source_object_api_name = #{sourceObjectApiName}
          and dest_object_api_name = #{destObjectApiName}
          AND is_deleted = false
          and source_data_id in
        <foreach collection="sourceDataIdCollection" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </select>

    <!--auto generated by MybatisCodeHelper on 2022-11-01-->
    <!--不查询remark字段-->
    <select id="listByIds" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select <include refid="fields_without_remark"/>
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and id in
        <foreach item="item" index="index" collection="idCollection"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        and is_deleted = false
    </select>
    <select id="getCountFromPgClass" resultType="com.fxiaoke.open.erpsyncdata.preprocess.model.TableRowCountDto">
        select relname as tableName, reltuples as rowCount
        from pg_class
        where relkind = 'r'
          and relname = 'sync_data_mappings_' || #{tenantId}
        order by rowCount desc;
    </select>
    <!--10分钟超时-->
    <select id="countByTenantId"
            resultType="long"
            timeout="600">
        select count(1)
        from sync_data_mappings
    </select>
    <select id="getCountFromPgStatTable"
            resultType="com.fxiaoke.open.erpsyncdata.preprocess.model.TableRowCountDto">
        select relname as tableName, n_live_tup as rowCount
        from pg_stat_user_tables
        where relname = 'sync_data_mappings_' || #{tenantId}
          and (last_autovacuum > now() - interval '7 days' or last_vacuum > now() - interval '7 days');
    </select>
    <select id="pageTenantIdByMaxId"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select <include refid="Base_Column_List"/>
        from sync_data_mappings
        where tenant_id = #{tenantId}
        <if test="maxId != null and maxId != ''">
            and id &gt; #{maxId}
        </if>
        and is_deleted = false
        order by id asc
        limit #{pageSize}
    </select>
    <update id="updateSourceDataIdByIdIgnore">
        update sync_data_mappings
        set source_data_id = #{sourceDataId},
            update_time      = (SELECT EXTRACT(epoch FROM now()) * 1000)
        where tenant_id = #{tenantId}
          and id = #{id}
          AND is_deleted = false
    </update>
    <update id="updateDestDataIdByIdIgnore">
        update sync_data_mappings
        set dest_data_id = #{destDataId},
            update_time      = (SELECT EXTRACT(epoch FROM now()) * 1000)
        where tenant_id = #{tenantId}
          and id = #{id}
          AND is_deleted = false
    </update>

    <update id="updateMappingByIdIgnore">
        update sync_data_mappings
        set  tenant_id= #{tenantId,jdbcType=VARCHAR},
        <if test="sourceDataId!=null and sourceDataId!=''">
            source_data_id=#{sourceDataId},
        </if>
        <if test="destDataId!=null and destDataId!=''">
            dest_data_id=#{destDataId},
        </if>
        <if test="sourceDataName!=null and sourceDataName!=''">
            source_data_name=#{sourceDataName},
        </if>
        <if test="destDataName!=null and destDataName!=''">
            dest_data_name=#{destDataName},
        </if>
        <if test="masterDataId!=null and masterDataId!=''">
            master_data_id=#{masterDataId},
        </if>
        <if test="lastSyncStatus!=null">
            last_sync_status=#{lastSyncStatus},
        </if>
        <if test="isCreated!=null">
            is_created=#{isCreated},
        </if>
        <if test="remark!=null and remark!=''">
            remark=#{remark},
        </if>
        update_time = #{updateTime}
        where tenant_id = #{tenantId}
        and id = #{id}
        AND is_deleted = false
    </update>

    <delete id="deleteByTenantIdAndIds">
        delete
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and id in
        <foreach item="item" index="index" collection="ids"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <sql id="query_replace_searchText">
        <!--下面这句是防代码解析报错的-->
        <!--@sql select count(id) from sync_data_mappings where tenant_id = #{tenantId} -->
        <if test="sourceDataId != null and sourceDataId != ''">
            and source_data_id = #{sourceDataId}
        </if>
        <if test="sourceDataName != null and sourceDataName != ''">
            and source_data_name = #{sourceDataName}
        </if>
        <if test="destDataId != null and destDataId != ''">
            and dest_data_id = #{destDataId}
        </if>
        <if test="destDataName != null and destDataName != ''">
            and dest_data_name = #{destDataName}
        </if>
        <if test="remark != null and remark != ''">
            and remark like CONCAT('%', #{remark}, '%')
        </if>
    </sql>


    <select id="getBySourceData"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select <include refid="Base_Column_List"/>
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and source_data_id = #{sourceDataId}
          and source_object_api_name = #{sourceObjApiName}
          and dest_object_api_name = #{destObjApiName}
          AND is_deleted = false
    </select>

    <select id="getBySourceAndDestObjApiNameLimit1"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select <include refid="Base_Column_List"/>
        from sync_data_mappings
        where tenant_id = #{tenantId}
        and source_object_api_name = #{sourceObjApiName}
        and dest_object_api_name = #{destObjApiName}
        AND is_deleted = false
        limit 1
    </select>

    <select id="getBySourceDataName"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select <include refid="Base_Column_List"/>
        from sync_data_mappings
        where tenant_id = #{tenantId}
        and source_data_name = #{sourceDataName}
        and source_object_api_name = #{sourceObjectApiName}
        and dest_object_api_name = #{destObjectApiName}
        AND is_deleted = false
    </select>

    <select id="getByDestData" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select <include refid="Base_Column_List"/>
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and source_object_api_name = #{sourceObjectApiName}
          and dest_data_id = #{destDataId}
          and dest_object_api_name = #{destObjectApiName}
          AND is_deleted = false
    </select>

    <select id="getByDestData2" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select <include refid="Base_Column_List"/>
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and dest_data_id = #{destDataId}
          and dest_object_api_name = #{destObjectApiName}
          AND is_deleted = false
    </select>

    <select id="listBySourceAndDestObjectApiName"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select <include refid="Base_Column_List"/>
        from sync_data_mappings
        where tenant_id = #{tenantId}
          AND source_object_api_name = #{sourceObjectApiName}
          AND dest_object_api_name = #{destObjectApiName}
          AND is_deleted = false
    </select>


    <select id="countByObjectApiNames" resultType="java.lang.Integer">
        select count(id)
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and
        <foreach collection="objectApiNameMappings" open="(" separator=" or " close=")" item="objectApiNameMapping">
            ( source_object_api_name = #{objectApiNameMapping.sourceObjectApiName}
                and dest_object_api_name = #{objectApiNameMapping.destObjectApiName})
        </foreach>
        <if test="status!=null and status in {1,2,3}">
            and trans_sync_status(last_sync_status) = #{status}
        </if>
        <if test="startTime != null">
            AND update_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND update_time &lt; #{endTime}
        </if>
        <include refid="query_replace_searchText"/>
        AND is_deleted = false
    </select>

    <select id="countByObjectApiNamesLimit1000" resultType="java.lang.Integer">
        select count(id) from
        (select id
         from sync_data_mappings
        where tenant_id = #{tenantId}
          and
        <foreach collection="objectApiNameMappings" open="(" separator=" or " close=")" item="objectApiNameMapping">
            ( source_object_api_name = #{objectApiNameMapping.sourceObjectApiName}
                and dest_object_api_name = #{objectApiNameMapping.destObjectApiName})
        </foreach>
        <if test="status!=null and status in {1,2,3}">
            and trans_sync_status(last_sync_status) = #{status}
        </if>
        <if test="startTime != null">
            AND update_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND update_time &lt; #{endTime}
        </if>
        <include refid="query_replace_searchText"/>
        AND is_deleted = false
        limit 1000)t1
    </select>

    <select id="countByObjectApiNames2Limit1000" resultType="java.lang.Integer" timeout="10">
        select count(id) from
        (select id
         from sync_data_mappings
        where tenant_id = #{tenantId}
          and source_object_api_name = #{objectApiNameMapping.sourceObjectApiName}
          and dest_object_api_name = #{objectApiNameMapping.destObjectApiName}
        <if test="startTime != null">
            AND update_time &gt;= #{startTime}
        </if>
        <if test="status != null and status in {1, 2, 3}">
            and trans_sync_status(last_sync_status) = #{status}
        </if>
        <include refid="query_replace_searchText"/>
        AND is_deleted = false
        limit 1000)t1
    </select>


    <select id="countByObjectApiNamesOnlyLimit1000" resultType="java.lang.Long" timeout="10">
        select count(id) from
        (select id
         from sync_data_mappings
        where tenant_id = #{tenantId}
          and source_object_api_name = #{sourceObjApiName}
          and dest_object_api_name = #{destObjectApiName}
        <if test="status != null and status in {1, 2, 3}">
            and trans_sync_status(last_sync_status) = #{status}
        </if>
        AND is_deleted = false
        limit 1000)t1
    </select>

    <select id="countGroup" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.model.MappingCountVo">
        select source_object_api_name,
               dest_object_api_name,
               trans_sync_status(last_sync_status) as sync_status,
               count(1)                            AS count
        from sync_data_mappings
        where tenant_id = #{tenantId}
          AND is_deleted = false
        GROUP BY source_object_api_name,
                 dest_object_api_name,
                 trans_sync_status(last_sync_status)
    </select>

    <select id="listByObjectApiNames2"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity"
            timeout="10">
        select <include refid="Base_Column_List"/>
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and source_object_api_name = #{objectApiNameMapping.sourceObjectApiName}
          and dest_object_api_name = #{objectApiNameMapping.destObjectApiName}
        <if test="status != null and status in {1, 2, 3}">
            and trans_sync_status(last_sync_status) = #{status}
        </if>
        <include refid="query_replace_searchText">
        </include>
        AND is_deleted = false
        <if test="startTime != null">
            AND update_time &gt;= #{startTime}
        </if>
        <choose>
            <when test="maxId != null">
                and update_time = #{maxTime}
                and id &lt; #{maxId}
            </when>
            <otherwise>
                and update_time &lt; #{maxTime}
            </otherwise>
        </choose>
        order by update_time desc, id desc
        limit #{limit}
    </select>

    <select id="countByDetailObjectApiNames" resultType="java.lang.Integer">
        select count(id)
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and master_data_id = #{masterDataId}
          and
        <foreach collection="objectApiNameMappings" open="(" separator=" or " close=")" item="objectApiNameMapping">
            ( source_object_api_name = #{objectApiNameMapping.sourceObjectApiName}
                and dest_object_api_name = #{objectApiNameMapping.destObjectApiName})
        </foreach>
        <if test="status!=null and status in {1,2,3}">
            and trans_sync_status(last_sync_status) = #{status}
        </if>
        <if test="startTime != null">
            AND update_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND update_time &lt; #{endTime}
        </if>
        <include refid="query_replace_searchText"/>
        AND is_deleted = false
    </select>

    <select id="countSyncFailed"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncDataMappingsFailedCountData">
        select source_object_api_name, dest_object_api_name, count(id) as failed_count
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and trans_sync_status(last_sync_status) = 2
          and
        <foreach collection="objectApiNameMappings" open="(" separator=" or " close=")" item="objectApiNameMapping">
            (source_object_api_name = #{objectApiNameMapping.sourceObjectApiName}
                and dest_object_api_name = #{objectApiNameMapping.destObjectApiName})
        </foreach>
        AND is_deleted = false
        group by tenant_id, source_object_api_name, dest_object_api_name
    </select>


    <select id="countBySyncFailedAndLimit" resultType="java.lang.Integer">
        select count(1)
        from (select id
              from sync_data_mappings
              where tenant_id = #{tenantId}
                and trans_sync_status(last_sync_status) = 2
                and (source_object_api_name = #{objectApiNameMapping.sourceObjectApiName}
                  and dest_object_api_name = #{objectApiNameMapping.destObjectApiName})
                AND is_deleted = false
              limit #{limit} offset #{offset}) as t
    </select>


    <select id="listByObjectApiNames"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select <include refid="Base_Column_List"/>
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and
        <foreach collection="objectApiNameMappings" open="(" separator=" or " close=")" item="objectApiNameMapping">
            ( source_object_api_name = #{objectApiNameMapping.sourceObjectApiName}
                and dest_object_api_name = #{objectApiNameMapping.destObjectApiName})
        </foreach>
        <if test="status!=null and status in {1,2,3}">
            and trans_sync_status(last_sync_status) = #{status}
        </if>
        <include refid="query_replace_searchText"/>
        AND is_deleted = false
        <if test="startTime != null">
            AND update_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND update_time &lt; #{endTime}
        </if>
        order by update_time desc
        limit #{limit}
        offset #{offset}
    </select>

    <select id="listByMasterDataId" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select <include refid="Base_Column_List"/>
        from sync_data_mappings
        where tenant_id = #{tenantId}
        and
        <foreach collection="objectApiNameMappings" open="(" separator=" or " close=")" item="objectApiNameMapping">
            ( source_object_api_name = #{objectApiNameMapping.sourceObjectApiName}
            and dest_object_api_name = #{objectApiNameMapping.destObjectApiName})
        </foreach>
        and master_data_id = #{masterDataId}
        AND is_deleted = false
    </select>

    <select id="listCreatedDetailMappingByMasterDataId"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select
        <include refid="Base_Column_List"/>
        from sync_data_mappings
        where tenant_id = #{tenantId}
        and source_object_api_name = #{objectApiNameMapping.sourceObjectApiName}
        and dest_object_api_name = #{objectApiNameMapping.destObjectApiName}
        and master_data_id = #{masterDataId}
        and is_created = true
        AND is_deleted = false
    </select>

    <select id="listByDetailObjectApiNames"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select <include refid="Base_Column_List"/>
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and master_data_id = #{masterDataId}
          and
        <foreach collection="objectApiNameMappings" open="(" separator=" or " close=")" item="objectApiNameMapping">
            ( source_object_api_name = #{objectApiNameMapping.sourceObjectApiName}
                and dest_object_api_name = #{objectApiNameMapping.destObjectApiName})
        </foreach>
        <if test="status!=null and status in {1,2,3}">
            and trans_sync_status(last_sync_status) = #{status}
        </if>
        <include refid="query_replace_searchText"/>
        AND is_deleted = false
        <if test="startTime != null">
            AND update_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND update_time &lt; #{endTime}
        </if>
        order by update_time desc
        limit #{limit}
        offset #{offset}
    </select>

    <update id="batchUpdateByIds">
        update sync_data_mappings
        set last_sync_status = #{syncStatus},
        <if test="destDataName != null">
            dest_data_name = #{destDataName},
        </if>
        <if test="remark != null">
            remark = #{remark},
        </if>
        update_time = #{updateTime}
        where tenant_id = #{tenantId}
          and id
            in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND is_deleted = false
    </update>


    <update id="updateDestDataIdIgnore">
        UPDATE sync_data_mappings
        SET dest_data_id=#{destDataId},
        <if test="setIsCreatedTrue">
            is_created = true,
        </if>
            update_time = #{updateTime}
        WHERE tenant_id = #{tenantId}
          and id = #{id}
          AND is_deleted = false
    </update>

    <update id="updateDataMapping2SuccessIgnore">
        UPDATE sync_data_mappings
        SET dest_data_id=#{destDataId},
            last_sync_status=6,
            is_created= true,
            update_time     = #{updateTime}
        <if test="masterDataId != null">
            ,
                master_data_id = #{masterDataId}
        </if>
        WHERE tenant_id = #{tenantId}
          and id = #{id}
          AND is_deleted = false
    </update>

    <update id="updateDataMappingByParams">
        UPDATE sync_data_mappings
        SET dest_data_id=#{destDataId},
            update_time = #{updateTime}
        <if test="masterDataId != null">
            ,
                master_data_id = #{masterDataId}
        </if>
        <if test="remark != null">
            ,
                remark = #{remark}
        </if>
        <if test="sourceDataName != null">
            ,
                source_data_name = #{sourceDataName}
        </if>
        <if test="destDataName != null">
            ,
                dest_data_name = #{destDataName}
        </if>
        WHERE tenant_id = #{tenantId}
          and id = #{id}
          AND is_deleted = false
    </update>

    <update id="updateSourceDataId">
        UPDATE sync_data_mappings
        SET source_data_id=#{sourceDataId},
            last_sync_status=6,
            is_created= true,
            update_time     = #{updateTime}
        WHERE tenant_id = #{tenantId}
          and id = #{id}
          AND is_deleted = false
    </update>
    <update id="updateLastStatusById">
        UPDATE sync_data_mappings
        SET last_sync_status=#{status},
            update_time = (SELECT EXTRACT(epoch FROM now()) * 1000)
        WHERE tenant_id = #{tenantId}
          and id = #{id}
          AND is_deleted = false
    </update>
    <update id="batchUpdateStatusByIds">
        update sync_data_mappings
        set  last_sync_status=#{status},
        remark=#{remark},
        update_time      = (SELECT EXTRACT(epoch FROM now()) * 1000)
        where tenant_id = #{tenantId}
        and id
        in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND is_deleted = false
    </update>

    <select id="queryBySourceDataIdList"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select <include refid="Base_Column_List"/>
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and source_object_api_name = #{sourceObjectApiName}
          and source_data_id IN
        <foreach collection="sourceDataIds" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
        and dest_object_api_name = #{destObjectApiName}
        AND is_deleted = false
    </select>


    <select id="queryByDestDataIdList"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select <include refid="Base_Column_List"/>
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and source_object_api_name = #{sourceObjectApiName}
          and dest_data_id IN
        <foreach collection="destDataIds" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
        and dest_object_api_name = #{destObjectApiName}
        AND is_deleted = false
    </select>

    <delete id="deleteSyncDataMappings">
        delete
        from sync_data_mappings
        where tenant_id = #{tenantId}
        <if test="sourceObjApiName != null">
            and source_object_api_name = #{sourceObjApiName}
        </if>
        <if test="destObjApiName != null">
            and dest_object_api_name = #{destObjApiName}
        </if>
    </delete>


    <!--auto generated by MybatisCodeHelper on 2021-07-16-->
    <select id="getById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sync_data_mappings
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
          and id = #{id,jdbcType=VARCHAR}
    </select>


    <select id="countIsCreatedMapping" resultType="java.lang.Integer">
        select count(id)
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and source_object_api_name = #{sourceObjectApiName}
          and dest_object_api_name = #{destObjectApiName}
          and is_created = #{isCreated}
          AND is_deleted = false
    </select>

    <delete id="deleteMappingsByDestId">
        delete
        from sync_data_mappings
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
          and dest_data_id = #{dataId}
          and source_object_api_name = #{sourceObjectApiName}
          and dest_object_api_name = #{destObjectApiName}
          and is_deleted = false
    </delete>
    <delete id="deleteMappingsBySourceId">
        delete
        from sync_data_mappings
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
          and source_data_id = #{dataId}
          and source_object_api_name = #{sourceObjectApiName}
          and dest_object_api_name = #{destObjectApiName}
          and is_deleted = false
    </delete>

    <select id="listCreatedBySourceDataIds" resultMap="BaseResultMap">
        select source_data_id, is_created, remark,last_sync_data_id
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and source_object_api_name = #{sourceObjApiName}
          and dest_object_api_name = #{destObjApiName}
          AND is_deleted = false
          and source_data_id in
        <foreach collection="sourceDataIds" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </select>

    <select id="listCreatedByDestDataIds" resultMap="BaseResultMap">
        select dest_data_id, is_created
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and source_object_api_name = #{sourceObjApiName}
          and dest_object_api_name = #{destObjApiName}
          AND is_deleted = false
          and dest_data_id in
        <foreach collection="destDataIds" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </select>

    <select id="listOrderById"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select
        <include refid="Base_Column_List"/>
        from sync_data_mappings
        <if test="id != null">
            where id > #{id}
        </if>
        order by id
        limit #{limit}
    </select>
    <select id="listOrderByUpdateTime"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select
        <include refid="Base_Column_List"/>
        from sync_data_mappings
        <if test="lastUpdateTime != null">
            where update_time > #{lastUpdateTime}
        </if>
        order by update_time
        limit #{limit}
    </select>
    <select id="listOrderByEqUpdateTime"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select
        <include refid="Base_Column_List"/>
        from sync_data_mappings
        <if test="lastUpdateTime != null">
            where update_time = #{lastUpdateTime}
        </if>
    </select>
    <select id="count" resultType="java.lang.Integer">
        select count(id)
        from sync_data_mappings
    </select>

    <!--auto generated by MybatisCodeHelper on 2022-04-25-->
    <select id="listSuccessBySrcIds"
            resultType="java.lang.String">
        select source_data_id
        from sync_data_mappings
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
          and source_object_api_name = #{sourceObjectApiName,jdbcType=VARCHAR}
          and dest_object_api_name = #{destObjectApiName,jdbcType=VARCHAR}
          and source_data_id in
        <foreach item="item" index="index" collection="sourceDataIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and trans_sync_status(last_sync_status) = 1
        and is_deleted = false;
    </select>

    <select id="countSyncFailedAfterTime" resultType="int" timeout="60">
        select count(id)
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and source_object_api_name = #{objectApiNameMapping.sourceObjectApiName}
          and dest_object_api_name = #{objectApiNameMapping.destObjectApiName}
          and trans_sync_status(last_sync_status) = 2
          and update_time &gt; #{minTime}
          and is_deleted = false;
    </select>
    <select id="listByStatusTimeOutMappings"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select
        <include refid="Base_Column_List"/>
        from sync_data_mappings
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
          and source_object_api_name = #{sourceObjectApiName,jdbcType=VARCHAR}
          and dest_object_api_name = #{destObjectApiName,jdbcType=VARCHAR}
          and trans_sync_status(last_sync_status) = 3
          and is_deleted = false
          and update_time between #{startTime} and #{endTime}
        limit #{limit} offset #{offset};
    </select>
    <select id="listByStatusFailMappings"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select
        <include refid="Base_Column_List"/>
        from sync_data_mappings
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
          and source_object_api_name = #{sourceObjectApiName,jdbcType=VARCHAR}
          and dest_object_api_name = #{destObjectApiName,jdbcType=VARCHAR}
          and trans_sync_status(last_sync_status) = 2
          and is_deleted = false
          and is_created = true
          and update_time between #{startTime} and #{endTime}
        limit #{limit} offset #{offset};
    </select>
    <select id="listByObjApiName" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select <include refid="Base_Column_List"/>
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and (source_object_api_name = #{objApiName} or dest_object_api_name = #{objApiName})
          AND is_deleted = false
        limit #{limit} offset #{offset}
    </select>

<!--auto generated by MybatisCodeHelper on 2023-06-07-->
    <select id="queryDeletedData" resultType="String">
        select
        id
        from sync_data_mappings
        where id <![CDATA[>]]> #{minId}
        and is_deleted = true
        order by id
        limit 1000;
    </select>


    <delete id="deleteByIds">
        delete from sync_data_mappings
        where  id in
        <foreach item="item" index="index" collection="ids"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="getMappingByDestObjApiNameAndId" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select *
        from sync_data_mappings
        where tenant_id = #{tenantId}
        and dest_object_api_name = #{destObjectApiName}
        and dest_data_id = #{destObjectId}
        and is_deleted = false
    </select>
    <select id="countByTimestamp" resultType="java.lang.Integer">
        select count(id)
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and source_object_api_name = #{sourceObjectApiName}
          and dest_object_api_name = #{destObjectApiName}
        <if test="status!=null and status in {1,2,3}">
            and trans_sync_status(last_sync_status) = #{status}
        </if>
        <if test="startCreateTime != null and endCreateTime != null">
            and create_time BETWEEN #{startCreateTime} and #{endCreateTime}
        </if>
        <if test="startUpdateTime != null and endUpdateTime != null">
            and update_time BETWEEN #{startUpdateTime} and #{endUpdateTime}
        </if>
          AND is_deleted = false
        order by update_time desc
    </select>
    <select id="countByIdsTimestamp" resultType="java.lang.Integer">
        select count(id)
        from sync_data_mappings
        where tenant_id = #{tenantId}
        and source_object_api_name = #{sourceObjectApiName}
        and dest_object_api_name = #{destObjectApiName}
        <if test="status!=null and status in {1,2,3}">
            and trans_sync_status(last_sync_status) = #{status}
        </if>
        <if test="startCreateTime != null">
            and create_time &gt; #{startCreateTime}
        </if>
        <if test="startUpdateTime != null">
            and update_time &gt; #{startUpdateTime}
        </if>
        and source_data_id in
        <foreach item="item" index="index" collection="sourceDataIds"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        AND is_deleted = false
    </select>
    <delete id="deleteMappingsByMasterId">
        delete
        from sync_data_mappings
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
          and master_data_id = #{masterDataId}
          and source_object_api_name = #{sourceObjectApiName}
          and dest_object_api_name = #{destObjectApiName}
    </delete>
    <select id="getLastSyncData" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity">
        select *
        from sync_data_mappings
        where tenant_id = #{tenantId}
        and source_object_api_name = #{sourceObjectApiName}
        and dest_object_api_name = #{destObjectApiName}
        order by update_time desc
        limit #{limit} offset #{offset}
    </select>


    <select id="listFailedMappingEqTime" resultMap="BaseResultMap">
        select id, is_created, update_time
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and source_object_api_name = #{sourceObjectApiName}
          and dest_object_api_name = #{destObjectApiName}
          and trans_sync_status(last_sync_status) = 2
          and update_time = #{updateTime}
          and id &lt; #{maxId}
          and is_deleted = false
        order by update_time desc, id desc limit 1000
    </select>

    <select id="listFailedMappingLtTime" resultMap="BaseResultMap">
        select id, is_created, update_time
        from sync_data_mappings
        where tenant_id = #{tenantId}
          and source_object_api_name = #{sourceObjectApiName}
          and dest_object_api_name = #{destObjectApiName}
          and trans_sync_status(last_sync_status) = 2
          and update_time &lt; #{updateTime}
          and is_deleted = false
        order by update_time desc, id desc limit 1000
    </select>

    <!--auto generated by MybatisCodeHelper on 2025-01-10-->
    <select id="queryIdAndUpdateTimeByTimeBetween"
            resultMap="BaseResultMap">
        select id, update_time
        from sync_data_mappings
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
          and source_object_api_name = #{sourceObjectApiName,jdbcType=VARCHAR}
          and dest_object_api_name = #{destObjectApiName,jdbcType=VARCHAR}
          and update_time <![CDATA[>]]> #{minUpdateTime,jdbcType=BIGINT}
          and update_time <![CDATA[<]]>
              #{maxUpdateTime,jdbcType=BIGINT}
          and is_deleted = false
        order by update_time desc limit 1000
    </select>
    <select id="groupByObjectAndStatus" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.model.MappingStatusNum">
        select 
            tenant_id as tenantId,
            source_object_api_name as sourceObjApiName,
            dest_object_api_name as destObjApiName,
            trans_sync_status(last_sync_status) as syncStatus,
            count(*) as cnt
        from sync_data_mappings
        where is_deleted = false
        group by tenant_id, source_object_api_name, dest_object_api_name, last_sync_status
    </select>
    
</mapper>