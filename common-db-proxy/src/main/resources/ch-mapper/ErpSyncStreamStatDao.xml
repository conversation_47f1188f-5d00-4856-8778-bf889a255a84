<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.ch.dao.ErpSyncStreamStatDao">
    <select id="getLatestTraceIdAndTime" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ch.ErpSyncStreamStat">
        select traceId,createTime
        from biz_log_erp_sync_stream_stat
        where upTenantId = #{upTenantId}
          and dcId = #{dcId}
        and createTime &gt; #{minCreateTime}
        order by createTime desc
        limit 1;
    </select>

    <select id="calDownstreamStat" resultType="com.fxiaoke.open.erpsyncdata.preprocess.model.DownstreamRelationManage">
        select tenantId,
               uniqIf(streamId, streamStatus = 'alerting') alertingStreamCount,
               uniqIf(streamId, mappingFailedTotal > 0)    syncDataFailedStreamsCount,
               max(createTime)         as                  maxCreateTime,
               max(lastAlertTime)      as                  maxLastAlertTime,
               max(lastSyncTime)       as                  maxLastSyncTime,
               sum(mappingFailedTotal) as                  syncDataFailedTotal
        from biz_log_erp_sync_stream_stat
        where upTenantId = #{upTenantId}
          and dcId = #{dcId}
          and createTime >= #{minCreateTime}
          and traceId = #{traceId}
        group by tenantId
        order by ${orderBy} maxCreateTime DESC
        limit #{limit} offset #{offset};
    </select>

    <select id="countDownstreamStat" resultType="int">
        select uniq(tenantId)
        from biz_log_erp_sync_stream_stat
        where upTenantId = #{upTenantId}
          and dcId = #{dcId}
          and createTime >= #{minCreateTime}
          and traceId = #{traceId};
    </select>
    <select id="getAlertDownstreamIds" resultType="java.lang.String">
        select uniq(tenantId)
        from biz_log_erp_sync_stream_stat
        where upTenantId = #{upTenantId}
          and dcId = #{dcId}
          and createTime >= #{minCreateTime}
          and traceId = #{traceId}
          and streamStatus = 'alerting';
    </select>
    <select id="getFailDownstreamIds" resultType="java.lang.String">
        select uniq(tenantId)
        from biz_log_erp_sync_stream_stat
        where upTenantId = #{upTenantId}
          and dcId = #{dcId}
          and createTime >= #{minCreateTime}
          and traceId = #{traceId}
          and mappingFailedTotal > 0;
    </select>
</mapper>