<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.ch.dao.CHSyncDataDao">
    <sql id="Base_Column_List">
        appName,
        traceId,
        serverIp,
        tenantId,
        createTime,
        updateTime,
        expireTime,
        logType,
        id,
        sourceTenantType,
        destTenantType,
        sourceEventType,
        sourceObjectApiName,
        sourceDataId,
        erpTempDataDataNumber,
        sourceData,
        sourceDetailSyncDataIds,
        destEventType,
        destObjectApiName,
        destDataId,
        destData,
        syncDataStatus,
        syncPloyDetailSnapshotId,
        remark,
        operatorId,
        errorCode,
        isDeleted,
        needReturnDestObjectData,
        "data",
        logId,
        dataReceiveType
    </sql>

    <select id="getById" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncDataEntity">
        select
        <include refid="Base_Column_List"/>
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='sync_data' and isDeleted is null
        and id = #{id}
        and createTime >= #{startLogTime}
        and createTime &lt;= #{endTLogTime}
        limit 1
    </select>

    <select id="getByIdSelective" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncDataEntity">
        select
        #{returnField}
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='sync_data' and isDeleted is null
        and id = #{id}
        and createTime >= #{startLogTime}
        and createTime &lt;= #{endTLogTime}
        limit 1
    </select>

    <select id="listByIds" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncDataEntity">
        select
        <include refid="Base_Column_List"/>
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='sync_data' and isDeleted is null
        and id IN
        <foreach item="item" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
        and createTime >= #{startLogTime}
        and createTime &lt;= #{endTLogTime}
    </select>
    <select id="listSimpleByIds" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncDataEntity">
        select
        <include refid="Base_Column_List"/>
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='sync_data' and isDeleted is null
        and id IN
        <foreach item="item" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
        and createTime >= #{startLogTime}
        and createTime &lt;= #{endTLogTime}
    </select>
    <select id="listBySourceData" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncDataEntity">
        select
        <include refid="Base_Column_List"/>
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='sync_data' and isDeleted is null
        <if test="sourceObjectApiNames!=null and sourceObjectApiNames.size()>0">
            and sourceObjectApiName in
            <foreach item="item" collection="sourceObjectApiNames" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="destObjectApiNames!=null and destObjectApiNames.size()>0">
            and destObjectApiName in
            <foreach item="item" collection="destObjectApiNames" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="sourceDataId!=null and sourceDataId.size()>0">
            and sourceDataId in
            <foreach item="item" collection="sourceDataId" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and createTime >= #{startLogTime}
        and createTime &lt;= #{endTLogTime}
        order by createTime desc
        limit 1000
    </select>
    <select id="listByPage" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncDataEntity">
        select sourceDataId,erpTempDataDataNumber,sourceData,destDataId,updateTime,id,logId,operatorId,sourceEventType,destEventType,dataReceiveType,remark,syncDataStatus
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='sync_data' and isDeleted is null
        <if test="sourceObjectApiNames!=null and sourceObjectApiNames.size()>0">
            and sourceObjectApiName in
            <foreach item="item" collection="sourceObjectApiNames" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="destObjectApiNames!=null and destObjectApiNames.size()>0">
            and destObjectApiName in
            <foreach item="item" collection="destObjectApiNames" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="logId!=null">
            and logId = #{logId}
        </if>
        <if test="sourceDataId!=null">
            and sourceDataId = #{sourceDataId}
        </if>
        <if test="sourceDataName!=null">
            and erpTempDataDataNumber = #{sourceDataName}
        </if>
        and createTime >= #{startLogTime}
        and createTime &lt;= #{endTLogTime}
        order by createTime desc
        limit #{limit} offset #{offset}
    </select>
    <select id="countByApiNames" resultType="java.lang.Integer">
        select
        count(*)
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='sync_data' and isDeleted is null
        <if test="sourceObjectApiNames!=null  and sourceObjectApiNames.size()>0">
            and sourceObjectApiName in
            <foreach item="item" collection="sourceObjectApiNames" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="destObjectApiNames!=null and destObjectApiNames.size()>0">
            and destObjectApiName in
            <foreach item="item" collection="destObjectApiNames" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="logId!=null">
            and logId = #{logId}
        </if>
        <if test="sourceDataId!=null">
            and sourceDataId = #{sourceDataId}
        </if>
        <if test="sourceDataName!=null">
            and erpTempDataDataNumber = #{sourceDataName}
        </if>
        and createTime >= #{startLogTime}
        and createTime &lt;= #{endTLogTime}
    </select>
    <select id="listByStatusListAndEndUpdateTime"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncDataEntity">
        select
        <include refid="Base_Column_List"/>
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='sync_data' and isDeleted is null
        and createTime>#{startUpdateTime}
        and createTime &lt; #{endUpdateTime}
        and syncDataStatus IN
        <foreach item="item" collection="statusList" open="(" separator="," close=")">
            #{item}
        </foreach>
        limit #{limit} offset #{offset}
    </select>
    <select id="listByFsDataId"  resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncDataEntity">
        select <include refid="Base_Column_List"/>
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='sync_data' and isDeleted is null
        <choose>
            <when test="syncDirection=2">
                and sourceObjectApiName = #{objApiName}
                and sourceDataId = #{fsDataId}
            </when>
            <otherwise>
                and destObjectApiName = #{objApiName}
                and destDataId = #{fsDataId}
            </otherwise>
        </choose>
        and createTime >= #{startLogTime}
        and createTime &lt;= #{endTLogTime}
        <if test="lastId!=null">
            id > #{lastId}
        </if>
        order by createTime asc
        limit #{limit}
    </select>

    <select id="limitGroupByObj" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncDataEntity">
        select <include refid="Base_Column_List"/> from
        (select <include refid="Base_Column_List"/>,ROW_NUMBER() OVER (PARTITION BY sourceDataId ORDER BY id desc) AS rn from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='sync_data' and isDeleted is null
        and sourceObjectApiName = #{sourceObjectApiName}
        and destObjectApiName = #{destObjectApiName}
        and sourceDataId in
        <foreach item="item" collection="srcIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        and createTime >= #{startLogTime}
        and createTime &lt;= #{endTLogTime}
        limit 3000)as t1
        where t1.rn>1 limit 3000
    </select>
    <select id="countByTenantId" resultType="java.lang.Long">
        select count(*)
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId}
          and logType = 'sync_data' and isDeleted is null
    </select>
    <select id="listSyncDatas" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncDataEntity">
        select
        <include refid="Base_Column_List"/>
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='sync_data' and isDeleted is null
        <if test="sourceObjApiName!=null">
            and sourceObjectApiName = #{sourceObjApiName}
        </if>
        <if test="destObjApiName!=null">
            and destObjectApiName = #{destObjApiName}
        </if>
        and createTime >= #{startLogTime}
        and createTime &lt;= #{endTLogTime}
    </select>
    <select id="listBySourceDataIds" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncDataEntity">
        select
        <include refid="Base_Column_List"/>
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='sync_data' and isDeleted is null
        and sourceObjectApiName = #{sourceObjectApiName}
        and destObjectApiName = #{destObjectApiName}
        and sourceDataId in
        <foreach item="item" collection="srcIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        and createTime >= #{startLogTime}
        and createTime &lt;= #{endTLogTime}
    </select>
    <select id="getAllTypeCount" resultType="java.util.Map">
        select destEventType,syncDataStatus,count(*)as countNum from(
        select sourceDataId,syncDataStatus,destEventType
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='sync_data' and isDeleted is null
        and sourceObjectApiName = #{sourceApiName}
        and destObjectApiName = #{destApiName}
        <if test="startTime!=null">
            createTime>=#{startTime}
        </if>
        <if test="endTime!=null">
            createTime &lt; #{endTime}
        </if>
        <if test="ids!=null">
            and sourceDataId in
            <foreach item="item" collection="ids" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by createTime desc limit 1 by(sourceDataId,syncDataStatus,destEventType))
        GROUP by (destEventType,syncDataStatus)
    </select>
    <select id="findMinDate" resultType="java.lang.Long">
        select createTime
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId}
          and logType = 'sync_data' and isDeleted is null
        order by createTime asc limit 1
    </select>
    <select id="listBetween" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncDataEntity">
        select
        <include refid="Base_Column_List"/>
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='sync_data' and isDeleted is null
        <if test="startTime!=null">
            createTime>=#{startTime}
        </if>
        <if test="endTime!=null">
            createTime &lt; #{endTime}
        </if>
        <if test="lastId!=null">
            id > #{lastId}
        </if>
        order by id asc
        limit #{limit}
    </select>
</mapper>