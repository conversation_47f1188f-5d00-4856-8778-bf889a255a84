<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:context="http://www.springframework.org/schema/context" default-lazy-init="true"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <context:component-scan base-package="com.fxiaoke.open.erpsyncdata.dbproxy.config"/>

    <import resource="classpath:enterpriserelation2/enterpriserelation.xml"/>
    <!--不会熔断的okhttp support-->
    <bean id="noCircuitOkHttpSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean"
          p:configName="erp-sync-data-section" p:sectionNames="http-no-circuit" init-method="init"/>
    <bean id="crmRestRetrofitFactory" class="com.fxiaoke.retrofitspring.fxiaoke.ConfigRetrofitSpringFactory"
          p:configNames="fs-crm-rest-api" init-method="init">
        <property name="okHttpSupport" ref="noCircuitOkHttpSupport"/>
        <property name="gson" ref="crmRestApiGson"/>
    </bean>

    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.MetadataActionService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.MetadataControllerService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.MetadataDataService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.ObjectDescribeService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.DuplicatesearchService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.ObjectRecordTypeService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.MetadataTagService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.MetadataTagDataService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.ObjectDataService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.MarketingService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.MemberService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.LeadsPoolService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.WechatFanService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.FlowService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" id="crmDeviceRestService"
          p:type="com.fxiaoke.crmrestapi.service.CrmDeviceService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.ObjectService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.RoleService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.SmartFormService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.ObjectLayoutService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.PaasMetadataRoleService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.MetadataTenantService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.PartnerService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.RoleFieldPrivlidgeService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.SkuSpuService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.PaasGlobalDataService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.RoleV2Service">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.SyncFieldService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.CurrencyService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.CrmErService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>

    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.CountryAreaService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>

    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.ObjectValueMappingService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>

    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.TenantSceneService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.ObjectDataServiceV3">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.crmrestapi.service.MetadataControllerServiceV3">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.open.erpsyncdata.dbproxy.remote.base.service.ModificationRecordService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
    <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.fxiaoke.open.erpsyncdata.dbproxy.remote.base.service.EgressApiService">
        <property name="factory" ref="crmRestRetrofitFactory"/>
    </bean>
</beans>
