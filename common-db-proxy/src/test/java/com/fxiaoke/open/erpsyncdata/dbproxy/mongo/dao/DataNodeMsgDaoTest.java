package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;

import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataNodeMsgDoc;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeTypeEnum;
import com.github.mongo.support.DatastoreExt;
import com.mongodb.MongoClient;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.MongoIterable;
import com.mongodb.client.result.DeleteResult;
import org.bson.Document;
import com.mongodb.client.ListIndexesIterable;


/**
 * DataNodeMsgDao 单元测试
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class DataNodeMsgDaoTest {

    private DataNodeMsgDao dataNodeMsgDao;

    @Mock
    private DatastoreExt store;

    @Mock
    private MongoCollection<DataNodeMsgDoc> mongoCollection;

    @Mock
    private FindIterable<DataNodeMsgDoc> findIterable;

    @Mock
    private MongoCursor<DataNodeMsgDoc> mongoCursor;

    private final String tenantId = "test-tenant";
    private final String objApiName = "TestObj";
    private final String dataId = "data123";
    private final Long version = 1L;
    private final String streamId = "streamX";
    private final String uniqueKey = "ukey";

    @BeforeEach
    void setUp() throws Exception {
        dataNodeMsgDao = new DataNodeMsgDao();
        // 先反射赋值 DATABASE
        Field databaseField = DataNodeMsgDao.class.getDeclaredField("DATABASE");
        databaseField.setAccessible(true);
        databaseField.set(dataNodeMsgDao, "test-db");
        // mock 链路
        MongoClient mongoClient = mock(MongoClient.class);
        when(store.getMongo()).thenReturn(mongoClient);
        com.mongodb.client.MongoDatabase mongoDatabase = mock(com.mongodb.client.MongoDatabase.class);
        when(mongoClient.getDatabase(anyString())).thenReturn(mongoDatabase);
        when(mongoDatabase.withCodecRegistry(any())).thenReturn(mongoDatabase);
        when(mongoDatabase.getCollection(anyString(), eq(DataNodeMsgDoc.class))).thenReturn(mongoCollection);
        MongoIterable<String> collectionNames = mock(MongoIterable.class);
        when(mongoDatabase.listCollectionNames()).thenReturn(collectionNames);
        MongoCursor<String> strCursor = mock(MongoCursor.class);
        when(collectionNames.iterator()).thenReturn(strCursor);
        when(strCursor.hasNext()).thenReturn(true, false);
        when(strCursor.next()).thenReturn("data_node_msg");
        // mock listIndexes 防止NPE
        ListIndexesIterable<Document> indexIterable = mock(ListIndexesIterable.class);
        when(mongoCollection.listIndexes()).thenReturn(indexIterable);
        MongoCursor<Document> indexCursor = mock(MongoCursor.class);
        when(indexIterable.iterator()).thenReturn(indexCursor);
        when(indexCursor.hasNext()).thenReturn(false);
        // 最后手动注入 store
        dataNodeMsgDao.setStore(store);
    }

    private DataNodeMsgDoc createTestDoc() {
        return DataNodeMsgDoc.create(
                tenantId, objApiName, dataId, version, streamId,
                DataNodeTypeEnum.start, DataNodeNameEnum.EnterTempData, System.currentTimeMillis(),
                "remark", "traceId", "operateMsg"
        );
    }

    @Test
    void testGetByObjectIds() {
        List<ObjectId> ids = Arrays.asList(new ObjectId(), new ObjectId());
        when(mongoCollection.find(any(org.bson.conversions.Bson.class))).thenReturn(findIterable);
        when(findIterable.limit(anyInt())).thenReturn(findIterable);
        when(findIterable.iterator()).thenReturn(mongoCursor);
        when(mongoCursor.hasNext()).thenReturn(true, true, false);
        when(mongoCursor.next()).thenReturn(createTestDoc(), createTestDoc());
        List<DataNodeMsgDoc> result = dataNodeMsgDao.getByObjectIds(ids);
        assertNotNull(result);
    }

    @Test
    void testGetByUniqueKey() {
        List<String> uniqueKeys = Arrays.asList("ukey1", "ukey2");
        when(mongoCollection.find(any(org.bson.conversions.Bson.class))).thenReturn(findIterable);
        when(findIterable.limit(anyInt())).thenReturn(findIterable);
        when(findIterable.iterator()).thenReturn(mongoCursor);
        when(mongoCursor.hasNext()).thenReturn(true, true, false);
        when(mongoCursor.next()).thenReturn(createTestDoc(), createTestDoc());
        List<DataNodeMsgDoc> result = dataNodeMsgDao.getByUniqueKey(tenantId, uniqueKeys);
        assertNotNull(result);
    }

    @Test
    void testDeleteByIds() {
        List<ObjectId> ids = Arrays.asList(new ObjectId(), new ObjectId());
        when(mongoCollection.deleteMany(any())).thenReturn(mock(DeleteResult.class));
        dataNodeMsgDao.deleteByIds(ids);
        verify(mongoCollection).deleteMany(any());
    }

    @Test
    void testDeleteInVersionNodesMsg() {
        when(mongoCollection.deleteMany(any())).thenReturn(mock(DeleteResult.class));
        dataNodeMsgDao.deleteInVersionNodesMsg(tenantId, objApiName, dataId, streamId, Arrays.asList(1L, 2L));
        verify(mongoCollection).deleteMany(any());
    }

    @Test
    void testDeleteLtVersionEnterTempDataNodesMsg() {
        when(mongoCollection.deleteMany(any())).thenReturn(mock(DeleteResult.class));
        dataNodeMsgDao.deleteLtVersionEnterTempDataNodesMsg(tenantId, objApiName, dataId, streamId, 1L);
        verify(mongoCollection).deleteMany(any());
    }

    @Test
    void testDeleteLteVersionNodesMsg() {
        when(mongoCollection.deleteMany(any())).thenReturn(mock(DeleteResult.class));
        dataNodeMsgDao.deleteLteVersionNodesMsg(tenantId, objApiName, dataId, streamId, 1L);
        verify(mongoCollection).deleteMany(any());
    }

    @Test
    void testGetLtTimeNodeMsgList() {
        when(mongoCollection.find(any(org.bson.conversions.Bson.class))).thenReturn(findIterable);
        when(findIterable.limit(anyInt())).thenReturn(findIterable);
        when(findIterable.iterator()).thenReturn(mongoCursor);
        when(mongoCursor.hasNext()).thenReturn(true, true, false);
        when(mongoCursor.next()).thenReturn(createTestDoc(), createTestDoc());
        List<DataNodeMsgDoc> result = dataNodeMsgDao.getLtTimeNodeMsgList(tenantId, null, System.currentTimeMillis(), 2);
        assertNotNull(result);
    }
} 