package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.fxiaoke.open.erpsyncdata.dbproxy.constant.LogStorageRuleEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpTenantConfigurationDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.config.AlertNoticeConfig;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TenantConfigurationManagerTest {

    @InjectMocks
    private TenantConfigurationManager tenantConfigurationManager;

    @Mock
    private ErpTenantConfigurationDao erpTenantConfigurationDao;

    private String tenantId = "99999";
    private String dataCenterId = "0";

    @BeforeEach
    void setUp() {
        // 设置基本的mock行为
        lenient().when(erpTenantConfigurationDao.setTenantId(any())).thenReturn(erpTenantConfigurationDao);
        
        // 清理缓存
        tenantConfigurationManager.clearCache();
    }

    private ErpTenantConfigurationEntity buildConfig(TenantConfigurationTypeEnum type,String config) {
        ErpTenantConfigurationEntity mockConfigEntity;
        mockConfigEntity = new ErpTenantConfigurationEntity();
        mockConfigEntity.setTenantId(tenantId);
        mockConfigEntity.setDataCenterId(dataCenterId);
        mockConfigEntity.setType(type.name());
        mockConfigEntity.setConfiguration(config);
        return mockConfigEntity;
    }


    private ErpTenantConfigurationEntity buildGlobalConfig(TenantConfigurationTypeEnum type,String config) {
        ErpTenantConfigurationEntity mockConfigEntity;
        mockConfigEntity = new ErpTenantConfigurationEntity();
        mockConfigEntity.setTenantId("0");
        mockConfigEntity.setDataCenterId("0");
        mockConfigEntity.setChannel("all");
        mockConfigEntity.setType(type.name());
        mockConfigEntity.setConfiguration(config);
        return mockConfigEntity;
    }

    @Test
    void testGetLogStorageRule() {
        //模拟返回的配置
        when(erpTenantConfigurationDao.queryList(any())).thenReturn(Lists.newArrayList(
            buildConfig(TenantConfigurationTypeEnum.LOG_STORAGE_RULE,"RULE_TWO")
        ));

        // 执行测试
        LogStorageRuleEnum result = tenantConfigurationManager.getLogStorageRule(tenantId);

        // 验证结果
        assertEquals(LogStorageRuleEnum.RULE_TWO, result);
        verify(erpTenantConfigurationDao).queryList(any());
    }

    @Test
    void testGetLogStorageRuleWithNullConfig() {
        //模拟返回的配置
        when(erpTenantConfigurationDao.queryList(any())).thenReturn(Lists.newArrayList(
            buildConfig(TenantConfigurationTypeEnum.LOG_STORAGE_RULE,"")
        ));

        // 执行测试
        LogStorageRuleEnum result = tenantConfigurationManager.getLogStorageRule(tenantId);

        // 验证结果
        assertEquals(LogStorageRuleEnum.RULE_ONE, result);
    }

    @Test
    void testGetDoubleConfig() {
        // 测试场景1: 正常获取配置值
        when(erpTenantConfigurationDao.queryList(any())).thenReturn(Lists.newArrayList(
            buildConfig(TenantConfigurationTypeEnum.TENANT_DISPATCHER_REMAIN_MAXIMUM, "{\"key1\":100.0,\"key2\":200.0}")
        ));
        Double result1 = tenantConfigurationManager.getDoubleConfig(TenantConfigurationTypeEnum.TENANT_DISPATCHER_REMAIN_MAXIMUM, 0.0, "key1");
        assertEquals(100.0, result1);

        // 清理缓存
        tenantConfigurationManager.clearCache();

        // 测试场景2: 配置为空时返回默认值
        when(erpTenantConfigurationDao.queryList(any())).thenReturn(Lists.newArrayList(
            buildConfig(TenantConfigurationTypeEnum.TENANT_DISPATCHER_REMAIN_MAXIMUM, null)
        ));
        Double result2 = tenantConfigurationManager.getDoubleConfig(TenantConfigurationTypeEnum.TENANT_DISPATCHER_REMAIN_MAXIMUM, 0.0, "key1");
        assertEquals(0.0, result2);

        // 清理缓存
        tenantConfigurationManager.clearCache();

        // 测试场景3: 配置格式错误时返回默认值
        when(erpTenantConfigurationDao.queryList(any())).thenReturn(Lists.newArrayList(
            buildConfig(TenantConfigurationTypeEnum.TENANT_DISPATCHER_REMAIN_MAXIMUM, "invalid json")
        ));
        Double result3 = tenantConfigurationManager.getDoubleConfig(TenantConfigurationTypeEnum.TENANT_DISPATCHER_REMAIN_MAXIMUM, 0.0, "key1");
        assertEquals(0.0, result3);

        // 清理缓存
        tenantConfigurationManager.clearCache();

        // 测试场景4: 多个key按顺序查找
        when(erpTenantConfigurationDao.queryList(any())).thenReturn(Lists.newArrayList(
            buildConfig(TenantConfigurationTypeEnum.TENANT_DISPATCHER_REMAIN_MAXIMUM, "{\"key1\":100.0,\"key2\":200.0}")
        ));
        Double result4 = tenantConfigurationManager.getDoubleConfig(TenantConfigurationTypeEnum.TENANT_DISPATCHER_REMAIN_MAXIMUM, 0.0, "key3", "key1", "key2");
        assertEquals(100.0, result4);

        // 清理缓存
        tenantConfigurationManager.clearCache();

        // 测试场景5: 参数为null时抛出异常
        assertThrows(ErpSyncDataException.class, () -> {
            tenantConfigurationManager.getDoubleConfig(TenantConfigurationTypeEnum.TENANT_DISPATCHER_REMAIN_MAXIMUM, 0.0, (String[])null);
        });
    }

    @Test
    void testGetIntegerValue() {
        // 测试场景1: 正常获取配置值
        when(erpTenantConfigurationDao.queryList(any())).thenReturn(Lists.newArrayList(
            buildConfig(TenantConfigurationTypeEnum.ERP_LIST_PAGE_SIZE, "100")
        ));
        int result1 = tenantConfigurationManager.getIntegerValue("0",TenantConfigurationTypeEnum.ERP_LIST_PAGE_SIZE);
        assertEquals(100, result1);

        // 清理缓存
        tenantConfigurationManager.clearCache();

        // 测试场景2: 配置为空时返回默认值
        when(erpTenantConfigurationDao.queryList(any())).thenReturn(Lists.newArrayList(
            buildConfig(TenantConfigurationTypeEnum.ERP_LIST_PAGE_SIZE, null)
        ));
        Integer result2 = tenantConfigurationManager.getIntegerValue("0",TenantConfigurationTypeEnum.ERP_LIST_PAGE_SIZE);
        assertNull(result2); // 默认值为0

        // 清理缓存
        tenantConfigurationManager.clearCache();
    }

    @Test
    void testGetWhiteListAndInWhiteList() {
        // 模拟返回的配置
        when(erpTenantConfigurationDao.queryList(any())).thenReturn(Lists.newArrayList(
            buildConfig(TenantConfigurationTypeEnum.NEED_FILL_UP_CRM_OBJ_SET, "obj1;obj2;obj3")
        ));

        // 执行测试 - getWhiteList
        Set<String> result = tenantConfigurationManager.getWhiteList(TenantConfigurationTypeEnum.NEED_FILL_UP_CRM_OBJ_SET);

        // 验证结果 - getWhiteList
        assertNotNull(result);
        assertTrue(result.contains("obj1"));
        assertTrue(result.contains("obj2"));
        assertTrue(result.contains("obj3"));

        // 验证结果 - inWhiteList
        assertTrue(tenantConfigurationManager.inWhiteList("obj1", TenantConfigurationTypeEnum.NEED_FILL_UP_CRM_OBJ_SET));
        assertFalse(tenantConfigurationManager.inWhiteList("obj4", TenantConfigurationTypeEnum.NEED_FILL_UP_CRM_OBJ_SET));

        verify(erpTenantConfigurationDao).queryList(any());
    }

    @Test
    void testGetGroupWhiteMapAndInGroupWhiteList() {
        // 模拟返回的配置
        when(erpTenantConfigurationDao.queryList(any())).thenReturn(Lists.newArrayList(
            buildConfig(TenantConfigurationTypeEnum.NEED_FILL_UP_CRM_OBJ_SET, "{\"group1\":[\"obj1\",\"obj2\"],\"group2\":[\"obj3\",\"obj4\"]}")
        ));

        // 执行测试 - getGroupWhiteMap
        ImmutableMap<String, ImmutableSet<String>> result = tenantConfigurationManager.getGroupWhiteMap(TenantConfigurationTypeEnum.NEED_FILL_UP_CRM_OBJ_SET);

        // 验证结果 - getGroupWhiteMap
        assertNotNull(result);
        assertTrue(result.containsKey("group1"));
        assertTrue(result.get("group1").contains("obj1"));
        assertTrue(result.get("group1").contains("obj2"));
        assertTrue(result.containsKey("group2"));
        assertTrue(result.get("group2").contains("obj3"));
        assertTrue(result.get("group2").contains("obj4"));

        // 验证结果 - inGroupWhiteList
        assertTrue(tenantConfigurationManager.inGroupWhiteList("group1", "obj1", TenantConfigurationTypeEnum.NEED_FILL_UP_CRM_OBJ_SET));
        assertFalse(tenantConfigurationManager.inGroupWhiteList("group1", "obj3", TenantConfigurationTypeEnum.NEED_FILL_UP_CRM_OBJ_SET));

        verify(erpTenantConfigurationDao).queryList(any());
    }

    @Test
    void testGetPushFrequencyLimitPerMin() {
        // 测试场景1: 正常获取配置值
        when(erpTenantConfigurationDao.queryList(any())).thenReturn(Lists.newArrayList(
            buildConfig(TenantConfigurationTypeEnum.PushFrequencyLimitPerMin, String.format("{\"%s\":200}", tenantId))
        ));
        int result1 = tenantConfigurationManager.getPushFrequencyLimitPerMin(tenantId);
        assertEquals(200, result1);

        // 清理缓存
        tenantConfigurationManager.clearCache();

        // 测试场景2: 配置为空时返回默认值
        when(erpTenantConfigurationDao.queryList(any())).thenReturn(Lists.newArrayList(
            buildConfig(TenantConfigurationTypeEnum.PushFrequencyLimitPerMin, null)
        ));
        int result2 = tenantConfigurationManager.getPushFrequencyLimitPerMin(tenantId);
        assertEquals(500, result2); // 默认值为500

        // 清理缓存
        tenantConfigurationManager.clearCache();

        // 测试场景3: 配置格式错误时返回默认值
        when(erpTenantConfigurationDao.queryList(any())).thenReturn(Lists.newArrayList(
            buildConfig(TenantConfigurationTypeEnum.PushFrequencyLimitPerMin, "invalid")
        ));
        int result3 = tenantConfigurationManager.getPushFrequencyLimitPerMin(tenantId);
        assertEquals(500, result3);

        verify(erpTenantConfigurationDao, times(3)).queryList(any());
    }

    @Test
    void testInitCache() {
        // 模拟返回的配置数据
        ErpTenantConfigurationEntity config1 = buildConfig(TenantConfigurationTypeEnum.TEST_JET_CACHE, "config1");
        
        when(erpTenantConfigurationDao.setTenantId(any())).thenReturn(erpTenantConfigurationDao);
        when(erpTenantConfigurationDao.queryList(any())).thenReturn(Lists.newArrayList(config1));
        
        // 调用initCache方法
        tenantConfigurationManager.initCache();
        
        // 验证initCache是否正确初始化缓存
        ErpTenantConfigurationEntity result1 = tenantConfigurationManager.findOne(tenantId, dataCenterId, ErpChannelEnum.ALL.name(), TenantConfigurationTypeEnum.TEST_JET_CACHE.name());
        assertEquals(config1.getConfiguration(), result1.getConfiguration());
        
        verify(erpTenantConfigurationDao,times(2)).setTenantId(any());
        verify(erpTenantConfigurationDao).queryList(any());
    }

    @Test
    void testGetAlertNoticeConfigNull() {

        // 执行测试
        AlertNoticeConfig result = tenantConfigurationManager.getAlertNoticeConfig(tenantId, dataCenterId);

        // 验证结果
        assertNotNull(result);
    }

    @Test
    void testUpdateGlobalConfig() {
        // 准备测试数据
        String type = "testType";
        String configuration = "testConfig";

        // 设置mock行为
        when(erpTenantConfigurationDao.setTenantId(any())).thenReturn(erpTenantConfigurationDao);
        when(erpTenantConfigurationDao.findOneNoCache(any(),any(),any(),any())).thenReturn( null);
        when(erpTenantConfigurationDao.insert(any())).thenReturn(1);

        // 执行测试方法
        tenantConfigurationManager.updateGlobalConfig(type, configuration);

        // 验证结果
        verify(erpTenantConfigurationDao).setTenantId(any());
        verify(erpTenantConfigurationDao).insert(any());
    }



    @Test
    void testUpdateGlobalConfig2() {
        // 准备测试数据
        String type = TenantConfigurationTypeEnum.TEST_JET_CACHE.name();
        String configuration = "testConfig";

        // 设置mock行为
        when(erpTenantConfigurationDao.setTenantId(any())).thenReturn(erpTenantConfigurationDao);
        when(erpTenantConfigurationDao.findOneNoCache(any(),any(),any(),any())).thenReturn( buildConfig(TenantConfigurationTypeEnum.TEST_JET_CACHE, configuration));
        when(erpTenantConfigurationDao.updateById(any())).thenReturn(1);

        // 执行测试方法
        tenantConfigurationManager.updateGlobalConfig(type, configuration);

        // 验证结果
        verify(erpTenantConfigurationDao).setTenantId(any());
        verify(erpTenantConfigurationDao).updateById(any());
    }



    @Test
    void tesUpsertConfigIgnoreChannel() {
        // 准备测试数据
        String configuration = "testConfig";

        // 设置mock行为
        when(erpTenantConfigurationDao.setTenantId(any())).thenReturn(erpTenantConfigurationDao);
        when(erpTenantConfigurationDao.findOneNoCache(any(),any(),any(),any())).thenReturn( null);
        when(erpTenantConfigurationDao.insert(any())).thenReturn(1);

        // 执行测试方法
        tenantConfigurationManager.upsertConfigIgnoreChannel(tenantId, dataCenterId,TenantConfigurationTypeEnum.TEST_JET_CACHE, configuration);

        // 验证结果
        verify(erpTenantConfigurationDao).setTenantId(any());
        verify(erpTenantConfigurationDao).insert(any());
    }




    @Test
    void tesUpsertConfigIgnoreChannel2() {
        // 准备测试数据
        String configuration = "testConfig";

        // 设置mock行为
        when(erpTenantConfigurationDao.setTenantId(any())).thenReturn(erpTenantConfigurationDao);
        when(erpTenantConfigurationDao.findOneNoCache(any(),any(),any(),any())).thenReturn( buildConfig(TenantConfigurationTypeEnum.TEST_JET_CACHE, configuration));
        when(erpTenantConfigurationDao.updateById(any())).thenReturn(1);

        // 执行测试方法
        tenantConfigurationManager.upsertConfigIgnoreChannel(tenantId, dataCenterId,TenantConfigurationTypeEnum.TEST_JET_CACHE, configuration);

        // 验证结果
        verify(erpTenantConfigurationDao).setTenantId(any());
        verify(erpTenantConfigurationDao).updateById(any());
    }

    @Test
    void testGetVipTenantIds_returnsConfiguredValue() {
        Set<String> actual = tenantConfigurationManager.getVipTenantIds();
        assertEquals(ConfigCenter.VIP_ENVIROMENT_TENANT, actual);
    }

    @Test
    void testGetGrayTenantIds_returnsConfiguredValue() {
        Set<String> actual = tenantConfigurationManager.getGrayTenantIds();
        assertEquals(ConfigCenter.GRAY_TENANTS, actual);
    }

    @Test
    void testGetColorLogTenantIds_returnsEmptySetWhenConfigIsNull() {
        when(erpTenantConfigurationDao.queryList(any())).thenReturn(Collections.emptyList());
        Set<String> actual = tenantConfigurationManager.getColorLogTenantIds();
        assertEquals(new HashSet<>(), actual);
    }

    @Test
    void testGetColorLogTenantIds_returnsParsedValue() {
        Set<String> expected = new HashSet<>();
        expected.add("1");
        expected.add("2");
        when(erpTenantConfigurationDao.queryList(any())).thenReturn(Collections.singletonList(buildGlobalConfig(TenantConfigurationTypeEnum.COLOR_EI_LOG_LIST, "1;2")));
        Set<String> actual = tenantConfigurationManager.getColorLogTenantIds();
        assertEquals(expected, actual);
    }
} 