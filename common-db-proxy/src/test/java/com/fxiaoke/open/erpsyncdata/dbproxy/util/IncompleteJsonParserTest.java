package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import static org.junit.jupiter.api.Assertions.*;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.List;

@Slf4j
public class IncompleteJsonParserTest {

    ObjectNode emptyNode = JacksonUtil.get().createObjectNode();

    @Test
    void testIncompleteObject() {
        String incomplete1 = "{\"name\": \"John <PERSON>\", \"age\": 30, \"city\": \"New";
        JsonNode result1 = IncompleteJsonParser.parse(incomplete1);
        assertNotNull(result1);
        assertEquals("John Doe", result1.get("name").asText());
        assertEquals(30, result1.get("age").asInt());
        assertNull(result1.get("city")); // Incomplete field should be ignored
    }

    @Test
    void testIncompleteArray() {
        String incomplete2 = "{\"tags\": [\"java\", \"json\", \"jac";
        JsonNode result2 = IncompleteJsonParser.parse(incomplete2);
        assertNotNull(result2);
        assertTrue(result2.has("tags"));
        assertEquals(2, result2.get("tags").size());
        assertEquals("java", result2.get("tags").get(0).asText());
        assertEquals("json", result2.get("tags").get(1).asText());
    }

    @Test
    void testNestedIncompleteObject() {
        String incomplete3 = "{\"user\": {\"id\": 123, \"role\": \"admin\"}, \"data\": {\"value\": 100, \"status\": \"pending";
        JsonNode result3 = IncompleteJsonParser.parse(incomplete3);
        assertNotNull(result3);
        assertTrue(result3.has("user"));
        assertEquals(123, result3.get("user").get("id").asInt());
        assertEquals("admin", result3.get("user").get("role").asText());
        assertTrue(result3.has("data"));
        assertEquals(100, result3.get("data").get("value").asInt());
        assertNull(result3.get("data").get("status")); // Incomplete field
    }

    @Test
    void testCompleteJson() {
        String completeJson = "{\"status\": \"complete\", \"items\": [1, 2, 3]}";
        JsonNode result4 = IncompleteJsonParser.parse(completeJson);
        assertNotNull(result4);
        assertEquals("complete", result4.get("status").asText());
        assertEquals(3, result4.get("items").size());
        assertEquals(1, result4.get("items").get(0).asInt());
        assertEquals(2, result4.get("items").get(1).asInt());
        assertEquals(3, result4.get("items").get(2).asInt());
    }

    @Test
    void testEmptyInput() {
        assertNull(IncompleteJsonParser.parse(null));
        assertNull(IncompleteJsonParser.parse(""));
        assertNull(IncompleteJsonParser.parse("   "));
    }

    @Test
    void testSpecialCharacters() {
        String jsonWithSpecialChars = "{\"message\": \"Hello@World!#特殊字符测试\", \"id\": 999}";
        JsonNode result = IncompleteJsonParser.parse(jsonWithSpecialChars);
        assertNotNull(result);
        assertEquals("Hello@World!#特殊字符测试", result.get("message").asText());
        assertEquals(999, result.get("id").asInt());
    }

    @Test
    void testDeeplyNestedStructure() {
        String nestedJson = "{\"a\":{\"b\":{\"c\":{\"d\":{\"e\":{\"value\":123}}}}}}";
        JsonNode result = IncompleteJsonParser.parse(nestedJson);
        assertNotNull(result);
        assertEquals(123, result.get("a").get("b").get("c").get("d").get("e").get("value").asInt());
    }

    @Test
    void testArrayWithMixedTypes() {
        String jsonArray = "{\"values\":[\"string\", 42, true, false, null]}";
        JsonNode result = IncompleteJsonParser.parse(jsonArray);
        assertNotNull(result);
        assertTrue(result.has("values"));
        assertEquals(5, result.get("values").size());
        
        assertEquals("string", result.get("values").get(0).asText());
        assertEquals(42, result.get("values").get(1).asInt());
        assertTrue(result.get("values").get(2).asBoolean());
        assertFalse(result.get("values").get(3).asBoolean());
        assertEquals("null",result.get("values").get(4).asText()); // null should be preserved as null
    }

    @Test
    void testNumericTypes() {
        String json = "{\"int\": 123, \"long\": 9223372036854775807, \"float\": 3.14, \"double\": 1.7976931348623157e+308, \"bigDecimal\": 1.2345678901234567890, \"bigInteger\": 9223372036854775807}";
        JsonNode result = IncompleteJsonParser.parse(json);
        assertNotNull(result);
        
        assertEquals(123, result.get("int").asInt());
        assertEquals(9223372036854775807L, result.get("long").asLong());
        assertEquals(3.14f, result.get("float").asDouble(), 0.001);
        assertEquals(1.7976931348623157e+308, result.get("double").asDouble(), 0.001);
        assertEquals("1.2345678901234567890", result.get("bigDecimal").decimalValue().toPlainString());
        assertEquals("9223372036854775807", result.get("bigInteger").asText());
    }

    @Test
    void testNumericEdgeCases() {
        String json = "{\"minInt\": -2147483648, \"maxInt\": 2147483647, \"minLong\": -9223372036854775808, \"maxLong\": 9223372036854775807}";
        JsonNode result = IncompleteJsonParser.parse(json);
        assertNotNull(result);
        
        assertEquals(-2147483648, result.get("minInt").asInt());
        assertEquals(2147483647, result.get("maxInt").asInt());
        assertEquals(-9223372036854775808L, result.get("minLong").asLong());
        assertEquals(9223372036854775807L, result.get("maxLong").asLong());
    }

    @Test
    void testInvalidJsonStructure() {
        String invalidJson = "{\"key\":}"; // Value missing after colon
        JsonNode result = IncompleteJsonParser.parse(invalidJson);
        assertEquals(emptyNode,result); // Expecting empty node for invalid JSON
    }

    @Test
    void testInvalidJsonObjTree() throws JsonProcessingException {
        String invalidJson = "{\n  \"erpObjectApiName\": \"WorkflowCreateBeCurrent\",\n  \"erpObjectName\": \"销售报价流程\",\n  \"fields\": [\n    \"wfid;流程ID;t;1;\",\n    \"appmark;系统标识;t;3;\",\n    \"djbh;单据编号;t;3;\",\n    \"requestname;标题;t;3"; // Missing comma between key-value pairs
        JsonNode result = IncompleteJsonParser.parse(invalidJson);
        GenObjTreeNode genObjTreeNode = JacksonUtil.get().treeToValue(result, GenObjTreeNode.class);
        log.info("result:{}",result);
        log.info("genObjTreeNode:{}",genObjTreeNode);
        assertNotNull(genObjTreeNode);
        assertEquals("WorkflowCreateBeCurrent", genObjTreeNode.getErpObjectApiName());
        assertEquals("销售报价流程", genObjTreeNode.getErpObjectName());
        assertEquals(3, genObjTreeNode.getFields().size());
    }

    @Data
    @Slf4j
    static class GenObjTreeNode {
        private String erpObjectApiName;
        private String erpObjectName;
        private List<String> fields;
        private List<GenObjTreeNode> children;
    }
}