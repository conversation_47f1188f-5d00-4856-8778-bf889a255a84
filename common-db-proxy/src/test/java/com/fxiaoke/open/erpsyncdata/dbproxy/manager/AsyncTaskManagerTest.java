package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.AsyncTaskResult;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisCacheManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import lombok.Data;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import redis.clients.jedis.params.SetParams;

import java.util.concurrent.TimeUnit;

import static com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant.REDIS_ASYNC_TASK_CACHE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class AsyncTaskManagerTest {

    @Mock
    private RedisCacheManager redisCacheManager;

    @InjectMocks
    private AsyncTaskManager asyncTaskManager;

    private static final String TENANT_ID = "test_tenant";
    private static final String TASK_KEY = "test_task";
    private static final String TASK_NAME = "测试任务";
    private static final long WAIT_SECOND = 1;
    private static final long EXECUTE_TIMEOUT_SECOND = 5;
    private static final long RESULT_EXPIRE_SECOND = 3600;

    @BeforeEach
    void setUp() {
        // 设置通用模拟行为
    }

    @Test
    @DisplayName("测试任务快速完成时直接返回结果")
    void testExecuteTaskCompletesQuickly() {
        // 准备
        String resultKey = REDIS_ASYNC_TASK_CACHE + TENANT_ID + ":" + TASK_KEY;
        when(redisCacheManager.setCache(eq(resultKey), anyString(), any(SetParams.class))).thenReturn(true);

        // 执行
        AsyncTaskResult<String> result = asyncTaskManager.executeTask(
                TENANT_ID,
                TASK_KEY,
                TASK_NAME,
                WAIT_SECOND,
                EXECUTE_TIMEOUT_SECOND,
                RESULT_EXPIRE_SECOND,
                taskResult -> {
                    taskResult.success("任务成功完成");
                }
        );

        // 验证
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("任务成功完成", result.getData());
        assertEquals(TASK_KEY, result.getTaskKey());
        assertEquals(TASK_NAME, result.getTaskName());
        
        verify(redisCacheManager).setCache(eq(resultKey), anyString(), any(SetParams.class));
        verify(redisCacheManager).delCache(resultKey);
    }

    @Test
    @DisplayName("测试任务执行中状态")
    void testExecuteTaskInProgress() {
        // 准备
        String resultKey = REDIS_ASYNC_TASK_CACHE + TENANT_ID + ":" + TASK_KEY;
        when(redisCacheManager.setCache(eq(resultKey), anyString(), any(SetParams.class))).thenReturn(false);

        // 执行
        AsyncTaskResult<String> result = asyncTaskManager.executeTask(
                TENANT_ID,
                TASK_KEY,
                TASK_NAME,
                WAIT_SECOND,
                EXECUTE_TIMEOUT_SECOND,
                RESULT_EXPIRE_SECOND,
                taskResult -> {
                    // 模拟任务执行
                }
        );

        // 验证
        assertNotNull(result);
        assertEquals(TASK_KEY, result.getTaskKey());
        assertEquals(TASK_NAME, result.getTaskName());
        
        verify(redisCacheManager).setCache(eq(resultKey), anyString(), any(SetParams.class));
        verify(redisCacheManager, never()).delCache(resultKey);
    }

    @Test
    @DisplayName("测试任务执行出现异常")
    void testExecuteTaskWithException() {
        // 准备
        String resultKey = REDIS_ASYNC_TASK_CACHE + TENANT_ID + ":" + TASK_KEY;
        when(redisCacheManager.setCache(eq(resultKey), anyString(), any(SetParams.class))).thenReturn(true);

        // 执行
        AsyncTaskResult<String> result = asyncTaskManager.executeTask(
                TENANT_ID,
                TASK_KEY,
                TASK_NAME,
                WAIT_SECOND,
                EXECUTE_TIMEOUT_SECOND,
                RESULT_EXPIRE_SECOND,
                taskResult -> {
                    throw new RuntimeException("任务执行异常");
                }
        );

        // 验证
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertNotNull(result.getErrMsg());
        assertTrue(result.getErrMsg().contains("任务执行异常"));
        
        verify(redisCacheManager).setCache(eq(resultKey), anyString(), any(SetParams.class));
        verify(redisCacheManager).delCache(resultKey);
    }

    @Test
    @DisplayName("测试任务执行超时")
    void testExecuteTaskTimeout() throws Exception {
        // 准备
        String resultKey = REDIS_ASYNC_TASK_CACHE + TENANT_ID + ":" + TASK_KEY;
        when(redisCacheManager.setCache(eq(resultKey), anyString(), any(SetParams.class))).thenReturn(true);
        
        // 执行
        AsyncTaskResult<String> result = asyncTaskManager.executeTask(
                TENANT_ID,
                TASK_KEY,
                TASK_NAME,
                1, // 短超时
                10, // 长执行超时
                RESULT_EXPIRE_SECOND,
                taskResult -> {
                    try {
                        // 模拟长时间任务
                        Thread.sleep(2000);
                        taskResult.success("延迟完成");
                    } catch (InterruptedException e) {
                        // 忽略中断
                    }
                }
        );

        // 验证
        assertNotNull(result);
        assertEquals(TASK_KEY, result.getTaskKey());
        
        // 等待后台线程执行结束
        TimeUnit.SECONDS.sleep(3);
        
        verify(redisCacheManager).setCache(eq(resultKey), anyString(), any(SetParams.class));
        // 验证结果被存入Redis
        verify(redisCacheManager).setCache(eq(resultKey), anyString(), eq(RESULT_EXPIRE_SECOND));
    }

    @Test
    @DisplayName("测试从缓存获取成功的任务结果")
    void testGetAsyncTaskResultCacheSuccess() {
        // 准备
        String resultKey = REDIS_ASYNC_TASK_CACHE + TENANT_ID + ":" + TASK_KEY;
        AsyncTaskResult<String> mockResult = new AsyncTaskResult<>();
        mockResult.setTaskKey(TASK_KEY);
        mockResult.success("缓存数据");
        
        when(redisCacheManager.getCache(resultKey)).thenReturn(JacksonUtil.toJson(mockResult));
        
        // 执行
        AsyncTaskResult<String> result = asyncTaskManager.getAsyncTaskResultCache(
                TENANT_ID,
                TASK_KEY,
                String.class
        );
        
        // 验证
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(TASK_KEY, result.getTaskKey());
        
        verify(redisCacheManager).getCache(resultKey);
    }

    @Test
    @DisplayName("测试从缓存获取失败的任务结果")
    void testGetAsyncTaskResultCacheFailure() {
        // 准备
        String resultKey = REDIS_ASYNC_TASK_CACHE + TENANT_ID + ":" + TASK_KEY;
        AsyncTaskResult<String> mockResult = new AsyncTaskResult<>();
        mockResult.setTaskKey(TASK_KEY);
        mockResult.error("data is null");
        
        when(redisCacheManager.getCache(resultKey)).thenReturn(JacksonUtil.toJson(mockResult));
        
        // 执行
        AsyncTaskResult<String> result = asyncTaskManager.getAsyncTaskResultCache(
                TENANT_ID,
                TASK_KEY,
                String.class
        );
        
        // 验证
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals(TASK_KEY, result.getTaskKey());
        
        verify(redisCacheManager).getCache(resultKey);
    }

    @Test
    @DisplayName("测试缓存中不存在任务结果")
    void testGetAsyncTaskResultCacheNotFound() {
        // 准备
        String resultKey = REDIS_ASYNC_TASK_CACHE + TENANT_ID + ":" + TASK_KEY;
        when(redisCacheManager.getCache(resultKey)).thenReturn(null);
        
        // 执行
        AsyncTaskResult<String> result = asyncTaskManager.getAsyncTaskResultCache(
                TENANT_ID,
                TASK_KEY,
                String.class
        );
        
        // 验证
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals(TASK_KEY, result.getTaskKey());
        assertTrue(result.getErrMsg().contains("not found task"));
        
        verify(redisCacheManager).getCache(resultKey);
    }


    @Test
    @DisplayName("测试获取 执行prompt 异常结果")
    void testGetAsyncTaskResultExecutePrompt() {
        // 准备
        String resultKey = REDIS_ASYNC_TASK_CACHE + TENANT_ID + ":" + TASK_KEY;
        AsyncTaskResult<String> mockResult = new AsyncTaskResult<>();
        mockResult.setTaskKey(TASK_KEY);
        mockResult.success("缓存数据");

        when(redisCacheManager.getCache(resultKey)).thenReturn("{\"errCode\":\"s306240000\",\"errMsg\":\"java.net.SocketException: Socket closed\",\"traceMsg\":\"fs-erp-sync-data/68366eb25c58af5b18c37735\",\"data\":null,\"taskKey\":\"executePrompt-68366eb25c58af5b18c37736\",\"taskName\":\"executePrompt-68366eb25c58af5b18c37736\",\"success\":false}");

        // 执行
        AsyncTaskResult<ExecutePromptResult> result = asyncTaskManager.getAsyncTaskResultCache(
                TENANT_ID,
                TASK_KEY,
                ExecutePromptResult.class
        );

        // 验证
        assertNotNull(result);
        assertFalse(result.isSuccess());

        verify(redisCacheManager).getCache(resultKey);
    }

    @Test
    @DisplayName("测试获取 执行prompt 成功结果")
    void testGetAsyncTaskResultExecutePromptSuccess() {

        JSONObject jsonObject = JSON.parseObject("{\"errCode\":\"s306240000\",\"errMsg\":\"java.net.SocketException: Socket closed\",\"traceMsg\":\"fs-erp-sync-data/68366eb25c58af5b18c37735\",\"data\":null,\"taskKey\":\"executePrompt-68366eb25c58af5b18c37736\",\"taskName\":\"executePrompt-68366eb25c58af5b18c37736\",\"success\":false}");

        assertNotNull(jsonObject);
    }



    @Data
    static
    class ExecutePromptResult {
        private String message;
        private String type;
        /**
         * type时json时，会把message解析成成json对象
         */
        private JSONObject jsonResult;
    }
}
