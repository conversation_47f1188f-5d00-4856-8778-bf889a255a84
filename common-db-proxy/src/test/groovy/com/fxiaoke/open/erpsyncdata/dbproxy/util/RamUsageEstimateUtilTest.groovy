package com.fxiaoke.open.erpsyncdata.dbproxy.util

import cn.hutool.core.io.unit.DataSizeUtil
import com.fxiaoke.open.erpsyncdata.preprocess.util.RamUsageEstimateUtil
import spock.lang.Specification

/**
 *
 * <AUTHOR> (^_−)☆
 */
class RamUsageEstimateUtilTest extends Specification {
    def "SizeOfObjectIgnoreException"() {

        def str = """
["调用接口成功，但明细对象【PriceBookProductObj】的数据【[66e0140cdf1deb00070fe435, 66e0140cdf1deb00070fe437, 66e0140cdf1deb00070fe42a, 66e01410df1deb00070fe700, 66e01410df1deb00070fe702, 66e01410df1deb00070fe704, 66e0140cdf1deb00070fe42d, 66e01410df1deb00070fe706, 66e01410df1deb00070fe708, 66e0140cdf1deb00070fe441, 66e0140cdf1deb00070fe444, 66e0140cdf1deb00070fe446, 66e0140cdf1deb00070fe448, 66e0140cdf1deb00070fe43a, 66e0140cdf1deb00070fe43c, 66e01417df1deb00070feb7d, 66e01410df1deb00070fe710, 66e01417df1deb00070feb7b, 66e01410df1deb00070fe712, 66e01410df1deb00070fe714, 66e01410df1deb00070fe716, 66e0140cdf1deb00070fe43e, 66e01410df1deb00070fe719, 66e0140cdf1deb00070fe453, 66e0140cdf1deb00070fe455, 66e01403df1deb00070fdd5e, 66e0140cdf1deb00070fe451, 66e01410df1deb00070fe70a, 66e0140cdf1deb00070fe457, 66e01417df1deb00070feb79, 66e0140cdf1deb00070fe459, 66e01417df1deb00070feb77, 66e0140cdf1deb00070fe44a, 66e0140cdf1deb00070fe44c, 66e01417df1deb00070feb6a, 66e0140cdf1deb00070fe44e, 66e01417df1deb00070feb6f, 66e0140cdf1deb00070fe464, 66e0140cdf1deb00070fe466, 66e01417df1deb00070feb83, 66e0140cdf1deb00070fe461, 66e01417df1deb00070feb81, 66e0140cdf1deb00070fe468, 66e01417df1deb00070feb67, 66e0140cdf1deb00070fe45b, 66e01417df1deb00070feb5d, 66e01417df1deb00070feb5b, 66e01415df1deb00070fea01, 66e0140cdf1deb00070fe45f, 66e01417df1deb00070feb5f, 66e01417df1deb00070feb75, 66e0140cdf1deb00070fe474, 66e01417df1deb00070feb73, 66e0140cdf1deb00070fe476, 66e01417df1deb00070feb71, 66e0140cdf1deb00070fe470, 66e0140cdf1deb00070fe472, 66e01417df1deb00070feb59, 66e01403df1deb00070fdd2a, 66e01417df1deb00070feb57, 66e01417df1deb00070feb4b, 66e01403df1deb00070fdd39, 66e01403df1deb00070fdd34, 66e01403df1deb00070fdd36, 66e01417df1deb00070feb4e, 66e01417df1deb00070feb65, 66e0140cdf1deb00070fe400, 66e01417df1deb00070feb63, 66e01403df1deb00070fdd2c, 66e01417df1deb00070feb61, 66e01403df1deb00070fdd2e, 66e0140cdf1deb00070fe408, 66e01417df1deb00070feb49, 66e0140cdf1deb00070fe402, 66e01417df1deb00070feb46, 66e0140cdf1deb00070fe404, 66e01417df1deb00070feb44, 66e01403df1deb00070fdd26, 66e01417df1deb00070feb3b, 66e01403df1deb00070fdd30, 66e01403df1deb00070fdd32, 66e01417df1deb00070feb3e, 66e01403df1deb00070fdd1f, 66e01417df1deb00070feb54, 66e01417df1deb00070feb51, 66e0140cdf1deb00070fe410, 66e01403df1deb00070fdd1d, 66e0140cdf1deb00070fe416, 66e01410df1deb00070fe70c, 66e0140cdf1deb00070fe418, 66e01410df1deb00070fe70e, 66e01417df1deb00070feb38, 66e0140cdf1deb00070fe412, 66e01417df1deb00070feb36, 66e0140cdf1deb00070fe414, 66e01417df1deb00070feb34, 66e0140edf1deb00070fe58f, 66e01417df1deb00070feb2a, 66e01410df1deb00070fe721, 66e01410df1deb00070fe723, 66e0140cdf1deb00070fe40e, 66e01410df1deb00070fe725, 66e0140cdf1deb00070fe40a, 66e0140edf1deb00070fe58b, 66e01417df1deb00070feb2e, 66e0140cdf1deb00070fe40c, 66e0140edf1deb00070fe58d, 66e01417df1deb00070feb2c, 66e01417df1deb00070feb42, 66e0140edf1deb00070fe597, 66e0140cdf1deb00070fe421, 66e0140edf1deb00070fe599, 66e01410df1deb00070fe71b, 66e0140cdf1deb00070fe428, 66e01410df1deb00070fe71d, 66e01410df1deb00070fe71f, 66e0140cdf1deb00070fe424, 66e01417df1deb00070feb25, 66e0140cdf1deb00070fe426, 66e01417df1deb00070feb22, 66e0140cdf1deb00070fe41a, 66e01403df1deb00070fdd45, 66e0140edf1deb00070fe59b, 66e01417df1deb00070feb1e, 66e0140cdf1deb00070fe41c, 66e0140edf1deb00070fe59d, 66e01417df1deb00070feb1c, 66e0140cdf1deb00070fe41e, 66e01417df1deb00070feb1a, 66e0140cdf1deb00070fe430, 66e01417df1deb00070feb32, 66e0140cdf1deb00070fe433, 66e01417df1deb00070feb30, 66e01403df1deb00070fdd18, 66e01403df1deb00070fdd14, 66e01403df1deb00070fdd21, 66e01403df1deb00070fdd0c, 66e01403df1deb00070fdd09, 66e01403df1deb00070fdd04, 66e01403df1deb00070fdd06, 66e01403df1deb00070fdd01, 66e01403df1deb00070fdd10, 66e01406df1deb00070fe0ba, 66e01406df1deb00070fe0bc, 66e01406df1deb00070fe0c6, 66e01406df1deb00070fe0c8, 66e01406df1deb00070fe0be, 66e01406df1deb00070fe0aa, 66e01406df1deb00070fe0b4, 66e01406df1deb00070fe0b6, 66e01406df1deb00070fe0b8, 66e01406df1deb00070fe0c0, 66e01406df1deb00070fe0c2, 66e01406df1deb00070fe0c4, 66e01406df1deb00070fe0ac, 66e01406df1deb00070fe0ae, 66e01406df1deb00070fe0ca, 66e01406df1deb00070fe0cd, 66e01406df1deb00070fe0cf, 66e0140adf1deb00070fe273, 66e0140adf1deb00070fe271, 66e0140adf1deb00070fe277, 66e0140adf1deb00070fe275, 66e0140adf1deb00070fe279, 66e0140adf1deb00070fe291, 66e0140adf1deb00070fe295, 66e0140adf1deb00070fe293, 66e0140adf1deb00070fe299, 66e0140adf1deb00070fe297, 66e0140adf1deb00070fe27b, 66e0140adf1deb00070fe27f, 66e01415df1deb00070fea7d, 66e0140adf1deb00070fe27d, 66e01415df1deb00070fea7b, 66e01415df1deb00070fea7f, 66e0140adf1deb00070fe283, 66e0140adf1deb00070fe281, 66e0140adf1deb00070fe287, 66e0140adf1deb00070fe285, 66e0140adf1deb00070fe289, 66e01415df1deb00070fea8b, 66e0140adf1deb00070fe26e, 66e0140adf1deb00070fe29d, 66e0140adf1deb00070fe29b, 66e0140adf1deb00070fe29f, 66e0140adf1deb00070fe28d, 66e0140adf1deb00070fe28b, 66e01414df1deb00070fe9b7, 66e01417df1deb00070feb18, 66e01414df1deb00070fe9b9, 66e01417df1deb00070feb16, 66e01417df1deb00070feb14, 66e01417df1deb00070feb12, 66e01414df1deb00070fe9b1, 66e01414df1deb00070fe9b3, 66e01415df1deb00070fea57, 66e01415df1deb00070fea54, 66e01415df1deb00070fea49, 66e01414df1deb00070fe9af, 66e01415df1deb00070fea47, 66e01417df1deb00070feb0c, 66e01417df1deb00070feb0a, 66e01417df1deb00070feb20, 66e01414df1deb00070fe9ab, 66e01415df1deb00070fea3e, 66e01415df1deb00070fea3c, 66e01414df1deb00070fe9a5, 66e01417df1deb00070feb06, 66e01414df1deb00070fe9a7, 66e01417df1deb00070feb04, 66e01414df1deb00070fe9a9, 66e01417df1deb00070feb02, 66e01415df1deb00070fea63, 66e01415df1deb00070fea61, 66e01414df1deb00070fe9a1, 66e01415df1deb00070fea67, 66e01415df1deb00070fea65, 66e01417df1deb00070feb08, 66e01415df1deb00070fea59, 66e01415df1deb00070fea4b, 66e01415df1deb00070fea4f, 66e01415df1deb00070fea4d, 66e01419df1deb00070feca8, 66e01419df1deb00070feca6, 66e01415df1deb00070fea70, 66e01419df1deb00070feca0, 66e01415df1deb00070fea75, 66e01415df1deb00070fea73, 66e01415df1deb00070fea79, 66e01415df1deb00070fea77, 66e01419df1deb00070feca3, 66e01415df1deb00070fea69, 66e01415df1deb00070fea5b, 66e01415df1deb00070fea5f, 66e01415df1deb00070fea5d, 66e01415df1deb00070fea82, 66e01415df1deb00070fea85, 66e01415df1deb00070fea87, 66e01415df1deb00070fea6d, 66e01415df1deb00070fea6b, 66e01419df1deb00070fecc8, 66e01419df1deb00070fecc2, 66e01419df1deb00070fecc0, 66e01419df1deb00070fecc6, 66e01415df1deb00070fea10, 66e01419df1deb00070fecc4, 66e01415df1deb00070fea06, 66e01415df1deb00070fea04, 66e01415df1deb00070fea08, 66e01419df1deb00070fecaa, 66e01419df1deb00070fecae, 66e01419df1deb00070fecac, 66e01419df1deb00070fecb8, 66e01415df1deb00070fea20, 66e01419df1deb00070fecb0, 66e01419df1deb00070fecb6, 66e01415df1deb00070fea16, 66e01415df1deb00070fea14, 66e01415df1deb00070fea18, 66e01415df1deb00070fea0a, 66e01415df1deb00070fea30, 66e01419df1deb00070fece4, 66e01419df1deb00070fece2, 66e01415df1deb00070fea34, 66e01415df1deb00070fea32, 66e01419df1deb00070fece0, 66e01419df1deb00070feccb, 66e01415df1deb00070fea1a, 66e01419df1deb00070fecce, 66e01415df1deb00070fea2e, 66e01415df1deb00070fea42, 66e01419df1deb00070fecd4, 66e01419df1deb00070fecd1, 66e01415df1deb00070fea40, 66e01415df1deb00070fea45, 66e01419df1deb00070fecd8, 66e01419df1deb00070fecd6, 66e01415df1deb00070fea38, 66e01419df1deb00070fecba, 66e01415df1deb00070fea2c, 66e01419df1deb00070fecbe, 66e01415df1deb00070fea2a, 66e01419df1deb00070fecbc, 66e01412df1deb00070fe79f, 66e01412df1deb00070fe79a, 66e01412df1deb00070fe79c, 66e0140adf1deb00070fe2dc, 66e0140adf1deb00070fe2da, 66e0140adf1deb00070fe2df, 66e0140adf1deb00070fe2e2, 66e01403df1deb00070fde9c, 66e01403df1deb00070fde4a, 66e01403df1deb00070fde58, 66e01403df1deb00070fde56, 66e01403df1deb00070fde62, 66e01403df1deb00070fde60, 66e01403df1deb00070fde4e, 66e01403df1deb00070fde46, 66e01403df1deb00070fde52, 66e01403df1deb00070fde3c, 66e01403df1deb00070fde80, 66e01403df1deb00070fde5a, 66e01403df1deb00070fde5c, 66e01412df1deb00070fe78f, 66e01412df1deb00070fe78c, 66e01412df1deb00070fe796, 66e01412df1deb00070fe798, 66e01412df1deb00070fe791, 66e01414df1deb00070fe9bb, 66e01403df1deb00070fde5e, 66e01414df1deb00070fe9bd, 66e01412df1deb00070fe794, 66e01408df1deb00070fe1bf, 66e01408df1deb00070fe1bd, 66e01408df1deb00070fe1c3, 66e01408df1deb00070fe1c5, 66e01408df1deb00070fe1c1, 66e01408df1deb00070fe1ae, 66e01408df1deb00070fe1aa, 66e01408df1deb00070fe1ac, 66e01408df1deb00070fe1b6, 66e01408df1deb00070fe1b8, 66e01408df1deb00070fe1b2, 66e01408df1deb00070fe1b4, 66e01408df1deb00070fe1b0, 66e01403df1deb00070fde37, 66e01403df1deb00070fde35, 66e01403df1deb00070fde41, 66e01408df1deb00070fe1a8, 66e01403df1deb00070fde2f, 66e01408df1deb00070fe1a4, 66e01408df1deb00070fe1a6, 66e01403df1deb00070fde2b, 66e01408df1deb00070fe1a0, 66e01403df1deb00070fde2d, 66e01408df1deb00070fe1a2, 66e01403df1deb00070fde29, 66e01403df1deb00070fde25, 66e01403df1deb00070fde27, 66e01403df1deb00070fde21, 66e01403df1deb00070fde23, 66e01403df1deb00070fde1e, 66e01403df1deb00070fde1a, 66e01410df1deb00070fe69b, 66e0140cdf1deb00070fe3fe, 66e01410df1deb00070fe69d, 66e01410df1deb00070fe69f, 66e0140cdf1deb00070fe3fa, 66e0140cdf1deb00070fe3fc, 66e01419df1deb00070fec39, 66e01419df1deb00070fec33, 66e01419df1deb00070fec37, 66e01419df1deb00070fec35, 66e01410df1deb00070fe691, 66e01410df1deb00070fe693, 66e01410df1deb00070fe695, 66e01410df1deb00070fe697, 66e01410df1deb00070fe699, 66e01410df1deb00070fe68c, 66e01410df1deb00070fe68f, 66e01419df1deb00070fec61, 66e01419df1deb00070fec4d, 66e01419df1deb00070fec4b, 66e01419df1deb00070fec4f, 66e01403df1deb00070fdcfd, 66e01403df1deb00070fdcff, 66e01403df1deb00070fdcfb, 66e01419df1deb00070fec55, 66e01419df1deb00070fec53, 66e01419df1deb00070fec59, 66e01419df1deb00070fec57, 66e01403df1deb00070fdcf5, 66e01419df1deb00070fec51, 66e01419df1deb00070fec3b, 66e01419df1deb00070fec3f, 66e01419df1deb00070fec3d, 66e01419df1deb00070fec49, 66e01419df1deb00070fec43, 66e01419df1deb00070fec41, 66e01403df1deb00070fdcf7, 66e01419df1deb00070fec47, 66e01403df1deb00070fdcf9, 66e01419df1deb00070fec45, 6..."]"""

        def size = RamUsageEstimateUtil.sizeOfObjectIgnoreException(str)
        println(DataSizeUtil.format(size))
        expect:
        size>10
    }
}
