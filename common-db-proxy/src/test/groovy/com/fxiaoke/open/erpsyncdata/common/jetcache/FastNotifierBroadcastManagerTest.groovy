package com.fxiaoke.open.erpsyncdata.common.jetcache

import com.alicp.jetcache.*
import com.alicp.jetcache.embedded.LinkedHashMapCacheBuilder
import com.alicp.jetcache.support.*
import com.fxiaoke.common.redisson.RedissonFactoryBean
import groovy.util.logging.Slf4j
import junit.framework.AssertionFailedError
import org.redisson.api.RedissonClient
import spock.lang.Ignore
import spock.lang.Specification

import java.util.concurrent.TimeUnit
import java.util.function.Supplier

/**
 *
 * <AUTHOR> (^_−)☆
 */
// TODO: Ignore Redis
@Ignore
@Slf4j
class FastNotifierBroadcastManagerTest extends Specification {
    private static RedissonClient client

    void setup() {
        System.setProperty("process.profile", "fstest")
        System.setProperty("process.name", "fs-erp-sync-data")
        def bean = new RedissonFactoryBean()
        bean.setConfigName("erp-sync-data-all")
        bean.afterPropertiesSet()
        client = bean.getObject()
    }

    void cleanup() {
        client.shutdown();
    }

    protected void testBroadcastManager(BroadcastManager manager) throws Exception {
        when:
        CacheMessage cm = new CacheMessage();
        cm.setArea("area");
        cm.setCacheName("cacheName");
        cm.setKeys("K");
        cm.setValues("V1");
        cm.setType(100);

        Cache c1 = LinkedHashMapCacheBuilder.createLinkedHashMapCacheBuilder().buildCache();
        Cache c2 = LinkedHashMapCacheBuilder.createLinkedHashMapCacheBuilder().buildCache();
        MultiLevelCache mc = (MultiLevelCache) MultiLevelCacheBuilder
                .createMultiLevelCacheBuilder()
                .addCache(c1, c2)
                .buildCache();
        mc.put("K", "V1");
        then:
        Objects.equals("V1", c1.get("K"))

        when:
        manager.getCacheManager().putCache("area", "cacheName", mc);
        manager.startSubscribe();
        Thread.sleep(50);
        CacheResult result = manager.publish(cm);
        then:
        result.isSuccess()
        waitUtil({ -> c1.get("K") == null })

        where:
        manager << [RedissonCacheBuilder.createBuilder()
                            .redissonClient(client)
                            .broadcastChannel("erpSyncDataTest")
                            .expireAfterWrite(10L, TimeUnit.MINUTES)
                            .keyPrefix("erpSyncDataTest:cache:")
                            .valueEncoder(Kryo5ValueEncoder.INSTANCE)
                            .valueDecoder(Kryo5ValueDecoder.INSTANCE)
                            .keyConvertor(FastjsonKeyConvertor.INSTANCE)
                            .createBroadcastManager(new SimpleCacheManager())]

    }


    public static void waitUtil(Supplier<Boolean> condition) {
        waitUtil(Boolean.TRUE, condition);
    }

    public static void waitUtil(Object expectValue, Supplier<? extends Object> actual) {
        waitUtil(expectValue, actual, 5000);
    }

    public static void waitUtil(Object expectValue, Supplier<? extends Object> actual, long timeoutMillis) {
        long start = System.nanoTime();
        long deadline = start + timeoutMillis * 1000 * 1000;
        Object obj = actual.get();
        log.info("expect",actual)
        if (Objects.equals(expectValue, obj)) {
            return;
        }
        int waitCount = 0;
        while (deadline - System.nanoTime() > 0) {
            try {
                Thread.sleep(5);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            waitCount++;
            obj = actual.get();
            if (Objects.equals(expectValue, obj)) {
                return;
            }
        }
        throw new AssertionFailedError("expect: " + expectValue +
                ", actual:" + obj + ", timeout=" + timeoutMillis + "ms, cost=" + (System.nanoTime() - start) / 1000 / 1000 + "ms, waitCount=" + waitCount);
    }

}
