package com.fxiaoke.open.erpsyncdata.dbproxy.aop

import com.fxiaoke.api.IdGenerator
import com.fxiaoke.open.erpsyncdata.common.util.BeanUtil2
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpSyncTimeEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.RelationErpShardEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.RelationManageGroupEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailSnapshotEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyData
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyDetailData
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*
import com.google.common.collect.Lists
import org.apache.commons.collections4.CollectionUtils
import org.springframework.aop.aspectj.annotation.AspectJProxyFactory
import spock.lang.Ignore
import spock.lang.Specification

import java.util.stream.Collectors
/**
 * <AUTHOR> 
 * @date 2024/2/22 10:36:31
 */
// TODO: 待修改
@Ignore
class MybatisReplaceEnterpriseAspectTest extends Specification {


    def managedTenantId = String.valueOf(1_0000_0000)
    def tenantId = managedTenantId + 1

    void setup() {
        initManageTenantIds()
    }
//
//    /**
//     * @see com.fxiaoke.open.erpsyncdata.dbproxy.aop.TemplateJetCacheInvalidateAspect
//     */
//    def "测试jetCache失效通知到下游"() {
//        when:
//        List<?> tenantIds = new ArrayList<>()
//        ErpObjectRelationshipDao dao = Mock(ErpObjectRelationshipDao) {
//            2 * invalidCacheErpObj(*_) >> {
//                tenantIds.add(it[0])
//            }
//        }
//        // 使用AspectJProxyFactory来创建被增强的代理对象
//        AspectJProxyFactory factory = new AspectJProxyFactory(dao);
//        factory.addAspect(cacheInvalidateAspect); // 添加切面到代理工厂中
//        ErpObjectRelationshipDao proxyService = factory.getProxy(); // 获取代理对象
//
//        proxyService.invalidCacheErpObj(tenantId, "testDc")
//
//        then:
//        tenantIds == [tenantId, managedTenantId]
//    }
//
//    /**
//     * @see com.fxiaoke.open.erpsyncdata.dbproxy.aop.AggDownstreamNotify2TemplateAspect
//     */
//    def "测试上游聚合下游消息"() {
//        when:
//        def millis = System.currentTimeMillis()
//        dataIntegrationNotificationDao.insert(managedTenantId, null, null, AlarmRuleType.GENERAL, "test", AlarmType.GET_BY_ID_API_BREAK, AlarmLevel.GENERAL, "testMsg", Lists.newArrayList(1000),null)
//
//        def id = upstreamAlertAggregationDao.getAndDeleteLastByTenantId(tenantId)
//
//        then:
//        if (id != null)
//            id.getUpdateTime() > millis
//    }
//
//    /**
//     * 下面的测试用例都是
//     * @see com.fxiaoke.open.erpsyncdata.dbproxy.aop.MybatisReplaceEnterpriseAspect
//     */
//    def "测试代管aop"() {
//        when:
//        def one = adminSyncPloyDetailDao.setTenantId("-10001").findOne(managedTenantId, "test", "test")
//        def one1 = adminSyncPloyDetailDao.findOne(tenantId, "test", "test")
//
//
//        then:
//        one.getTenantId() == managedTenantId
//        one.setTenantId(tenantId)
//        one == one1
//    }
//
//    def "测试嵌套TenantId"() {
//        when:
//        def id1 = adminSyncPloyDetailSnapshotDao.listByTenantId(tenantId).get(0)
//        def managed = adminSyncPloyDetailSnapshotDao.setTenantId("-10001").listByTenantId(managedTenantId).get(0)
//
//        then:
//        managed.getSourceTenantId() == managedTenantId
//        managed.getDestTenantId() == managedTenantId
//
//        managed.getSyncPloyDetailData().getTenantId() == managedTenantId
//        managed.setSourceTenantId(tenantId)
//        managed.setDestTenantId(tenantId)
//        managed.getSyncPloyDetailData().setTenantId(tenantId)
//        managed == id1
//    }
//
//    def "测试List"() {
//        when:
//        def status = adminSyncPloyDetailDao.setTenantId("-10001").listByStatus(Lists.newArrayList(managedTenantId), 2)
//        def status2 = adminSyncPloyDetailDao.listByStatus(Lists.newArrayList(managedTenantId, tenantId), 2)
//
//        def collect = status.stream().map({ it.getTenantId() }).distinct().collect(Collectors.toList())
//        def collect2 = status2.stream().map({ it.getTenantId() }).distinct().collect(Collectors.toList())
//
//        then:
////        入参只有下游,返回下游数据
//        collect == [managedTenantId]
////        有上下游,返回上下游
//        collect2 == [tenantId, managedTenantId]
////          理论上,下游数据量应该和上游数据量一样
//        status.size() * 2 == status2.size()
//    }
//
//    def "测试queryList"() {
//        when:
//        def entity = new ErpConnectInfoEntity()
//        entity.setTenantId(managedTenantId)
//        def list = erpConnectInfoDao.setTenantId("-10001").queryList(entity)
//        entity.setTenantId(tenantId)
//        def list2 = erpConnectInfoDao.queryList(entity)
//
//
//        // 应该只有testD的数据
//        def list3 = list2.stream().filter { it.getId() == "testD" }.collect(Collectors.toList())
//
//        def list1 = list.stream().map({ it.getTenantId() }).distinct().collect(Collectors.toList())
//        list.forEach { it.setTenantId(tenantId) }
//
//        then:
//        list1 == [managedTenantId]
//        list == list3
//    }
//
//    def "listSyncExtentByTenantId"() {
//        when:
//        def id = erpSyncTimeDao.setTenantId("-10001").listSyncExtentByTenantId(managedTenantId)
//        def get = id.get(0)
//
//        def id1 = get.getSnapshotId()
//
//        def id2 = adminSyncPloyDetailSnapshotDao.getById(tenantId, id1)
//
//        then:
//        id2 != null
//    }
//
//    def "listBySourceTenantTypeAndObjApiName"() {
//        when:
//        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setGlobalTenant(tenantId).listBySourceTenantTypeAndObjApiName("89333", TenantType.ERP, "erpSalesOrderObj_1hop5m1b8_1");
//
//        then:
//        CollectionUtils.isNotEmpty(syncPloyDetailEntities)
//    }
//
//    void initManageTenantIds() {
//        tenantConfigurationManager.updateConfig(managedTenantId, "0", "ALL", TenantConfigurationTypeEnum.MANAGED_ENTERPRISE.name(), "1")
//
//        initErpConnectInfo()
//
//        initRelationErpShard()
//
//        initSyncPloyDetail()
//
//        initSyncPloyDetailSnapshot()
//
//        initSyncTime()
//    }
//
//    private void initRelationErpShard() {
//        def id1 = relationManageGroupDao.queryAllByTemplateId(tenantId)
//        if (CollectionUtils.isEmpty(id1)) {
//            relationManageGroupDao.insert(new RelationManageGroupEntity(IdGenerator.get(), "testT", "testN", tenantId, "testD", System.currentTimeMillis(), System.currentTimeMillis()))
//        }
//        def id2 = relationManageGroupDao.queryAllByTemplateId(tenantId).get(0)
//
//        def id = relationErpShardDao.queryFirstNormalByDownstreamId(managedTenantId)
//        if (Objects.isNull(id) || id.getTemplateId() != tenantId) {
//            relationErpShardDao.insert(new RelationErpShardEntity(IdGenerator.get(), id2.getId(), managedTenantId, 1, System.currentTimeMillis(), System.currentTimeMillis()))
//        }
//    }
//
//    private void initSyncPloyDetailSnapshot() {
//        def id1 = adminSyncPloyDetailSnapshotDao.listByTenantId(tenantId)
//        if (id1 == null || id1.isEmpty()) {
//            def one1 = adminSyncPloyDetailDao.findOne(tenantId, "test", "test")
//            SyncPloyDetailData syncPloyDetailData = BeanUtil2.deepCopy(one1, SyncPloyDetailData.class);
//            def entity = new SyncPloyDetailSnapshotEntity()
//            entity.setId(IdGenerator.get())
//            entity.setSourceTenantId(tenantId)
//            entity.setDestTenantId(tenantId)
//            entity.setSourceObjectApiName(one1.getSourceObjectApiName())
//            entity.setDestObjectApiName(one1.getDestObjectApiName())
//            entity.setSyncPloyId(one1.getId())
//            entity.setSyncPloyDetailId(one1.getId())
//            entity.setSyncPloyDetailData(syncPloyDetailData)
//            entity.setSyncPloyData(new SyncPloyData());//默认，必填
//            entity.setStatus(1)
//            entity.setCreateTime(System.currentTimeMillis())
//            entity.setUpdateTime(System.currentTimeMillis())
//
//            adminSyncPloyDetailSnapshotDao.insert(entity)
//        }
//        adminSyncPloyDetailSnapshotDao.deleteByTenantId(managedTenantId)
//    }
//
//    private void initSyncPloyDetail() {
//        def one = adminSyncPloyDetailDao.findOne(tenantId, "test", "test")
//        if (one == null) {
//            def entity = new SyncPloyDetailEntity()
//            entity.setId(IdGenerator.get())
//            entity.setTenantId(tenantId)
//            entity.setIntegrationStreamName("testN")
//            entity.setSourceDataCenterId("testS")
//            entity.setDestDataCenterId("testD")
//            entity.setSourceObjectApiName("test")
//            entity.setDestObjectApiName("test")
//            entity.setStatus(2)
//            entity.setSourceTenantType(2)
//            entity.setDestTenantType(1)
//            entity.setSyncPloyId("")
//            entity.setCreateTime(System.currentTimeMillis())
//            entity.setUpdateTime(System.currentTimeMillis())
//            adminSyncPloyDetailDao.insert(entity)
//        }
//        adminSyncPloyDetailDao.deleteByTenantId(managedTenantId)
//    }
//
//    def void initErpConnectInfo() {
//        def id = erpConnectInfoDao.listByTenantId(tenantId)
//        if (id == null || id.isEmpty()) {
//            def entity = new ErpConnectInfoEntity()
//            entity.setId("testD")
//            entity.setTenantId(tenantId)
//            entity.setChannel(ErpChannelEnum.ERP_DB_PROXY)
//            entity.setDataCenterName("testN")
//            entity.setEnterpriseName("testE")
//            entity.setConnectParams("{\"baseUrl\":\"http://172.31.100.60/k3cloud/\",\"dbId\":\"5ec229fad54306\",\"dbName\":\"接口环境\",\"authType\":1,\"userName\":\"ces\",\"password\":\"sIR+13m2zFsaZPi4AF4LzA==\",\"pushDataApiNames\":[\"SAL_SC_CustMat\",\"ORG_Organizations\"],\"useFsHttpClient\":true,\"config\":{\"useAppToken\":false,\"enableDebug\":false,\"removeZeroWidthChar\":false},\"lang\":\"zh-CN\",\"lcid\":2052}")
//            entity.setNumber(123456)
//            entity.setCreateTime(System.currentTimeMillis())
//            entity.setUpdateTime(System.currentTimeMillis())
//            erpConnectInfoDao.insert(entity)
//
//            entity.setId(IdGenerator.get())
//            entity.setDataCenterName("testN2")
//            entity.setNumber(123457)
//            erpConnectInfoDao.insert(entity)
//        } else if (id.size() < 2) {
//            def entity = id.get(0)
//            entity.setId(IdGenerator.get())
//            entity.setDataCenterName("testN2")
//            entity.setNumber(entity.getNumber() + 1)
//            erpConnectInfoDao.insert(entity)
//        }
//        erpConnectInfoDao.deleteByTenantId(managedTenantId)
//    }
//
//    void initSyncTime() {
//        def name = erpSyncTimeDao.listByTenantIdAndObjectApiName(managedTenantId, "test")
//        if (CollectionUtils.isEmpty(name)) {
//            erpSyncTimeDao.insert(new ErpSyncTimeEntity(IdGenerator.get(), managedTenantId, "test", 1, 1710000000000, 1720000000000, 10, "{\"dayLimitType\":\"EVERY_DAY\",\"cronExpression\":\"4/6 * * * *\",\"minutes\":6,\"startDataTime\":\"00:00\",\"endDataTime\":\"23:59\"}", System.currentTimeMillis(), System.currentTimeMillis()))
//        }
//    }
}
