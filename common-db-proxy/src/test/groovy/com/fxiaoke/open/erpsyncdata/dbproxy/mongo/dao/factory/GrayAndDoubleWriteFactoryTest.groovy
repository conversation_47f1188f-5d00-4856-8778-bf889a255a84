package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.factory

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.DoubleWrite
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR> 
 * @date 2023/2/22 17:53:29
 */
class GrayAndDoubleWriteFactoryTest extends Specification {

    @Unroll
    def "#id"() {
        setup:
        String tenantId
        GrayAndDoubleWriteFactory.TenantGrayRule config = Mock() {
            isAllow(*_) >> { args ->
                tenantId = (String) args[0]
                return gray
            }
        }
        GrayAndDoubleWriteFactory.TenantGrayRule config2 = Mock() {
            isAllow(*_) >> true
        }
        def factory = new GrayAndDoubleWriteFactory<>(TestA.class)
        factory.setGrayRule(config)
        factory.setDoubleWriteNewRule(config2)
        factory.setDoubleWriteOldRule(config2)
        factory.setOldBean(new TestA())
        factory.setNewBean(new TestB())
        def testA = factory.getObject()

        when:
        Object[] a = args.toArray()
        def value
        for (final def m in TestA.class.getMethods()) {
            if (m.getName() == method) {
                value = m.invoke(testA, a)
                break
            }
        }

        then:
        value == result
        tenantId == t

        where:
//        id | 是否灰度 | 方法名 | 参数    || 查询灰度的企业ID | 结果
        id                | gray  | method  | args                  || t   | result
        "灰度,企业注解"   | true  | "test"  | ["1", "2"]            || "2" | "2"
        "无灰度,企业注解" | false | "test"  | ["1", "2"]            || "2" | "1"

        "灰度,无注解"     | true  | "test2" | ["1", "2"]            || "1" | "2"
        "无灰度,无注解"   | false | "test2" | ["1", "2"]            || "1" | "1"

        "灰度双写"        | true  | "test3" | ["1", new TestArg(1)] || "1" | new TestArg(4)
        "无灰度双写"      | false | "test3" | ["1", new TestArg(1)] || "1" | new TestArg(4)
    }

    @Unroll
    def "#id-测试TenantGrayRule"() {
        when:
        def grayRule = new GrayAndDoubleWriteFactory.TenantGrayRule(rule)
        def allow = grayRule.isAllow(tenantId)

        then:
        result == allow

        where:
        id        | rule                          | tenantId || result
        "空"      | ""                            | "123"     | false

        "无灰度"  | "white:"                      | "123"     | false
        "无灰度2" | "black:"                      | "123"     | true

        "灰度"    | "white:123"                   | "123"    || true
        "灰度2"   | "white:123,345,567,789,12334" | "789"    || true

        "全网"    | "white:*"                     | "123"    || true
        "全网2"   | "white:634,sdf,87,9761,*"     | "123"    || true
    }

    static class TestA {
        public String test(String a, @TenantID String b) {
            println "=======" + getClass().getSimpleName() + "=======" + a + "----" + b
            return a
        }

        public String test2(String a, String b) {
            println "=======" + getClass().getSimpleName() + "=======" + a + "----" + b
            return a
        }

        @DoubleWrite
        public TestArg test3(String a, TestArg b) {
            b.i++
            return b
        }
    }

    static class TestB extends TestA {
        public String test(String a, String b) {
            println "=======" + getClass().getSimpleName() + "=======" + a + "----" + b
            return b
        }

        public String test2(String a, String b) {
            println "=======" + getClass().getSimpleName() + "=======" + a + "----" + b
            return b
        }

        public TestArg test3(String a, TestArg b) {
            b.i += 2
            return b
        }
    }

    static class TestArg {
        Integer i

        TestArg(final Integer i) {
            this.i = i
        }

        boolean equals(final o) {
            if (this.is(o)) return true
            if (o == null || getClass() != o.class) return false

            TestArg testArg = (TestArg) o

            if (i != testArg.i) return false

            return true
        }

        int hashCode() {
            return (i != null ? i.hashCode() : 0)
        }
    }
}
