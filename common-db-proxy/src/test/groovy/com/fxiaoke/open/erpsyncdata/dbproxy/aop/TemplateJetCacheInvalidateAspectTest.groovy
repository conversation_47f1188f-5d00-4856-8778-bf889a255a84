package com.fxiaoke.open.erpsyncdata.dbproxy.aop


import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.RelationErpShardDao
import org.springframework.aop.aspectj.annotation.AspectJProxyFactory
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR> 
 * @date 2024/3/29 14:11:58
 */
class TemplateJetCacheInvalidateAspectTest extends Specification {

    @Unroll
    def "#id-测试jetCache失效通知到下游-CacheInvalidateContainer"() {
        when:
        List<?> tenantIds = new ArrayList<>()
        ErpObjectRelationshipDao dao = Mock(ErpObjectRelationshipDao) {
            invalidCacheErpObj(*_) >> {
                tenantIds.add(it[0])
            }
        }

        def proxyService = getAspect(dao, template, downstreamIds)

        proxyService.invalidCacheErpObj(tenantId, "testDc")

        then:
        tenantIds == result

        where:
        id       | tenantId | template | downstreamIds || result
        "非模版" | "123"    | "a"      | []            || ["123"]

        "模版"   | "123"    | tenantId | ["1", "2"]    || ["123", "1", "2"]
    }

    private <T> T getAspect(T o, String template, List<String> downstreamIds) {
        def aop = new TemplateJetCacheInvalidateAspect(
                relationErpShardDao: Mock(RelationErpShardDao) {
                    getAllDownstreamIdsByTemplateId(template) >> {
                        return downstreamIds
                    }
                }
        )

        // 使用AspectJProxyFactory来创建被增强的代理对象
        AspectJProxyFactory factory = new AspectJProxyFactory(o);
        factory.addAspect(aop); // 添加切面到代理工厂中
        return factory.getProxy(); // 获取代理对象
    }

}
