package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao

import cn.hutool.core.util.IdUtil
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.RecycleType
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.MongoTestUtil
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.RecycleBinData
import com.fxiaoke.open.erpsyncdata.dbproxy.util.WaitUtil
import com.github.mongo.support.DatastoreExt
import groovy.util.logging.Slf4j
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.Callable

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/5/26
 */
@Slf4j
@Unroll
class RecycleBinDaoTest extends Specification {
    @Shared
    private RecycleBinDao recycleBinDao
    @Shared
    private DatastoreExt store = MongoTestUtil.createStore()

    void setup() {
        if (recycleBinDao == null) {
            recycleBinDao = new RecycleBinDao(
                    store: store
            )
        }
    }

    def "测试MongoIndex"() {
        given:
        String tenantId = "83952"
        when:
        def coll = recycleBinDao.getOrCreateCollection(tenantId)
        coll.insertOne(RecycleBinData.builder().tenantId("83952").build())
        //等待执行，最多等几s
        def needIndexes = recycleBinDao.buildIndexes("83952")
        List indexes = WaitUtil.fetchResourceWithDelays(new Callable<List>() {
            @Override
            List call() throws Exception {
                def indexes = coll.listIndexes().toList()
                if (indexes.size() == needIndexes.size() + 1) {
                    return indexes
                } else {
                    return null
                }
            }
        }, [100L, 200L, 2000L, 2000L])
        println(indexes)
        then:
        //多一个id索引
        indexes.size() == needIndexes.size() + 1
    }

    def "insert"() {
        recycleBinDao.insertIgnore("1", RecycleType.SYNC_DATA_MAPPING, randomEntity())
        recycleBinDao.batchInsert("1", RecycleType.SYNC_DATA_MAPPING, [randomEntity()])
        recycleBinDao.batchInsertJson("1", RecycleType.SYNC_DATA_MAPPING, [randomEntity()], IdUtil.nanoId())
        def recent = recycleBinDao.listRecent("1", RecycleType.SYNC_DATA_MAPPING, null, 100)
        log.info("recent:${recent}")
        def recent2 = recycleBinDao.listRecent("1", RecycleType.SYNC_DATA_MAPPING, recent.get(0).getId().toHexString(), 100)
        log.info("recent2:${recent2}")
        expect:
        recent.size() == 3
        recent2.size() == 2
    }

    private SyncDataMappingsEntity randomEntity() {
        new SyncDataMappingsEntity(
                id: IdUtil.nanoId(),
                tenantId: "1"
        )
    }
}
