package com.fxiaoke.open.erpsyncdata.dbproxy.manager


import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity
import spock.lang.Specification


/**
 * <AUTHOR> @Date: 11:34 2023/6/9
 * @Desc:
 */
class TenantConfigurationManagerSpec extends Specification {

    TenantConfigurationManager mockTenantConfigurationManager;

    def "setup" () {
        mockTenantConfigurationManager = Spy(TenantConfigurationManager) {
            findOne(CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.NotChangeBigDecimalTenant.name()) >> {
                ErpTenantConfigurationEntity config = ErpTenantConfigurationEntity.builder()
                        .configuration("testei1;testei2")
                        .updateTime(123)
                        .build();
                return config;
            }
        };
    }


    def "isTenantNotChangeBigDecimal" (testcase, testei, expectResult) {
        setup:

        when:
        boolean result1 = mockTenantConfigurationManager.isTenantNotChangeBigDecimal(testei);

        then:
        System.out.println("testei:"+ testei+" result1:"+result1);
        result1 == expectResult;

        where :
        testcase | testei       | expectResult
        /**测试场景1: 设置过config的企业返回true **/
        1        | "testei1"    | true
        /**测试场景2: 设置过config的返回true **/
        2        | "testei2"    | true
        /**测试场景3: 没设置过config的企业返回false，**/
        3        | "testei5"    | false
    }
}
