package com.fxiaoke.open.erpsyncdata.common.config;

import com.github.microwww.redis.RedisServer;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.InetSocketAddress;

/**
 * mockRedisson
 *
 * <AUTHOR> (^_−)☆
 */
@Configuration
@Slf4j
public class MockRedissonConfig {

    @Bean
    public RedisServer mockRedisServer() {
        RedisServer server = new RedisServer();
        try {
            server.listener("127.0.0.1", 6379);
            InetSocketAddress address = (InetSocketAddress) server.getSockets().getServerSocket().getLocalSocketAddress();
            log.info("Mocker Redis start :: [{}:{}], set 'server.redis.host' to match it", address.getHostName(), address.getPort());
        } catch (Exception e) {
            log.warn("Mocker Redis not start in this process");
        }
        return server;
    }

    @Bean
    public RedissonClient mockRedisson(RedisServer redisServer) {
        Config config = new Config();
        config.useSingleServer()
                .setTimeout(1000000)
                .setAddress("redis://127.0.0.1:6379");
        RedissonClient redissonClient = Redisson.create(config);
        return redissonClient;
    }
}
