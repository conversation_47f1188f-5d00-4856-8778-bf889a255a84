package com.fxiaoke.open.erpsyncdata.dbproxy.util

import com.googlecode.aviator.AviatorEvaluator
import spock.lang.Specification

class AviatorUtilsTest extends Specification {
    def "执行表达式"() {
        given:
        String conditionExpress1 = conditionExpress
        def envMap = valueMap
        when:
        boolean compiledExp = AviatorEvaluator.execute(conditionExpress1, envMap, false);
        then:
        compiledExp == result
        where: "执行表达式"
        type       | valueMap                              | conditionExpress                                               | result
        "都不包含" | ["field_9m25X__c": ["bbb", "aaa"]]    | "include(field_9m25X__c,\"abc\")"                                  | false
        "其一包含" | ["field_9m25X__c": ["abc", "111"]]    | "include(field_9m25X__c,111)||include(field_9m25X__c,\"111\")" | true
        "其一包含" | ["field_9m25X__c": ["abc", "llllll"]] | "include(field_9m25X__c,\"abc\")"                                  | true
        "为空"     | ["field_9m25X__c": []]                | "include(field_9m25X__c,\"abc\")"                                  | false
        "为null"   | ["field_9m25X__c": null]              | "include(field_9m25X__c,\"abc\")"                                  | false
        "没有key"  | ["field_9m25X1111__c": "111"]         | "include(field_9m25X__c,\"111\")"                                  | false
        "数字"     | ["field_9m25X__c": [111]]             | "include(field_9m25X__c,\"111\")||include(field_9m25X__c,111)"     | true
    }
}
