package com.fxiaoke.open.erpsyncdata.dbproxy.Impl

import cn.hutool.core.date.DateTime
import com.facishare.converter.EIEAConverter
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NTempFileUpload
import com.facishare.fsi.proxy.service.NFileStorageService
import com.facishare.qixin.api.model.message.content.AdvanceText
import com.facishare.qixin.api.open.OpenMessageBatchService
import com.fxiaoke.api.MessageServiceV2
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.result.QueryBySearchTemplateResult
import com.fxiaoke.crmrestapi.service.ObjectDataService
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.MessageNotificationConfiguration
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DBFileManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DataIntegrationNotificationManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager
import com.fxiaoke.open.erpsyncdata.dbproxy.model.BuildExcelFileResult
import com.fxiaoke.open.erpsyncdata.dbproxy.remote.service.UserCenterService
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TextInfoUtil
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendAdminNoticeArg
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendSuperAdminNoticeArg
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendTextNoticeArg
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SyncStatusMessageArg
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType
import com.fxiaoke.open.erpsyncdata.preprocess.constant.NoticeType
import com.fxiaoke.open.erpsyncdata.preprocess.model.AlertArg
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService
import com.fxiaoke.paasauthrestapi.arg.RoleUserArg
import com.fxiaoke.paasauthrestapi.result.RoleUserResult
import com.fxiaoke.paasauthrestapi.service.PaasAuthService
import com.google.common.collect.Lists
import org.apache.commons.lang.RandomStringUtils
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
class NotificationServiceImplTest extends Specification {

    NotificationServiceImpl impl
    EIEAConverter eieaConverter
    I18NStringManager i18NStringManager = new I18NStringManager()
    String tenantId = "12345"
    boolean flag = true

    def setup() {
        eieaConverter = Mock(EIEAConverter) {
            enterpriseAccountToId(*_) >> 100
        }
        def service = Mock(OpenMessageBatchService) {
            sendMessageBatchAsyncV2(*_) >> true
        }
        def manager = Mock(DataIntegrationNotificationManager)
        def msgService = Mock(MessageServiceV2) {
            sendTextMessage(*_) >> null
        }
        def conf = Mock(ConfigCenterConfig) {
            getSendNotifyToOtherTenantConfig(*_) >> {
                def m = ["123":[111, 222], "789":[]]
                return m
            }
        }
        def t = Mock(TenantConfigurationManager) {
            findOne(*_) >> { args ->
                if (flag) {
                    def msgConf = new MessageNotificationConfiguration(status: 1, users: [000, 001, 002], fsAdmin: ["000": [000]])
                    def config = JacksonUtil.toJson(msgConf)
                    def entity = new ErpTenantConfigurationEntity(tenantId: args[0], configuration: config)
                    return entity
                }
                return null
            }
        }
        def erpConnectInfoManager = Mock(ErpConnectInfoManager) {
            listByTenantId(_) >> {
                def entity = new ErpConnectInfoEntity(id: "001", enterpriseName: "test", dataCenterName: "test001")
                return [entity]
            }
        }
        def nFileStorageService = Mock(NFileStorageService) {
            nTempFileUpload(*_) >> {
                return new NTempFileUpload.Result("temp")
            }
        }
        def nS = Mock(NotificationService) {
            sendErpSyncDataAppNotice(*_) >> Result.newSuccess()
            sendSuperAdminNotice(*_) >> Result.newSuccess()
        }
        def objectDataService = Mock(ObjectDataService) {
            queryBySearchTemplate(*_) >> {
                def obj1 = new ObjectData()
                obj1.setOwner(100)
                // relevant_team是一个Map的List
                Map m = ["teamMemberType": 0, "teamMemberEmployee": [000, 001]]
                obj1.put("relevant_team", [m])
                def data = [obj1]
                QueryBySearchTemplateResult.QueryResult q = new QueryBySearchTemplateResult.QueryResult(data: data, totalNumber: 2)
                QueryBySearchTemplateResult qRes = new QueryBySearchTemplateResult(queryResult: q)
                com.fxiaoke.crmrestapi.common.result.Result< QueryBySearchTemplateResult> res = new com.fxiaoke.crmrestapi.common.result.Result(data: qRes)
                return res
            }
        }
        def fM = Mock(DBFileManager) {
            buildExcelFileResult(*_) >> {
                Result<BuildExcelFileResult.Result> excelResult = Result.newSuccess(new BuildExcelFileResult.Result(tnFilePath: "test"))
            }
        }
        def pass = Mock(PaasAuthService) {
            roleUser(*_) >> { args ->
                def role = args[1] as RoleUserArg
                def code = role.roleCode
                if ("999".equals(code)) {
                    return new com.fxiaoke.paasauthrestapi.common.result.Result(errCode: -1)
                }
                com.fxiaoke.paasauthrestapi.common.result.Result<RoleUserResult> result = new com.fxiaoke.paasauthrestapi.common.result.Result(errCode: 0)
                result.setResult(new RoleUserResult(users: ["000", "001"]))
                return result
            }
        }
        impl = new NotificationServiceImpl(i18NStringManager: i18NStringManager, configCenterConfig: conf,
                openMessageBatchService: service, dataIntegrationNotificationManager: manager,
                eieaConverter: eieaConverter, messageServiceV2: msgService, tenantConfigurationManager: t,
                notificationService: nS, objectDataService: objectDataService, erpConnectInfoManager: erpConnectInfoManager,
                storageService: nFileStorageService, DBFileManager: fM, paasAuthService: pass,
                userCenterService: Mock(UserCenterService){
                    getPreviewFilePathFormat(*_) >> '%s'
                    getTnViewUrlFormat(*_) >> '%s'
                    getDownloadFilePath(*_) >> '/FSC/EM/File/DownloadByPath?Path=%s&name=%s'
                })
    }

    def "test uploadTnFile - #name"() {
        given:
        def nFileStorageService = Mock(NFileStorageService) {
            nTempFileUpload(*_) >> {
                if (success) {
                    return new NTempFileUpload.Result("temp")
                }
                return null
            }
        }
        NotificationServiceImpl s = new NotificationServiceImpl(storageService: nFileStorageService,
                                            eieaConverter: eieaConverter)
        when:
        Exception ex
        try {
            s.uploadTnFile("100", 256, "test".getBytes("UTF-8"))
        } catch(Exception e) {
            ex = e
        }
        then:
        if (success) {
            ex == null
        } else {
            ex != null && ex instanceof ErpSyncDataException
        }
        where:
        name    | success
        "成功"    | true
        "失败"    | false
    }

    def "test sendErpSyncDataAppNotice - #name"() {
        when:
        //发送企信消息
        SendTextNoticeArg sendTextNoticeArg = new SendTextNoticeArg();
        sendTextNoticeArg.setTenantId(tenantId);
        sendTextNoticeArg.setDataCenterId(dataCenterId);
        sendTextNoticeArg.setReceivers(Collections.singletonList(000));
        sendTextNoticeArg.setMsg("message");
        sendTextNoticeArg.setMsgTitle(msgTitle);
        def res = impl.sendErpSyncDataAppNotice(sendTextNoticeArg,
                AlarmRuleType.OTHER,
                AlarmRuleType.OTHER.getName(i18NStringManager, null, tenantId),
                AlarmType.GET_BY_ID_API_BREAK, // 为了触发sendToOtherTenant的逻辑
                AlarmLevel.GENERAL)
        then:
        noExceptionThrown()
        res.success
        where:
        name        | msgTitle  | dataCenterId
        "空消息标题"   | ""         | ""
        "消息标题非空"  | "Title"    | "001"
    }

    def "test sendErpSyncDataAppNotice - 长消息"() {
        when:
        //发送企信消息
        SendTextNoticeArg sendTextNoticeArg = new SendTextNoticeArg();
        sendTextNoticeArg.setTenantId(tenantId);
        sendTextNoticeArg.setDataCenterId(dataCenterId);
        sendTextNoticeArg.setReceivers(Collections.singletonList(000));
        sendTextNoticeArg.setMsg("We need a very long message to get length uo to 500. Here I write some useless words. \n " +
                "纷享销客是专业的移动CRM服务商，以\"连接型CRM\"为独特定位，以开放的企业级通讯为基础构架，以连接人，连接业务，连接客户为使命，" +
                "将CRM、PRM及SCRM融为一体，为企业提供内部销售管理，伙伴销售管理及终端客户管理一体化解决方案。" +
                "开放的通讯架构与交互的业务逻辑，帮助企业实现与外部伙伴、终端用户在业务与通讯上的互联互通，帮助企业构建完整的业务价值网络。\n" +
                "纷享销客隶属于北京易动纷享科技有限责任公司，创立于2011年12月。总部位于北京市海淀区中关村，" +
                "在北京、上海、广州、深圳、杭州、南京、武汉、成都、长沙、郑州、西安、济南等12个城市设立直营省分公司，在全国50余个城市建立营销服务中心。\n" +
                "截至2022年，员工总数1000余人，产品研发团队300余人，是一家具备完善的研发、实施交付能力的优质SaaS企业。" +
                "先后获得IDG资本、北极光创投、DCM、高瓴资本、中信产业基金、金蝶国际、鼎晖百孚和中软国际等优秀投资机构投资。 \n" +
                "纷享销客以连接型CRM为特色，连接业务，连接人，连接系统，实现以客户为中心，企业内部和上下游业务的高效协作。" +
                "纷享销客坚持行业化战略，为高科技、现代企业服务、快消、农牧、大制造等行业的大中型企业提供深度行业化的产品、方案和服务，助力企业通过营销、销售、服务全业务链一体化实现持续增长。");
        sendTextNoticeArg.setMsgTitle(msgTitle);
        def res = impl.sendErpSyncDataAppNotice(sendTextNoticeArg,
                AlarmRuleType.OTHER,
                AlarmRuleType.OTHER.getName(i18NStringManager, null, tenantId),
                AlarmType.GET_BY_ID_API_BREAK, // 为了触发sendToOtherTenant的逻辑
                AlarmLevel.GENERAL)
        then:
        noExceptionThrown()
        res.success
        where:
        name        | msgTitle  | dataCenterId
        "空消息标题"   | ""         | ""
    }

    def "test sendErpSyncDataAppMultiNotice"() {
        when:
        def res = impl.sendErpSyncDataAppMultiNotice([],
                tenantId,
                Lists.newArrayList(000),
                AlarmRuleType.OTHER,
                AlarmRuleType.OTHER.getName(i18NStringManager,null, tenantId),
                AlarmType.OTHER,
                AlarmLevel.GENERAL,null)
        then:
        noExceptionThrown()
        res.success
    }

    def "test sendTenantAdminNotice - #name"() {
        given:
        flag = hasSendTenantAdmin
        when:
        SendAdminNoticeArg arg = SendAdminNoticeArg.builder()
                .msg("测试发出消息")
                .msgTitle(i18NStringManager.getByEi(I18NStringEnum.s821,tenantId))
                .tenantId(tenantId)
                .dcId("001")
                .sendSuperAdminIfNoSendTenantAdmin(true)
                .needFillPreDbName(true)
                .alwaysSendSuperAdmin(true)
                .build();
        arg = arg.addTraceInfo();
        def res = impl.sendTenantAdminNotice(arg,
                AlarmRuleType.GENERAL,
                AlarmRuleType.GENERAL.getName(i18NStringManager,null,tenantId),
                AlarmType.GET_BY_ID_API_BREAK,
                AlarmLevel.IMPORTANT);
        then:
        res.success
        where:
        name            | hasSendTenantAdmin
        "有负责人"      | true
        "没有负责人"     | false
    }

    def "test sendNoticeByConfig"() {
        given:
        SendTextNoticeArg noticeArg = new SendTextNoticeArg();
        noticeArg.setMsg("test");
        String msgTitle = i18NStringManager.get(I18NStringEnum.s761, null, tenantId);
        noticeArg.setMsgTitle(msgTitle);
        noticeArg.setTenantId(tenantId);
        expect:
        impl.sendNoticeByConfig(noticeArg,
                [000, 001, 002],
                ["000", "999", null],
                AlarmRuleType.GENERAL,
                AlarmRuleType.GENERAL.getName(i18NStringManager,null,tenantId),
                AlarmType.INTEGRATION_STREAM_BREAK,
                AlarmLevel.URGENT).success
    }

    def "test sendSuperAdminNotice"() {
        given:
        SendAdminNoticeArg sendAdminNoticeArg = SendAdminNoticeArg.builder()
                .tenantId(tenantId)
                .msgTitle(i18NStringManager.getByEi(I18NStringEnum.s698, tenantId))
                .msg("test")
                .build().addTraceInfo()
        sendAdminNoticeArg.setNeedFillPreDbName(true)
        expect:
        impl.sendSuperAdminNotice(sendAdminNoticeArg).success
    }

    def "test sendCustomerRoomNotice"() {
        // 这个方法现在是空的，直接调了
        when:
        def res = impl.sendCustomerRoomNotice(null, null)
        then:
        SendSuperAdminNoticeArg arg = new SendSuperAdminNoticeArg()
        res.success
    }

    def "test sendSuperAdminNoticeToManageTool"() {
        // 因为调用了静态类，会因为缺失资源而报错
        given:
        def service = new NotificationServiceImpl(i18NStringManager: i18NStringManager)
        when:
        Result res = service.sendSuperAdminNoticeToManageTool(NoticeType.POLLING_ERP_DATA_DUPLICATE.arg()
                .setTenantId(tenantId)
                .setMsg("test message"))
        then:
        res.success
    }

    def "test sendFsUserIds"() {
        expect:
        impl.sendFsUserIds("81243","628312575457230848","集成平台数据同步存在错误数据，请及时修复错误数据\n" +
                "企业：(81243)zsl测试企业022\n" + "同步对象：源对象ApiName(SalesOrderObj)->目标对象ApiName(SAL_SaleOrder.BillHead)\n" +
                "同步方向：CRM->ERP\n" + "主对象同步失败数量：89\n" + "\n" + "企业：(81243)zsl测试企业022\n" +
                "同步对象：源对象ApiName(SAL_SaleOrder.BillHead)->目标对象ApiName(SalesOrderObj)\n" + "同步方向：ERP->CRM\n" +
                "主对象同步失败数量：136\n" + "\n" + "企业：(81243)zsl测试企业022\n" +
                "同步对象：源对象ApiName(BD_MATERIALUNITCONVERT.BillHead)->目标对象ApiName(MultiUnitRelatedObj)\n" + "同步方向：ERP->CRM\n" +
                "主对象同步失败数量：163\n" + "\n" + "企业：(81243)zsl测试企业022\n" +
                "同步对象：源对象ApiName(AccountObj)->目标对象ApiName(BD_Customer.BillHead)\n" + "同步方向：CRM->ERP\n" + "主对象同步失败数量：50\n" +
                "企业：(81243)zsl测试企业022\n" + "同步对象：源对象ApiName(BD_MATERIAL.BillHead)->目标对象ApiName(ProductObj)\n" +
                "同步方向：ERP->CRM\n" + "主对象同步失败数量：204\n" + "\n" + "企业：(81243)zsl测试企业022\n" +
                "同步对象：源对象ApiName(BD_STOCK.BillHead)->目标对象ApiName(WarehouseObj)\n" + "同步方向：ERP->CRM\n" + "主对象同步失败数量：21",
                AlarmRuleType.OTHER,
                AlarmRuleType.OTHER.getName(i18NStringManager,null,"81243"),
                AlarmType.OTHER,
                AlarmLevel.GENERAL).success
    }

    def "test sendAlertMsg"() {
        given:
        AlertArg arg = AlertArg.normalApp()
        AdvanceText advanceText = TextInfoUtil.lines2Text(
                i18NStringManager.getByEi(I18NStringEnum.s852,tenantId),
                i18NStringManager.getByEi(I18NStringEnum.s853,tenantId) + DateTime.now(),
                i18NStringManager.getByEi(I18NStringEnum.s854,tenantId) + "test",
                i18NStringManager.getByEi(I18NStringEnum.s855,tenantId) + "test000",
                i18NStringManager.getByEi(I18NStringEnum.s856,tenantId) + "testResultTag",
                i18NStringManager.getByEi(I18NStringEnum.s857,tenantId) + "testErrorMessage"
        )
        arg.setTenantId(tenantId)
            .setToUserList([000, 001])
            .setAdvanceText(advanceText)
            .setSendPrimaryApp(true)
        expect:
        impl.sendAlertMsg(arg,
                AlarmRuleType.OTHER,
                AlarmRuleType.OTHER.getName(i18NStringManager,null,tenantId),
                AlarmType.OTHER,
                AlarmLevel.GENERAL).success
    }

    def "test generateDataSyncResult - #name"() {
        given:
        SyncStatusMessageArg arg = new SyncStatusMessageArg(tenantId: tenantId)
        arg.setStartTime(System.currentTimeMillis())
        arg.setEndTime(System.currentTimeMillis() + (1000 * 60 * 60 * 24))
        SyncStatusMessageArg.SyncDataStatusMessage
        List<SyncStatusMessageArg.SyncDataStatusMessage> errList = getList(3, 3)
        arg.setErrorList(errList)
        arg.setSuccessList(getList(sLen, sLen))
        arg.setConnInfoName("Test Connector")
        arg.setSyncDirection("test direction")

        expect:
        impl.generateDataSyncResult(arg).success
        where:
        name         | sLen
        "过长"        | 190
        "正常"        | 3
    }

    private List<SyncStatusMessageArg.SyncDataStatusMessage> getList(int len, int num) {
        def res = []
        for (int i = 0; i < num; i++) {
            res.add(new SyncStatusMessageArg.SyncDataStatusMessage(dataName: "test"+ RandomStringUtils.randomAlphanumeric(len)))
        }
        return res
    }

    def "test init"() {
        // 因为缺少资源，会报错
        expect:
        try {
            impl.init()
        } catch (Exception e) {
            println(e)
        }
//        then:
//        thrown(FRestClientConfigException)
//        noExceptionThrown()
    }

}
