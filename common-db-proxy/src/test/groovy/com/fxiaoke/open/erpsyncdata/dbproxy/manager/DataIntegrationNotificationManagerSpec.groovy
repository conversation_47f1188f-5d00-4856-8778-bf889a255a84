package com.fxiaoke.open.erpsyncdata.dbproxy.manager

import com.facishare.organization.api.model.employee.EmployeeDto
import com.facishare.organization.api.model.employee.result.BatchGetEmployeeDtoResult
import com.facishare.organization.api.service.EmployeeProviderService
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.model.DataCenterModel
import com.fxiaoke.open.erpsyncdata.dbproxy.model.DataIntegrationNotificationModel
import com.fxiaoke.open.erpsyncdata.dbproxy.model.PloyDetailModel
import com.fxiaoke.open.erpsyncdata.dbproxy.model.UserModel
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.DataIntegrationNotificationDao
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataIntegrationNotificationEntity
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*
import org.bson.types.ObjectId
import spock.lang.Specification

/**
 * <AUTHOR> 
 * @date 2024/10/22 16:36:21
 */
class DataIntegrationNotificationManagerSpec extends Specification {
    DataIntegrationNotificationDao dataIntegrationNotificationDao = Mock()
    AdminSyncPloyDetailDao adminSyncPloyDetailDao = Mock()
    ErpConnectInfoManager erpConnectInfoManager = Mock()
    EmployeeProviderService employeeProviderService = Mock()
    ErpConnectInfoDao erpConnectInfoDao = Mock()
    TenantConfigurationManager tenantConfigurationManager = Mock()
    
    DataIntegrationNotificationManager dataIntegrationNotificationManager = new DataIntegrationNotificationManager(
            dataIntegrationNotificationDao: dataIntegrationNotificationDao,
            adminSyncPloyDetailDao: adminSyncPloyDetailDao,
            erpConnectInfoManager: erpConnectInfoManager,
            employeeProviderService: employeeProviderService,
            erpConnectInfoDao: erpConnectInfoDao,
            tenantConfigurationManager: tenantConfigurationManager
    )

    def setup() {
        erpConnectInfoDao.setTenantId(*_) >> erpConnectInfoDao
        adminSyncPloyDetailDao.setTenantId(*_) >> adminSyncPloyDetailDao
    }

    def "test get Data List By Page"() {
        def time = new GregorianCalendar(2024, Calendar.OCTOBER, 22, 16, 36).getTime()
        def id = new ObjectId(time, 0, (short) 0, 0)

        def entity = new DataIntegrationNotificationEntity(id, "88521", "dataCenterId", ["ployDetailIdList"], NotificationType.ALERT, AlarmRuleType.GENERAL, "alarmRuleName", AlarmType.POLLING_ERP_API_EXCEPTION, AlarmLevel.URGENT, 1l, "msg", [1000], [NotifyType.SMS.name()],true, "traceId", time, time)
        def entry = new DataIntegrationNotificationModel.Entry()

        given:
        dataIntegrationNotificationDao.getDataListByPage(*_) >> new DataIntegrationNotificationModel(0l, [entity], [entry])

        adminSyncPloyDetailDao.listByIds(*_) >> [new SyncPloyDetailEntity(id: 'ployDetailId', integrationStreamName: 'ployDetailName')]
        erpConnectInfoManager.getByIdAndTenantId(*_) >> new ErpConnectInfoEntity("id", "88521", ErpChannelEnum.ERP_K3CLOUD, "dataCenterName", "enterpriseName", "connectParams", 1l, 1l, 0, 0)
        employeeProviderService.batchGetEmployeeDto(*_) >> new BatchGetEmployeeDtoResult([new EmployeeDto(employeeId: 123, name: "name")])

        when:
        DataIntegrationNotificationModel result = dataIntegrationNotificationManager.getDataListByPage("88521", "dataCenterId", ["ployDetailIdList"], 0, NotificationType.ALERT, AlarmType.POLLING_ERP_API_EXCEPTION, AlarmLevel.URGENT, time, time, 0, 0)

        then:
        result == new DataIntegrationNotificationModel(0l, null, [new DataIntegrationNotificationModel.Entry(id: id.toString(), tenantId: '88521', dataCenterId: 'dataCenterId', dataCenterName: 'dataCenterName', ployDetailList: [new PloyDetailModel(ployDetailId: 'ployDetailId', ployDetailName: 'ployDetailName')], notificationType: NotificationType.ALERT, alarmRuleType: AlarmRuleType.GENERAL, alarmType: AlarmType.POLLING_ERP_API_EXCEPTION, alarmLevel: AlarmLevel.URGENT, time: 1l, msg: 'msg', userList: [new UserModel(userId: 123, userName: 'name')], notifyType: [NotifyType.SMS.name()],createTime: time, updateTime: time)])
    }

    def "test get Ploy Detail List In Progress"() {
        def time = new GregorianCalendar(2024, Calendar.OCTOBER, 22, 16, 36).getTime()

        def entity = new DataIntegrationNotificationEntity(new ObjectId(time, 0, (short) 0, 0), "88521", "dataCenterId", ["ployDetailIdList"], NotificationType.ALERT, AlarmRuleType.GENERAL, "alarmRuleName", AlarmType.POLLING_ERP_API_EXCEPTION, AlarmLevel.URGENT, 1l, "msg", [0], [NotifyType.SMS.name()], true, "traceId", time, time)
        given:
        dataIntegrationNotificationDao.distinct(*_) >> [entity]
        adminSyncPloyDetailDao.listByIds(*_) >> [new SyncPloyDetailEntity(id: 'ployDetailId', integrationStreamName: 'ployDetailName')]


        when:
        List<PloyDetailModel> result = dataIntegrationNotificationManager.getPloyDetailListInProgress("88521", "dataCenterId", ["ployDetailIdList"], AlarmType.POLLING_ERP_API_EXCEPTION)

        then:
        result == [new PloyDetailModel("ployDetailId", "ployDetailName")]
    }

    def "test get Dc List"() {
        given:
        dataIntegrationNotificationDao.getDataListByDcId(*_) >> ["getDataListByDcIdResponse"]

        erpConnectInfoDao.listByIds(*_) >> [new ErpConnectInfoEntity("id", "88521", ErpChannelEnum.ERP_K3CLOUD, "dataCenterName", "enterpriseName", "connectParams", 1l, 1l, 0, 0)]


        when:
        List<DataCenterModel> result = dataIntegrationNotificationManager.getDcList("88521")

        then:
        result == [new DataCenterModel("id", "dataCenterName")]
    }

    def "test clear Data"() {
        given:
        dataIntegrationNotificationDao.deleteMany(*_) >> 0l

        when:
        long result = dataIntegrationNotificationManager.clearData()

        then:
        result == 0l
    }

    def "test delete By Ploy Detail Id"() {
        given:
        dataIntegrationNotificationDao.deleteByPloyDetailId(*_) >> 0l

        when:
        long result = dataIntegrationNotificationManager.deleteByPloyDetailId("88521", "ployDetailId")

        then:
        result == 0l
    }

    def "test update Alarm Status"() {
        given:
        dataIntegrationNotificationDao.updateAlarmStatus(*_) >> 0l

        when:
        long result = dataIntegrationNotificationManager.updateAlarmStatus("88521", "ployDetailId", AlarmType.POLLING_ERP_API_EXCEPTION, true)

        then:
        result == 0l
    }

    def "test has Data"() {
        given:
        tenantConfigurationManager.findGlobal(*_) >> new ErpTenantConfigurationEntity("id", "88521", "dataCenterId", "channel", "type", "configuration", 1l, 1l)

        when:
        boolean result = dataIntegrationNotificationManager.hasData("88521", 0)

        then:
        !result
    }
}
