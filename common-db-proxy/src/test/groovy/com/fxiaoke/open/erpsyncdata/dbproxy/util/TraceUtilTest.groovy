package com.fxiaoke.open.erpsyncdata.dbproxy.util

import com.facishare.converter.EIEAConverter
import com.github.trace.TraceContext
import spock.lang.Specification

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/5/10
 */
class TraceUtilTest extends Specification {

    void setup() {
        def eIEAConverter = Mock(EIEAConverter)
        eIEAConverter.enterpriseIdToAccount(_) >> { args ->
            def ea = args[0] + "_ea"
            return ea
        }
        new TraceUtil().setFuncEiTEa(eIEAConverter)
        TraceContext.remove();
    }
}
