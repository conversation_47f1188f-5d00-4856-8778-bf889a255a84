package com.fxiaoke.open.erpsyncdata.dbproxy.aop

import com.alibaba.fastjson.JSON
import com.fxiaoke.api.IdGenerator
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpTenantConfigurationDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisCacheManager
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil
import com.google.common.base.Splitter
import com.google.common.collect.Lists
import org.apache.commons.collections4.CollectionUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationContext
import org.springframework.test.context.ContextConfiguration
import spock.lang.Ignore
import spock.lang.Specification

/**
 * <AUTHOR> 
 * @date 2024/3/1 10:45:42
 */
// TODO: Ignore Spring
@Ignore
@ContextConfiguration(["classpath*:spring-test.xml"])
class TenantConfigReplaceEnterpriseAspectTest extends Specification {

    static {
        System.setProperty("process.profile", "fstest");
    }

    @Autowired
    private ErpTenantConfigurationDao erpTenantConfigurationDao;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager
    @Autowired
    private ConfigCenterConfig config

    @Autowired
    private SyncPloyManager syncPloyManager
    @Autowired
    private RedisCacheManager redisCacheManager;

    @Autowired
    private ApplicationContext applicationContext


    String downstreamId = String.valueOf(1_0000_0000)
    String templateId = downstreamId + 1

    void setup() {
        initManageTenantIds()
    }

    def "测试企业配置"() {
        when:
        def day = config.getSyncLogExpireDay(downstreamId)
        def day2 = config.getSyncLogExpireDay(templateId)

        then:
        day == day2
    }

    def "测试全局List配置"() {
        expect:
        tenantConfigurationManager.inWhiteList(downstreamId, TenantConfigurationTypeEnum.SERIALIZE_NULL_TENANTS)
        tenantConfigurationManager.isPassDataSource(downstreamId, "testA", 1, "test1")
    }

    def "测试集成流熔断"() {
        when:
        def field = SyncPloyManager.class.getDeclaredField("adminSyncPloyDetailDao")
        field.setAccessible(true)
        field.set(syncPloyManager, Mock(AdminSyncPloyDetailDao) {
            0 * setTenantId(*_)
        })

        def ployDetailId = "testP"
        String breakKey = String.format(DownstreamPloyDetailCircuitBreakerAspect.downstreamPloyDetailCircuitBreakerFormat, downstreamId, ployDetailId);
        redisCacheManager.delCache(breakKey, "123")

        def id = syncPloyManager.disablePloyDetailByStreamId(downstreamId, "testD", ployDetailId, "testO", "123", true)
        def id2 = syncPloyManager.disablePloyDetailByStreamId(downstreamId, "testD", ployDetailId, "testO", "123", true)

        then:
        id
        !id2
    }

    void initManageTenantIds() {
        initTenantConfig()

        initWhiteList()

        initPaasData()

        tenantConfigurationManager.updateConfig(downstreamId, "0", "ALL", TenantConfigurationTypeEnum.MANAGED_ENTERPRISE.name(), "1")
    }

    private void initWhiteList() {
        def key = "SERIALIZE_NULL_TENANTS"
        def one = tenantConfigurationManager.findOne("0", "0", "ALL", key)
        if (Objects.isNull(one)) {
            def entity = new ErpTenantConfigurationEntity()
            entity.setId(IdGenerator.get())
            entity.setTenantId("0")
            entity.setDataCenterId("0")
            entity.setChannel("ALL")
            entity.setType(key)
            entity.setConfiguration(templateId)
            entity.setCreateTime(System.currentTimeMillis())
            entity.setUpdateTime(System.currentTimeMillis())
            erpTenantConfigurationDao.insert(entity)
            return;
        }

        def list = Lists.newArrayList(Splitter.on(";").splitToList(one.getConfiguration()))
        if (!list.contains(downstreamId) && list.contains(templateId)) {
            return
        }

        if (list.contains(downstreamId)) {
            list.remove(downstreamId)
        }
        if (!list.contains(templateId)) {
            list.add(templateId)
        }
        tenantConfigurationManager.updateGlobalConfig(key, list.join(";"))
    }

    private void initTenantConfig() {
        for (final def type in ConfigCenter.replaceDownstreamConfigTypeSet) {
            def entity = new ErpTenantConfigurationEntity()
            entity.setTenantId(templateId)
            entity.setType(type)
            def list = erpTenantConfigurationDao.queryList(entity)
            if (CollectionUtils.isEmpty(list)) {
                entity.setId(IdGenerator.get())
                entity.setConfiguration("1")
                entity.setDataCenterId("testD")
                entity.setChannel("testC")
                entity.setCreateTime(System.currentTimeMillis())
                entity.setUpdateTime(System.currentTimeMillis())
                erpTenantConfigurationDao.insert(entity)
            }
        }
        erpTenantConfigurationDao.deleteByTenantId(downstreamId)
    }

    void initPaasData() {
        def key = TenantConfigurationTypeEnum.TENANT_NEED_PASS_DATASOURCE.name()
        ErpTenantConfigurationEntity config = tenantConfigurationManager.findOne(CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier, key);
        if (Objects.isNull(config)) {
            def entity = new ErpTenantConfigurationEntity()
            entity.setId(IdGenerator.get())
            entity.setTenantId(CommonConstant.configUniformIdentifier)
            entity.setDataCenterId(CommonConstant.configUniformIdentifier)
            entity.setChannel(CommonConstant.configUniformIdentifier)
            entity.setType(key)
            entity.setConfiguration('{"' + templateId + '":["test1","test2"]}')
            entity.setCreateTime(System.currentTimeMillis())
            entity.setUpdateTime(System.currentTimeMillis())
            erpTenantConfigurationDao.insert(entity)
        }

        def configuration = config.getConfiguration()
        Map<String, List<String>> json = JacksonUtil.fromJson(configuration, Map.class);
        if (json.containsKey(templateId) && !json.containsKey(downstreamId)) {
            return
        }

        if (!json.containsKey(templateId)) {
            json.put(templateId, ["test1", "test2"])
        }
        if (json.containsKey(downstreamId)) {
            json.remove(downstreamId)
        }

        def entity = new ErpTenantConfigurationEntity()
        entity.setId(config.getId())
        entity.setTenantId("0")
        entity.setType(key)
        entity.setConfiguration(JSON.toJSONString(json))
        tenantConfigurationManager.updateById(CommonConstant.configUniformIdentifier, entity)
    }
}
