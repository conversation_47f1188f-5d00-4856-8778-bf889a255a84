package com.fxiaoke.open.erpsyncdata.dbproxy.aop

import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncObjectAndTenantMappingData
import io.vavr.collection.Stream
import org.springframework.aop.aspectj.annotation.AspectJProxyFactory
import spock.lang.Specification
import spock.lang.Unroll

import java.util.stream.Collectors

/**
 * <AUTHOR> 
 * @date 2025/1/6 10:29:37
 */
class SplitArgReduceResultAspectTest extends Specification {
    SyncDataMappingsDao dao
    SplitArgReduceResultAspect aop
    def mock = Mock(SyncDataMappingsDao)

    void setup() {
        aop = new SplitArgReduceResultAspect()
        dao = getAspect(mock, aop)
    }

    @Unroll
    def "返回list-#id"() {
        when:
        mock.listByMasterDataId(*_) >> {
            return Stream.range(0, splitSize).map { new SyncDataMappingsEntity() }.collect(Collectors.toList())
        }
        def collect = Stream.range(0, num).map({ new SyncObjectAndTenantMappingData() }).collect(Collectors.toList())
        def data = dao.listByMasterDataId("", collect, "")

        then:
        data.size() == result

        where:
        id          | num  | splitSize || result
        "拆分2次"   | 200  | 90        || 180
        "拆分2次-2" | 101  | 90        || 180

        "不拆分"    | 80   | 90        || 90

        "拆分10次"  | 1000 | 90        || 900
    }

    @Unroll
    def "返回number-#id"() {
        when:
        mock.countByObjectApiNamesLimit1000(*_) >> splitSize
        def collect = Stream.range(0, num).map({ new SyncObjectAndTenantMappingData() }).collect(Collectors.toList())
        def data = dao.countByObjectApiNamesLimit1000("", collect, 0, "", "", "", "", "", 0, 0)

        then:
        data == result

        where:
        id          | num    | splitSize || result
        "拆分2次"   | 200    | 90        || 180
        "拆分2次-2" | 101    | 90        || 180

        "不拆分"    | 80     | 90        || 90

        "拆分10次"  | 1000   | 90        || 900

        "限制上限"  | 100000 | 90        || 1000
    }

    private <T> T getAspect(T o, Object aop) {
        // 使用AspectJProxyFactory来创建被增强的代理对象
        AspectJProxyFactory factory = new AspectJProxyFactory(o);

        factory.setProxyTargetClass(true) // 使用CGLib代理
        factory.addAspect(aop); // 添加切面到代理工厂中
        return factory.getProxy(); // 获取代理对象
    }
}
