package com.fxiaoke.open.erpsyncdata.dbproxy.util

import cn.hutool.core.thread.ThreadUtil
import cn.hutool.core.util.IdUtil
import cn.hutool.core.util.RandomUtil
import com.fxiaoke.common.StopWatch
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent
import com.github.trace.TraceContext
import groovy.util.logging.Slf4j
import spock.lang.Specification
import spock.lang.Unroll

import java.lang.ref.Reference
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicLong
import java.util.function.BiConsumer

/**
 *
 * <AUTHOR> (^_−)☆
 */
@Slf4j
class LocalDispatcherUtilTest extends Specification {
    class TestCtxData {
        SyncDataContextEvent main;
        List<SyncDataContextEvent> details;

        int count() {
            return details.size() + 1
        }
    }

    def "testLocal"() {
        given:
        List<String> locales = []
        def util = new LocalDispatcherUtil<String>({ k, vs ->
            locales.add(TraceContext.get().getLocale())
            log.info("consumer ${vs}")
        })
        util.setBatchProcessTimeLimitInSecond(1)
        when:
        TraceContext.get().setLocale("en")
        TraceContext.get().setTraceId("E-E.666.666")
        util.produceDataCount1("key","1")
        util.produceDataCount1("key","3")
        //等待执行
        ThreadUtil.safeSleep(2000)
        then:
        locales[0] == "en"
    }

    @Unroll
    def "批量数据测试-#threadNum线程-#batchNum条/批"(int threadNum, int batchNum) {
        setup:
        //消费的数据全部存储
        AtomicLong consumeCounter = new AtomicLong(0)
        AtomicLong produceCounter = new AtomicLong(0)
        def util = new LocalDispatcherUtil<TestCtxData>({ k, vs ->
            def dataList = vs as List<TestCtxData>
            def first = (TestCtxData) dataList.get(0)
            def count = dataList.sum({ it.count() }) as Long
            consumeCounter.addAndGet(count)
            log.info("consumer $count,firstId:${first.main.getDataId()}")
        })
        util.setQueueCapacity(10)
        util.setBatchProcessTimeLimitInSecond(2)
        when:
        StopWatch sw = StopWatch.createStarted("multiTest", "put")
        //模拟100个线程并发写入100条数据
        ThreadUtil.concurrencyTest(threadNum, {
            for (i in 0..<batchNum) {
                def ctx = new TestCtxData()
                ctx.setMain(new SyncDataContextEvent(dataId: IdUtil.nanoId()))
                ctx.setDetails(
                        //随机的明细数量
                        (1..RandomUtil.randomInt(10)).collect {
                            new SyncDataContextEvent(dataId: IdUtil.nanoId())
                        })
                def count = ctx.count()
                produceCounter.addAndGet(count)
                util.produceData("key2", ctx, count)
            }
        })
        sw.stopLastAndStart("wait")
        println("sw:${sw}，count:${consumeCounter.get()}")
        //等待延迟消费的数据，如果超时认为出现死锁
        WaitUtil.fetchResourceWithDelays({ consumeCounter.get() == produceCounter.get() ? 1 : null }, [1000L, 3000L, 10000L])
        sw.stop()
        //还需要验证等待时间的，，，先只观察下吧
        println("sw:${sw}，count:${consumeCounter.get()}")
        then:
        consumeCounter.get() > threadNum * batchNum
        consumeCounter.get() == produceCounter.get()

        where:
        threadNum | batchNum
        1         | 10
        10        | 10
        100       | 10
        8         | 23
        89        | 103
        //增加一个大量数据测试, 跑单测
        //sw:StopWatch 'multiTest': running time = 28457 ms; [put] 27443 ms = 96%; [wait] 1013 ms = 4%，count:11400656
//        200      | 9999
    }

    @Unroll
    def "单条数据测试-#threadNum线程-#batchNum条/批"(int threadNum, int batchNum) {
        setup:
        //消费的数据全部存储
        Set<String> consumeDataId = new HashSet<>()
        AtomicLong consumeCounter = new AtomicLong(0)
        def util = new LocalDispatcherUtil<SyncDataContextEvent>({ k, vs ->
            def dataList = vs as List
            consumeCounter.addAndGet(dataList.size())
            log.info("consumer ${dataList.size()},firstId:${((SyncDataContextEvent) dataList.get(0)).getDataId()}")
            for (final SyncDataContextEvent v in vs) {
                consumeDataId.add(v.getDataId())
            }
        })
        util.setQueueCapacity(10)
        util.setBatchProcessTimeLimitInSecond(2)
        when:
        StopWatch sw = StopWatch.createStarted("singleTest", "put")
        //模拟100个线程并发写入100条数据
        ThreadUtil.concurrencyTest(threadNum, {
            for (i in 0..<batchNum) {
                def ctx = new SyncDataContextEvent()
                ctx.setDataId(IdUtil.nanoId())
                util.produceDataCount1("key", ctx)
            }
        })
        sw.stopLastAndStart("wait")
        println("sw:${sw}，count:${consumeCounter.get()}")
        //等待延迟消费的数据，如果超时认为出现死锁
        WaitUtil.fetchResourceWithDelays({ consumeCounter.get() == threadNum * batchNum ? 1 : null }, [1000L, 3000L, 10000L])
        sw.stop()
        //还需要验证等待时间的，，，先只观察下吧
        println("sw:${sw}，count:${consumeCounter.get()}")
        then:
        consumeCounter.get() == threadNum * batchNum

        where:
        threadNum | batchNum
        1         | 10
        10        | 10
        100       | 10
        8         | 23
        89        | 103
    }


    def "test generated by gpt-4o"() {
        given:
        int everyTheadTestDataNum = 2000
        AtomicInteger countProduceDataNum = new AtomicInteger(0)
        AtomicInteger countConsumeDataNum = new AtomicInteger(0)
        AtomicInteger countCallBatchFuncNum = new AtomicInteger(0)
        BiConsumer<String, List<String>> biConsumer = { k, v ->
            countConsumeDataNum.addAndGet(v.size())
            countCallBatchFuncNum.incrementAndGet()
            return true
        }
        LocalDispatcherUtil<String> dispatcher = new LocalDispatcherUtil<String>(biConsumer)

        int keyNum = 30
        int testProduceThreadNum = 1000

        ExecutorService processThreadPool = Executors.newFixedThreadPool(testProduceThreadNum)

        when:
        for (int i = 0; i < testProduceThreadNum; i++) {
            processThreadPool.submit({
                try {
                    int j = 0
                    while (j < everyTheadTestDataNum) {
                        j++
                        dispatcher.produceDataBatch("key${new Random().nextInt() % keyNum}", ["${new Random().nextInt()}"])
                        if (0 == (j % 100)) {
                            try {
                                Thread.sleep(5)
                            } catch (Exception ignored) {
                            }
                        }
                    }
                    countProduceDataNum.addAndGet(j)
                } catch (Exception e) {
                    log.error("get exception, ", e)
                }
            })
        }

        processThreadPool.awaitTermination(20, TimeUnit.SECONDS)

        then:
        println "countProduceDataNum: ${countProduceDataNum}, countConsumeDataNum: ${countConsumeDataNum}, call batch func num: ${countCallBatchFuncNum}"
        processThreadPool.shutdown()

        expect:
        countProduceDataNum.get() == countConsumeDataNum.get()
    }

}
