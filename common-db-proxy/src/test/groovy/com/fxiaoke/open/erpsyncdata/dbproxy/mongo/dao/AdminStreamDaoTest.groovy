package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao

import com.fxiaoke.open.erpsyncdata.dbproxy.model.AdminStreamQuery
import com.fxiaoke.open.erpsyncdata.dbproxy.model.StreamSimpleInfo
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.MongoTestUtil
import spock.lang.Specification

class AdminStreamDaoTest extends Specification {
    AdminStreamDao dao

    void setup() {
        dao = new AdminStreamDao(
                store: MongoTestUtil.createStore()
        )
    }
    def "test getAllStreamIds"() {
        given:
        dao.batchUpsert([
                new StreamSimpleInfo(streamId: "001", status: 1),
                new StreamSimpleInfo(streamId: "002", status: 0),
                new StreamSimpleInfo(streamId: "003", status: 1),
                new StreamSimpleInfo(streamId: "004", status: 1),
                new StreamSimpleInfo(streamId: "005", status: 0)
        ])
        def f = new AdminStreamQuery(status: "1")
        when:
        int count = dao.filteredCount(f)
        def ids = dao.pageInfo(f)
        println(count)
        println(ids)
        then:
        ids.size() == count
    }

    def "test getAllStreamIds - null"() {
        given:
        dao.batchUpsert([])
        when:
        int count = dao.filteredCount(null)
        def ids = dao.getAllStreamIds()
        println(ids)
        then:
        ids.size() == count
    }
}
