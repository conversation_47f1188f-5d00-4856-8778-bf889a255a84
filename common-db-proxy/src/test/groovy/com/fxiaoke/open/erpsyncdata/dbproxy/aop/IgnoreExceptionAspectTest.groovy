package com.fxiaoke.open.erpsyncdata.dbproxy.aop

import com.fxiaoke.open.erpsyncdata.dbproxy.monitor.CheckProductErpDataMonitor
import org.springframework.aop.aspectj.annotation.AspectJProxyFactory
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2024/6/4
 */
class IgnoreExceptionAspectTest extends Specification {

    CheckProductErpDataMonitor proxy
    def data = []

    def setup() {
        def monitor = Mock(CheckProductErpDataMonitor) {
            destroy() >> {
                throw new Exception("fail test")
            }
            checkInit(_) >> { args ->
                data.add(args[0])
            }
        }
        proxy = getAspect(monitor)
    }

    def "执行成功"() {
        expect:
        proxy.checkInit("0536")
        data.get(0) == "0536"
    }

    def "执行失败"() {
        when:
        proxy.destroy()
        then:
        noExceptionThrown()
    }

    private <T> T getAspect(T o) {
        def aop = new IgnoreExceptionAspect()

        // 使用AspectJProxyFactory来创建被增强的代理对象
        AspectJProxyFactory factory = new AspectJProxyFactory(o);
        factory.setProxyTargetClass(true) // 使用CGLib代理
        factory.addAspect(aop); // 添加切面到代理工厂中
        return factory.getProxy(); // 获取代理对象
    }
}
