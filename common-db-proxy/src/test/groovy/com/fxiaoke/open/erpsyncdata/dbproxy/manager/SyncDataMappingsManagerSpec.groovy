package com.fxiaoke.open.erpsyncdata.dbproxy.manager

import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailSnapshotDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailSnapshotEntity
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import org.apache.commons.lang3.tuple.Pair
import spock.lang.Specification


/**
 * <AUTHOR> 
 * @date 2024/10/24 10:08:11
 */
class SyncDataMappingsManagerSpec extends Specification {
    SyncDataMappingsDao syncDataMappingsDao = Mock()
    AdminSyncPloyDetailSnapshotDao adminSyncPloyDetailSnapshotDao = Mock()
    SyncDataFixDao adminSyncDataDao = Mock()
    IdGenerator idGenerator = Mock()
    I18NStringManager i18NStringManager = Mock()

    SyncDataMappingsManager syncDataMappingsManager = new SyncDataMappingsManager(
            syncDataMappingsDao: syncDataMappingsDao
            ,
            adminSyncPloyDetailSnapshotDao: adminSyncPloyDetailSnapshotDao
            ,
            adminSyncDataDao: adminSyncDataDao
            ,
            idGenerator: idGenerator
            ,
            i18NStringManager: i18NStringManager
    )

    void setup() {
        syncDataMappingsDao.setTenantId(_) >> syncDataMappingsDao
        adminSyncDataDao.setTenantId(*_) >> adminSyncDataDao
        adminSyncPloyDetailSnapshotDao.setTenantId(*_) >> adminSyncPloyDetailSnapshotDao
    }

    def "test exit Mapping And Created"() {
        given:
        syncDataMappingsDao.getBySourceData(*_) >> source
        syncDataMappingsDao.getByDestData(*_) >> dest

        when:
        boolean result = syncDataMappingsManager.exitMappingAndCreated("tenantId", "sourceObjectApiName", "sourceDataId", "destObjectApiName")

        then:
        result == ret

        where:
        source                                       | dest                                        || ret
        new SyncDataMappingsEntity(isCreated: true)  | null                                        || true
        null                                         | new SyncDataMappingsEntity(isCreated: true) || true
        new SyncDataMappingsEntity(isCreated: false) | new SyncDataMappingsEntity(isCreated: true) || false
        null                                         | null                                        || false
    }

    def "test get Mapping2 Way"() {
        given:
        def source = new SyncDataMappingsEntity(isCreated: false)
        def dest = new SyncDataMappingsEntity(isCreated: true)
        syncDataMappingsDao.getBySourceData(*_) >> source
        syncDataMappingsDao.getByDestData(*_) >> dest

        when:
        Pair<SyncDataMappingsEntity, SyncDataMappingsEntity> result = syncDataMappingsManager.getMapping2Way("tenantId", "sourceObjectApiName", "sourceDataId", "destObjectApiName")

        then:
        result == Pair.of(source, dest)
    }

    def "test find Mapping By Sync Data Id"() {
        given:
        syncDataMappingsDao.getBySourceData(*_) >> new SyncDataMappingsEntity()
        adminSyncDataDao.setTenantId(*_) >> new SyncDataFixDaoImpl()
        adminSyncDataDao.getSimple(*_) >> new SyncDataEntity()

        when:
        SyncDataMappingsEntity result = syncDataMappingsManager.findMappingBySyncDataId("tenantId", "syncDataId")

        then:
        result == new SyncDataMappingsEntity()
    }

    def "test list Created Mapping"() {
        given:
        syncDataMappingsDao.listCreatedBySourceDataIds(*_) >> [new SyncDataMappingsEntity(isCreated: false, sourceDataId: "sId1"), new SyncDataMappingsEntity(isCreated: true, sourceDataId: "sId2")]
        syncDataMappingsDao.listCreatedByDestDataIds(*_) >> [new SyncDataMappingsEntity(isCreated: false, destDataId: "dId1"), new SyncDataMappingsEntity(isCreated: true, destDataId: "dId2")]

        when:
        Map<String, Boolean> result = syncDataMappingsManager.listCreatedMapping("tenantId", "sourceObjectApiName", ["sourceDataIds"], "destObjectApiName")

        then:
        result == ["sourceDataIds": false]
    }

    def "test insert If Absent By Dest Api Name"() {
        given:
        syncDataMappingsDao.getBySourceData(*_) >> null
        adminSyncPloyDetailSnapshotDao.listNewestByDestTenantIdAndDestObjectApiName(*_) >> [new SyncPloyDetailSnapshotEntity()]
        idGenerator.get(*_) >> "getResponse"
        i18NStringManager.getByEi(*_) >> "getByEiResponse"

        when:
        syncDataMappingsManager.insertIfAbsentByDestApiName("tenantId", "sourceId", "sourceName", "destObjApiName", "destId", "destName")

        then:
        1 * syncDataMappingsDao.insertIgnore(*_) >> 0
    }

    def "test list By Obj Api Name"() {
        given:
        syncDataMappingsDao.listByObjApiName(*_) >> [new SyncDataMappingsEntity()]

        when:
        List<SyncDataMappingsEntity> result = syncDataMappingsManager.listByObjApiName("tenantId", "erpObjApiName", 0, 0)

        then:
        result == [new SyncDataMappingsEntity()]
    }

    def "test get By Dest Data2"() {
        given:
        syncDataMappingsDao.getByDestData2(*_) >> new SyncDataMappingsEntity()

        when:
        SyncDataMappingsEntity result = syncDataMappingsManager.getByDestData2("tenantId", "destObjectApiName", "destDataId")

        then:
        result == new SyncDataMappingsEntity()
    }
}
