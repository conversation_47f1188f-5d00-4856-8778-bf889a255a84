package com.fxiaoke.open.erpsyncdata.dbproxy.manager

import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldExtendDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil
import com.fxiaoke.open.erpsyncdata.preprocess.arg.InitErpObjectFieldsArg
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
class ErpFieldManagerSpec extends Specification {

    private ErpFieldManager erpFieldManager
    def tenantId = "123456"
    @Shared
    def apiName = "test_api_name"
    def idGenerator = Mock(IdGenerator) {
        get() >> "456789"
    }

    def setup() {
        // 便于跟踪链式调用
        def dao = Mock(ErpObjectFieldDao) {
            findIdField(*_) >> new ErpObjectFieldEntity(id: "test")
            findIdField2(*_) >> new ErpObjectFieldEntity(id: "test")
            queryList(*_) >> {
                ErpObjectFieldEntity entity = new ErpObjectFieldEntity(fieldApiName: apiName, erpObjectApiName: apiName,
                        fieldDefineType: ErpFieldTypeEnum.select_one)
                return [entity]
            }
            findByObjApiNameAndType(*_) >> [new ErpObjectFieldEntity(fieldApiName: apiName)]
        }
        def erpObjectFieldDao = Mock(ErpObjectFieldDao) {
            setTenantId(*_) >> dao
        }
        def erpObjectDao = Mock(ErpObjectDao) {
            queryByRealObjApiName(*_) >> {
                ErpObjectEntity entity = new ErpObjectEntity(erpObjectExtendValue: apiName)
                return [entity]
            }
        }
        def extDao = Mock(ErpFieldExtendDao) {
            queryByObjApiName(*_) >> [new ErpFieldExtendEntity(fieldApiName: apiName)]
            queryIdFieldByObjApiName(*_) >> [new ErpFieldExtendEntity(fieldApiName: apiName)]
            queryList(*_) >> {
                def entity = new ErpFieldExtendEntity(id: "extDao", fieldApiName: "test_field_api_name")
                return [entity]
            }
            getNumFieldByObjApiName(*_) >> new ErpFieldExtendEntity(fieldApiName: apiName)
            findByDefineType(*_) >> [new ErpFieldExtendEntity(fieldApiName: apiName)]
        }
        def erpFieldExtendDao = Mock(ErpFieldExtendDao) {
            queryList(*_) >> {
                def entity = new ErpFieldExtendEntity(fieldApiName: "test_field_api_name")
                return [entity]
            }
            setTenantId(*_) >> extDao
        }
        def erpObjManager = Mock(ErpObjManager)
        erpFieldManager = new ErpFieldManager(erpObjectFieldDao: erpObjectFieldDao, erpObjectDao: erpObjectDao, erpFieldExtendDao: erpFieldExtendDao,
                                idGenerator: idGenerator, erpObjManager: erpObjManager)
    }

    def "test queryAllFieldExtend"() {
        expect:
        erpFieldManager.queryAllFieldExtend(tenantId, "dcId", apiName).size() == 1
    }

    def "test getAllNeedQueryFieldExtend"() {
        given:
        def extDao = Mock(ErpFieldExtendDao) {
            getAllNeedQueryFieldExtend(*_) >> [new ErpFieldExtendEntity(id: "001", fieldApiName: apiName)]
            queryByObjQueryCode(*_) >> [new ErpFieldExtendEntity(id: "002", fieldApiName: apiName)]
        }
        def erpFieldExtendDao = Mock(ErpFieldExtendDao) {
            setTenantId(*_) >> extDao
        }
        def tenantConfigurationManager = Mock(TenantConfigurationManager) {
            findOne(*_) >> {
                Map<String, List<String>> configurationMap = ["ENG_BOM.TreeEntity": ["001", "002"]]
                def conf = JacksonUtil.toJson(configurationMap)
                ErpTenantConfigurationEntity entity = new ErpTenantConfigurationEntity(tenantId: tenantId, configuration: conf)
                return entity
            }
        }
        def manager = new ErpFieldManager(erpFieldExtendDao: erpFieldExtendDao, tenantConfigurationManager: tenantConfigurationManager)
        when:
        def res = manager.getAllNeedQueryFieldExtend(tenantId, "dcId", "ENG_BOM.TreeEntity")
        then:
        res.size() == 2
    }

    def "test findByObjApiNameAndType"() {
        expect:
        null != erpFieldManager.findByObjApiNameAndType("81138","BD_STOCK.BillHead", [ErpFieldTypeEnum.id])
    }

    def "test queryFieldMap"() {
        expect:
        null != erpFieldManager.queryFieldMap(tenantId, "dcId", apiName)
    }

    def "test queryIdField"() {
        expect:
        null != erpFieldManager.queryIdField(tenantId, "dcId", apiName)
    }

    def "test upsertExtend - #name"() {
        given:
        def extDao = Mock(ErpFieldExtendDao) {
            findOne(*_) >> q
        }
        def erpFieldExtendDao = Mock(ErpFieldExtendDao) {
            queryList(*_) >> {
                def entity = new ErpFieldExtendEntity(fieldApiName: "test_field_api_name")
                return [entity]
            }
            setTenantId(*_) >> extDao
        }
        def manager = new ErpFieldManager(erpFieldExtendDao: erpFieldExtendDao, idGenerator: idGenerator)
        def arg = new ErpFieldExtendEntity(tenantId: tenantId, objApiName: apiName, dataCenterId: "dcId")
        expect:
        manager.upsertExtend(arg, false)
        where:
        name        | q
        "查询为空"    | null
        "查询非空"    | new ErpFieldExtendEntity(fieldApiName: apiName, erpFieldType: "e12")
    }

    def "test queryAllField"() {
        expect:
        null != erpFieldManager.queryAllField(tenantId, "dcId", apiName)
    }

    def "test deleteField"() {
        expect:
        erpFieldManager.deleteField(tenantId, "dcId", apiName)
    }

    def "test copyByEi"() {
        given:
        def arg = new InitErpObjectFieldsArg(targetTenantId: tenantId, dataCenterId: "dcId", objApiName: apiName, needRemoveFirst: true)
        expect:
        erpFieldManager.copyByEi(arg, "")
    }

    def "test queryNumFieldExtend"() {
        expect:
        null != erpFieldManager.queryNumFieldExtend(tenantId, "", apiName)
    }

    def "test findIdField"() {
        expect:
        "test".equals(erpFieldManager.findIdField("84801", "BD_MATERIAL.BillHead").getId())
        "test".equals(erpFieldManager.findIdField2("84801", "dcId", "BD_MATERIAL.BillHead").getId())
    }

    def "test findMasterIdField"() {
        expect:
        "test".equals(erpFieldManager.findMasterIdField("84801", "dcId", "BD_MATERIAL.BillHead").getId())
    }

    def "test findIdFieldExtend"() {
        expect:
        null != erpFieldManager.findIdFieldExtend(tenantId, "dcId", apiName)
    }

    def "test checkK3detailField"() {
        expect:
        erpFieldManager.checkK3detailField(tenantId, "dcId", "realApiName")
    }

    def "test findMasterDetailField - #name"() {
        given:
        def dao = Mock(ErpObjectFieldDao) {
            queryMasterDetailField(*_) >> q
        }
        def manager = new ErpFieldManager(erpObjectFieldDao: dao)
        when:
        def res = manager.findMasterDetailField(tenantId, apiName, "")
        then:
        res == result
        where:
        name            | q                                                                             || result
        "DAO返回为空"     | []                                                                            || null
        "DAO返回正确"     | [new ErpObjectFieldEntity(id: "001")]                                         || new ErpObjectFieldEntity(id: "001")
        "DAO返回过多"     | [new ErpObjectFieldEntity(id: "001"), new ErpObjectFieldEntity(id: "002")]    || new ErpObjectFieldEntity(id: "001")
    }

    def "test findByFieldApiName - #name"() {
        given:
        def dao = Mock(ErpObjectFieldDao) {
            queryList(*_) >> q
        }
        def erpObjectFieldDao = Mock(ErpObjectFieldDao) {
            setTenantId(*_) >> dao
        }
        def manager = new ErpFieldManager(erpObjectFieldDao: erpObjectFieldDao)
        when:
        def res = manager.findByFieldApiName("dcId", tenantId, apiName, "")
        then:
        res == result
        where:
        name        | q                                         || result
        "获取成功"    | [new ErpObjectFieldEntity(id: "001")]     || new ErpObjectFieldEntity(id: "001")
        "获取失败"    | []                                        || null
    }

    def "test incrementalInsertErpObjectField"() {
        given:
        ErpObjectFieldEntity entity = new ErpObjectFieldEntity(fieldApiName: "test_field_api_name")
        expect:
        erpFieldManager.incrementalInsertErpObjectField(tenantId, "dcId", apiName, [entity])
    }
}
