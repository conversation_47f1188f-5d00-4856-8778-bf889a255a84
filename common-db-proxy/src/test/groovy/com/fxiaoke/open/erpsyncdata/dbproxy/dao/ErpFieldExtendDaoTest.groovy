package com.fxiaoke.open.erpsyncdata.dbproxy.dao

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldExtendDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil
import com.google.common.collect.Sets
import groovy.util.logging.Slf4j
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/11/4
 */
@Ignore
@Slf4j
class ErpFieldExtendDaoTest extends BaseSpockTest {
    @Autowired
    private ErpFieldExtendDao erpFieldExtendDao
    @Autowired
    private IdGenerator idGenerator

    @Test
    public void insert() {
        ErpFieldExtendEntity erpFieldExtendEntity = new ErpFieldExtendEntity();
        erpFieldExtendEntity.setId(idGenerator.get())
        erpFieldExtendEntity.setTenantId("k30")
        erpFieldExtendEntity.setObjApiName("Account")
        erpFieldExtendEntity.setFieldApiName("name")
        erpFieldExtendEntity.setSaveCode("FName")
        erpFieldExtendEntity.setViewCode("Name")
        Long time = System.currentTimeMillis()
        erpFieldExtendEntity.setCreateTime(time)
        erpFieldExtendEntity.setUpdateTime(time)
        def insert = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("k30")).insert(erpFieldExtendEntity)
        log.info("result:{}",insert)
    }

    @Test
    public void query() {
        def fields = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("81138"))
                .queryByObjApiName("81138","", "SAL_SaleOrder")
        log.info("{}",fields)
    }
    @Test
    public void queryUsed() {
        def fields = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("81138")).getAllNeedQueryFieldExtend("81138",null, "SAL_SaleOrder")
        log.info("{}",fields)
    }
    private final Set<String> K3ChannelIDField=Sets.newHashSet("FNumber","Number","FBillNo");
    @Test
    public void testSql(){

        Set<String> apiNameSet= Sets.newHashSet();
        apiNameSet.removeAll(K3ChannelIDField);
        apiNameSet.add("BD_Customer.BD_CUSTBANK");
        apiNameSet.add("BD_Customer.BD_CUSTCONTACT");
        apiNameSet.add("BD_Customer.BD_CUSTORDERORG");
        Set<String> apiNameSet2= Sets.newHashSet();
        apiNameSet2.add("BD_Customer.BD_CUSTBANK");
        apiNameSet2.add("BD_Customer.BD_CUSTCONTACT");
        apiNameSet.removeAll(apiNameSet2)
        erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("81961"))
                .batchUpdateFieldExtendStatus("81961",null,apiNameSet,"BD_Customer",false);
        println 1

    }
}
