package com.fxiaoke.open.erpsyncdata.dbproxy.manager

import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseDataArg
import com.facishare.uc.api.model.enterprise.result.BatchGetSimpleEnterpriseDataResult
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData
import com.facishare.uc.api.service.EnterpriseEditionService
import com.fxiaoke.crmrestapi.arg.v3.FindV3Arg
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.common.result.Result
import com.fxiaoke.crmrestapi.result.v3.ObjectDataQueryListResult
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3
import spock.lang.Specification

import java.util.stream.Collectors

/**
 * <AUTHOR> 
 * @date 2024/10/24 11:53:55
 */
class TenantInfoManagerSpec extends Specification {
    EnterpriseEditionService enterpriseEditionService = Mock()
    ObjectDataServiceV3 objectDataServiceV3 = Mock()
    TenantConfigurationManager tenantConfigurationManager = Mock()

    TenantInfoManager tenantInfoManager = new TenantInfoManager(enterpriseEditionService: enterpriseEditionService,
            objectDataServiceV3: objectDataServiceV3,
            tenantConfigurationManager: tenantConfigurationManager)

    def setup() {

    }


    def "test get Expire Interval Time By Ei"() {
        given:

        enterpriseEditionService.batchGetSimpleEnterpriseData(*_) >> { args ->
            BatchGetSimpleEnterpriseDataArg arg = (BatchGetSimpleEnterpriseDataArg) args[0]
            def collect = arg.enterpriseIds.stream()
                    .map {
                        def enterpriseAccount = it + ''
                        if (it == 123) enterpriseAccount = it + '_sandbox'
                        return new SimpleEnterpriseData(enterpriseId: it, enterpriseAccount: enterpriseAccount)
                    }
                    .collect(Collectors.toList())
            return new BatchGetSimpleEnterpriseDataResult(collect)
        }

        objectDataServiceV3.queryList(*_) >> { args ->
            FindV3Arg findV3Arg = (FindV3Arg) args[1]
            if (findV3Arg.getSearchQueryInfo().contains('111')) {
                return new Result<ObjectDataQueryListResult>(code: 0, data: new ObjectDataQueryListResult(queryResult: new ObjectDataQueryListResult.QueryResult(dataList: [new ObjectData(field_e5798__c: ['vip1', 'vip2'], UDInt1__c: '111')])))
            }
            return new Result<ObjectDataQueryListResult>()
        }

        tenantConfigurationManager.getTenantExpireTimeInterval(*_) >> ["test": 10000l, 'vip2': 9999l]

        when:
        Long result = tenantInfoManager.getExpireIntervalTimeByEi(tenantId)

        then:
        result == ret

        where:
        id       | tenantId || ret
        '已配置' | 'test'   || 10000l
        '沙盒'   | '123'    || 1000 * 60 * 60 * 24 * 7L
        'vip'    | '111'    || 9999l
        '其他'   | '88521'  || 1000 * 60 * 60 * 24 * 30L
    }

    def "test query Enterprise Level Value"() {
        given:
        objectDataServiceV3.queryList(*_) >> new Result<ObjectDataQueryListResult>(code: 0, data: new ObjectDataQueryListResult(queryResult: new ObjectDataQueryListResult.QueryResult(dataList: [new ObjectData(field_e5798__c: ['vip1', 'vip2'], UDInt1__c: '88521')])))

        when:
        Map<String, List<String>> result = tenantInfoManager.queryEnterpriseLevelValue(["88521", "89772"])

        then:
        result == ['88521': ['vip1', 'vip2']]
    }

    def "test get Enterprise Level Value"() {
        given:
        objectDataServiceV3.queryList(*_) >> new Result<ObjectDataQueryListResult>(code: 0, data: new ObjectDataQueryListResult(queryResult: new ObjectDataQueryListResult.QueryResult(dataList: [new ObjectData(field_e5798__c: ['vip1', 'vip2'])])))

        when:
        List<String> result = tenantInfoManager.getEnterpriseLevelValue("88521")

        then:
        result == ['vip1', 'vip2']
    }

}
