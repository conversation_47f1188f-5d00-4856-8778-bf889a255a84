package com.fxiaoke.open.erpsyncdata.util

import com.fxiaoke.common.PasswordUtil
import org.junit.Ignore
import spock.lang.Specification

/**
 *
 * <AUTHOR> (^_−)☆
 */
@Ignore
class PasswordUtilTest extends Specification {
    def "encode"() {
        def encode = PasswordUtil.encode("local_user_pwd")
        println(encode)
        expect:
        encode == "948081209E40CC0D785E97B18051D846D45B0595FBAB7CCA"
    }
}
