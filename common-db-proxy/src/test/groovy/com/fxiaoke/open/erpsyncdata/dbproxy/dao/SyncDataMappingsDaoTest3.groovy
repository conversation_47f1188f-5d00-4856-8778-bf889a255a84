package com.fxiaoke.open.erpsyncdata.dbproxy.dao

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Ignore
import spock.lang.Specification

/**
 * <AUTHOR> 
 * @date 2023/4/26 20:11:16
 */
@Ignore
// TODO: Ignore Spring
@ContextConfiguration(["classpath:spring-test.xml"])
class SyncDataMappingsDaoTest3 extends Specification {

    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao


    def "PageTenantIdByMaxId"() {
        expect:
        Set<String> set = new HashSet<>()
        Set<String> set2 = new HashSet<>()
        String maxId = null;
        while (true) {
            final List<SyncDataMappingsEntity> syncDataMappingsEntities = syncDataMappingsDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("84801")).pageTenantIdByMaxId("84801", maxId, 1000);
            System.out.println("first entiry:"+syncDataMappingsEntities.get(0));
            if (syncDataMappingsEntities.isEmpty()) {
                break;
            }
            // 插入数据,先不考虑有中间表数据的情况
            syncDataMappingsEntities.forEach({ v ->
                if (!set.add(v.getId())) {
                    println v.getId()
                    throw new RuntimeException("重复数据")
                }

                def destId = v.getSourceObjectApiName() + "#" + v.getDestObjectApiName() + "#" + v.getDestDataId()
                if (!set2.add(destId)) {
                    println destId
                    throw new RuntimeException("重复数据2")
                }
            });
            maxId = syncDataMappingsEntities.get(syncDataMappingsEntities.size() - 1).getId();
            String s = syncDataMappingsEntities.stream().map {it.getId()} .max { a, b -> a.compareTo(b) }.get();
            if (!s.equals(maxId)) {
                throw new RuntimeException("排序错误")
            }
        }
    }

    def "updateDataMapping2Success"() {
        given:

        when:
        int ret = 0;
        String myid = "f4186b5b9a184361b6395ea56f64966c";
        try {
            ret = syncDataMappingsDao.setTenantId("84801").updateDataMapping2Success("84801",
                    null,
                    "CH1022",
                    System.currentTimeMillis(),
                    null);
        }catch (Exception e) {
            System.out.println("get exception, " +  e.getMessage(e));
        }
        then:
            ret >= 0
    }
}
