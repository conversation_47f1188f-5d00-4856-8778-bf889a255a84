package com.fxiaoke.open.erpsyncdata.dbproxy.manager

import com.facishare.paas.pod.mybatis.MyBatisRoutePolicy
import com.github.mybatis.tenant.TenantContext
import spock.lang.Specification

/**
 * <AUTHOR> 
 * @date 2024/10/18 10:56:04
 */
class ConfigRouteManagerSpec extends Specification {

    ConfigRouteManager manager = new ConfigRouteManager(
            tenantConfigurationManager : Mock(TenantConfigurationManager) {
                getConfigRouteTenant() >> ['gray' : ['88521','123','456'] ]
            },
            myBatisRoutePolicy: Mock(MyBatisRoutePolicy){
                get(*_) >> new TenantContext(null, "http://gray-url?test=123456", null, null, null)
            }
    )


    def "GetTenant2Db"() {
        when:
        def db = manager.getTenant2Db()

        then:
        db == ['88521': 'gray-url', '123': 'gray-url', '456': 'gray-url']
    }

    def "GetDb2Tenants"() {
        expect:
        manager.getDb2Tenants() == ['gray-url': ['88521', '123', '456']]
    }
}
