package com.fxiaoke.open.erpsyncdata.dbproxy.dao

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.TemplateDao
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncLog
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum
import org.bson.codecs.pojo.PojoCodecProvider
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/4/1
 */
@Ignore
class TemplateDaoTest extends BaseSpockTest {

    @Autowired
    private TemplateDao templateDao

    @Test
    public void testList() {
        println templateDao.listAllBaseInfo()
    }

    static void main(String[] args) {
        println SyncLogTypeEnum.values()
        def build2 =  PojoCodecProvider.builder()
                .register(SyncLog.class)
                .automatic(true).build()
    }
}
