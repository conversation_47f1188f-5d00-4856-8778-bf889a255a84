package com.fxiaoke.open.erpsyncdata.dbproxy.util.interceptorUtil

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum
import com.fxiaoke.open.erpsyncdata.preprocess.model.K3CloudConnectParam
import com.fxiaoke.open.erpsyncdata.preprocess.model.StandardConnectParam
import com.fxiaoke.open.erpsyncdata.preprocess.model.SystemParams
import spock.lang.Unroll

/**
 *
 * <AUTHOR> (^_−)☆
 */
class SecurityUtilTest extends BaseSpockTest {

    def "k3 password empty"() {
        when:
        def srcEntity = new ErpConnectInfoEntity()
        srcEntity.setChannel(ErpChannelEnum.ERP_K3CLOUD)
        def param = new K3CloudConnectParam()
        param.setBaseUrl("https://testk3.com")
        def srcParam = GsonUtil.toJson(param)
        srcEntity.setConnectParams(srcParam)

        def predictedParam = srcParam

        //加密
        SecurityUtil.encrypt(srcEntity)
        String encryptedParam = srcEntity.getConnectParams()
        //解密
        SecurityUtil.decrypt(srcEntity)
        String decryptedParam = srcEntity.getConnectParams()
        println("$srcParam \n,$encryptedParam \n,$decryptedParam")
        then:
        decryptedParam == srcParam
        predictedParam == encryptedParam
        predictedParam == srcParam
    }

    def "k3 password not empty"() {
        when:
        def srcEntity = new ErpConnectInfoEntity()
        srcEntity.setChannel(ErpChannelEnum.ERP_K3CLOUD)
        def param = new K3CloudConnectParam()
        param.setBaseUrl("https://testk3.com")
        def passWord = "1234qwer"
        param.setPassword(passWord)
        def srcParam = GsonUtil.toJson(param)
        srcEntity.setConnectParams(srcParam)

        param.setPassword(SecurityUtil.getAes().encryptBase64(passWord))
        def predictedParam = GsonUtil.toJson(param)

        //加密
        SecurityUtil.encrypt(srcEntity)
        def encryptedParam = srcEntity.getConnectParams()
        //解密
        SecurityUtil.decrypt(srcEntity)
        def decryptedParam = srcEntity.getConnectParams()
        println("$srcParam \n,$encryptedParam \n,$decryptedParam")
        then:
        decryptedParam == srcParam
        predictedParam == encryptedParam
        predictedParam != srcParam
    }


    def "systemParams empty"() {
        when:
        def srcEntity = new ErpConnectInfoEntity()
        srcEntity.setChannel(ErpChannelEnum.STANDARD_CHANNEL)
        def param = new StandardConnectParam()
        param.setBaseUrl("https://teststd.com")
        def srcParam = GsonUtil.toJson(param)
        srcEntity.setConnectParams(srcParam)

        def predictedParam = GsonUtil.toJson(param)

        //加密
        SecurityUtil.encrypt(srcEntity)
        def encryptedParam = srcEntity.getConnectParams()
        //解密
        SecurityUtil.decrypt(srcEntity)
        def decryptedParam = srcEntity.getConnectParams()
        println("$srcParam \n,$encryptedParam \n,$decryptedParam")
        then:
        decryptedParam == srcParam
        predictedParam == encryptedParam
        predictedParam == srcParam
    }

    def "systemParams not empty"() {
        when:
        def srcEntity = new ErpConnectInfoEntity()
        srcEntity.setChannel(ErpChannelEnum.STANDARD_CHANNEL)
        def param = new StandardConnectParam()
        param.setBaseUrl("https://teststd.com")
        String clientSecret = "3MVG9fe4g9fhX0E5urHzHznawpaCOXFottMaq3_bIA0gRtKgukeQ7lShz6kfxRrTZS0mscWOBDNzBaqJ2saGX"
        param.setSystemParams(SystemParams.of(["client_secret": clientSecret]))
        def srcParam = GsonUtil.toJson(param)
        srcEntity.setConnectParams(srcParam)

        String encryptSecret = SecurityUtil.getAes().encryptBase64(clientSecret)
        param.setSystemParams(SystemParams.of(["client_secret": encryptSecret]))
        def predictedParam = GsonUtil.toJson(param)

        //加密
        SecurityUtil.encrypt(srcEntity)
        def encryptedParam = srcEntity.getConnectParams()
        //解密
        SecurityUtil.decrypt(srcEntity)
        def decryptedParam = srcEntity.getConnectParams()
        println("$srcParam \n,$encryptedParam \n,$decryptedParam")
        then:
        decryptedParam == srcParam
        predictedParam == encryptedParam
        predictedParam != srcParam
    }


    @Unroll
    def "test #channel entity "(ErpChannelEnum channel) {
        expect:
        def srcEntity = new ErpConnectInfoEntity()
        srcEntity.setChannel(channel)
        //加密
        SecurityUtil.encrypt(srcEntity)
        //解密
        SecurityUtil.decrypt(srcEntity)
        where:
        channel << ErpChannelEnum.values()
    }
}
