package com.fxiaoke.open.erpsyncdata.web.controller.setUp;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.admin.arg.*;
import com.fxiaoke.open.erpsyncdata.admin.constant.CreateObjectEnum;
import com.fxiaoke.open.erpsyncdata.admin.manager.CopySettingManager;
import com.fxiaoke.open.erpsyncdata.admin.manager.UrlCheckManager;
import com.fxiaoke.open.erpsyncdata.admin.model.DeleteConnectInfo;
import com.fxiaoke.open.erpsyncdata.admin.model.GetHeaderValueOption;
import com.fxiaoke.open.erpsyncdata.admin.model.GetPushIpWhiteList;
import com.fxiaoke.open.erpsyncdata.admin.model.NPathModel;
import com.fxiaoke.open.erpsyncdata.admin.model.SyncQuota;
import com.fxiaoke.open.erpsyncdata.admin.model.UpdatePushIpWhiteList;
import com.fxiaoke.open.erpsyncdata.admin.remote.UserRoleManager;
import com.fxiaoke.open.erpsyncdata.admin.service.ConnectInfoService;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjPresetService;
import com.fxiaoke.open.erpsyncdata.admin.service.SyncQuotaService;
import com.fxiaoke.open.erpsyncdata.admin.utils.DataCenterCookieUtils;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.AplManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.QueryFuncArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.QueryFunctionResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.admin.FunctionInfo;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.ErpConnectService;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.RelationManageGroupDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ConfigCenterManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantEnvManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.remote.service.UserCenterService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.StringsUtils;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.AddOAConnectorArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BaseArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BaseConnectArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.GetConnectorIntroArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QuickCopySettingArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.RefreshSettingArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorAuthType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorHandlerType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.*;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.ConnectorIntro;
import com.fxiaoke.open.erpsyncdata.preprocess.model.proxyservice.DbProxyServiceInfo;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ConnectInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.DataCenterInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.GetTenantInfo;
import com.fxiaoke.open.erpsyncdata.preprocess.result.InitDcResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncDepartmentOrPersonnelService;
import com.fxiaoke.open.erpsyncdata.web.annontation.ManagedTenantIntercept;
import com.fxiaoke.open.erpsyncdata.web.controller.AsyncSupportController;
import com.fxiaoke.otherrestapi.function.data.FunctionServiceFindData;
import com.fxiaoke.otherrestapi.function.data.HeaderObj;
import com.fxiaoke.otherrestapi.function.result.FunctionServiceFindResult;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.async.DeferredResult;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 19:44 2020/8/17
 * @Desc:
 */
@Slf4j
@Api(tags = "企业连接信息设置相关接口")
@RestController("setUpConnectInfoController")
@RequestMapping("cep/setUp/enterprise")
@ManagedTenantIntercept
public class ConnectInfoController extends AsyncSupportController {
    @Autowired
    private ConnectInfoService connectInfoService;
    @Autowired
    private ErpConnectService erpConnectService;
    @Autowired
    private TenantEnvManager tenantEnvManager;
    @Autowired
    private CopySettingManager copySettingManager;
    @Autowired
    private SyncQuotaService syncQuotaService;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private AplManager aplManager;
    @Autowired
    private ErpObjPresetService erpObjPresetService;
    @Autowired
    private SyncDepartmentOrPersonnelService syncDepartmentOrPersonnelService;
    @Autowired
    private RelationManageGroupDao relationManageGroupDao;
    @Autowired
    private UserCenterService userCenterService;
    @Autowired
    private UrlCheckManager urlCheckManager;
    @Autowired
    private UserRoleManager userRoleManager;
    @Autowired
    private ConfigCenterManager configCenterManager;

    @ApiOperation(value = "切换数据中心")
    @RequestMapping(value = "/switchDataCenter", method = RequestMethod.POST)
    @Deprecated
    public Result<String> switchDataCenter(HttpServletResponse response) {
        String tenantId = getLoginUserTenantId();
        String dcId = getDcId();
        if (dcId == null) {
            Result.newError(ResultCodeEnum.NOT_RECEIVE_DCID);
        }
        //设置cookie
        String cookieValue = DataCenterCookieUtils.buildDataCenterCookie(tenantId, dcId);
        Cookie cookie = new Cookie("syncDC", cookieValue);
        cookie.setMaxAge((int) TimeUnit.DAYS.toSeconds(31));
        cookie.setPath("/erp/");
        response.addCookie(cookie);
        connectInfoService.refreshUpdateTime(tenantId, dcId);
        return Result.newSuccess(dcId);
    }


    @ApiOperation(value = "获取所有数据中心")
    @RequestMapping(value = "/queryDataCenterInfo", method = RequestMethod.POST)
    public Result<List<DataCenterInfoResult>> queryDataCenterInfo(@RequestBody QueryDataCenterInfoArg queryDataCenterInfoArg, @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        Result<List<DataCenterInfoResult>> dataCenterInfo = connectInfoService.queryDataCenterInfo(tenantId, userId, lang,queryDataCenterInfoArg);
        if (!dataCenterInfo.isSuccess()) {
            return Result.copy(dataCenterInfo);
        }
        //不知道有何作用，需要刷新提审批修改。或者规划一个按钮的需求。
//        connectInfoService.refreshUpdateTime(tenantId, dcId);
        return dataCenterInfo;
    }

    @ApiOperation(value = "初始化数据中心")
    @RequestMapping(value = "/initDataCenterInfo", method = RequestMethod.POST)
    public DeferredResult<Result<DataCenterInfoResult>> initDataCenterInfo(@RequestBody InitDataCenterInfoArg dataCenterInfoArg, @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String actionName = i18NStringManager.get(I18NStringEnum.s681, lang, tenantId);
        return asyncExecute(() -> connectInfoService.initErpOrOADataCenterInfo(tenantId, userId, dataCenterInfoArg, lang), 10, false, actionName, lang);
    }

    @ApiOperation(value = "获取可添加的数据中心")
    @RequestMapping(value = "/listAddableDataCenter", method = RequestMethod.POST)
    public Result<List<DataCenterInfoResult>> listAddableDataCenter(@RequestBody ListAddableDataCenterArg arg, @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        return connectInfoService.listAddableDataCenter(tenantId, arg.getConnectorType(), lang);
    }

    @ApiOperation(value = "更新数据中心信息，不允许新增")
    @RequestMapping(value = "/updateDataCenterInfo", method = RequestMethod.POST)
    public Result<DataCenterInfoResult> updateDataCenterInfo(@RequestBody DataCenterInfoResult connectInfo, @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        //不从header取数据中心id,且不允许从这个接口新增
        if (connectInfo.getId() == null) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        return connectInfoService.updateDataCenterInfo(tenantId, userId, connectInfo, lang);
    }


    @ApiOperation(value = "获取企业连接信息")
    @RequestMapping(value = "/getConnectInfoByDataCenterId", method = RequestMethod.POST)
    public Result<ConnectInfoResult> getConnectInfoByDataCenterId(@RequestBody BaseConnectArg baseArg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId = StringUtils.isEmpty(baseArg.getDataCenterId()) ? getDcId() : baseArg.getDataCenterId();
        return connectInfoService.getConnectInfoCheckStatus(tenantId, userId, dataCenterId);
    }

    @ApiOperation(value = "更新企业连接信息")
    @RequestMapping(value = "/updateConnectInfo", method = RequestMethod.POST)
    public DeferredResult<Result<ConnectInfoResult>> updateConnectInfo(@RequestBody ConnectInfoResult connectInfo, @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        BaseConnectParam baseConnectParamQuietly = connectInfo.getBaseConnectParamQuietly();
        if (baseConnectParamQuietly != null) {
            String baseUrlRewriteByConnector = baseConnectParamQuietly.getBaseUrlRewriteByConnector();
            urlCheckManager.checkUrl(baseUrlRewriteByConnector);
        }
        connectInfo.setEnterpriseName(getEnterpriseName());
        //从header取数据中心id
        connectInfo.setId(getDcId());
        String actionName = i18NStringManager.get(I18NStringEnum.s97, lang, tenantId);
        return asyncExecute(() -> connectInfoService.updateConnectInfo(tenantId, userId, connectInfo, lang, false), 10, false, actionName, lang);
    }

    @ApiOperation(value = "sap的代理测试")
    @RequestMapping(value = "/connectSapProxy", method = RequestMethod.POST)
    public DeferredResult<Result<Void>> connectSapProxy(@RequestBody SapConnectParam.SAPProxyUrl sapProxyUrl, @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String actionName = i18NStringManager.get(I18NStringEnum.s5005, lang, tenantId);
        return asyncExecute(() -> erpConnectService.actuatorSAPProxyStatus(sapProxyUrl.getSapProxyUrl()), 10, false, actionName, lang);
    }

    @ApiOperation(value = "sap下发系统版本")
    @RequestMapping(value = "/querySapVersion", method = RequestMethod.POST)
    public Result<List<String>> querySapVersion() {
        ErpTenantConfigurationEntity global = tenantConfigurationManager.findGlobal(TenantConfigurationTypeEnum.SAP_SYSTEM_VERSION.name());
        //SAP S/4 HANA
        //SAP S/4 HANA Cloud
        //SAP ECC
        //mySAP ERP
        //SAP A1
        //SAP B1
        //SAP R/3
        //SAP A1S
        //SAP Business ByDesign
        //mySAP.com
        List<String> systemVersion = Lists.newArrayList("SAP S/4 HANA Cloud", "SAP ECC", "mySAP ERP", "SAP S/4 HANA", "SAP A1", "SAP B1", "SAP R/3", "SAP A1S", "SAP Business ByDesign", "mySAP.com");
        if (ObjectUtils.isEmpty(global)) {
            return Result.newSuccess(systemVersion);
        }
        String configuration = global.getConfiguration();
        List<String> versionValues = Splitter.on(";").splitToList(global.getConfiguration());
        return Result.newSuccess(versionValues);
    }


    @ApiOperation(value = "获取K3Cloud数据中心", httpMethod = "POST")
    @RequestMapping(value = "/getK3CloudDbInfos", method = RequestMethod.POST)
    public DeferredResult<Result<List<DbInfo>>> getK3CloudDbInfos(@RequestBody GetK3CloudDbInfosArg arg, @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        urlCheckManager.checkUrl(arg.getBaseUrl());
        String tenantId = getLoginUserTenantId();
        String actionName = i18NStringManager.get(I18NStringEnum.s98, lang, tenantId);
        return asyncExecute(() -> erpConnectService.getK3CloudDbInfos(getLoginUserTenantId(), getDcId(), arg.getBaseUrl()), 10, false, actionName, lang);
    }

    @ApiOperation(value = "企业是否是灰度企业")
    @RequestMapping(value = "/getEnv", method = RequestMethod.POST)
    public Result<TenantEnvInfo> getEnv() {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        Pair<String, String> tenantWebEnvironment = tenantEnvManager.getTenantWebEnvironment(tenantId);
        TenantEnvInfo tenantEnvInfo = new TenantEnvInfo();
        tenantEnvInfo.setEnv(tenantWebEnvironment.getLeft());
        tenantEnvInfo.setWebApp(tenantWebEnvironment.getRight());
        tenantEnvInfo.setEnvVuentryMd5(ConfigCenter.globalConfig.get("ENV_SEAJS_MAP"));
        return Result.newSuccess(tenantEnvInfo);
    }


    @ApiOperation(value = "获取数据库所有数据中心")
    @RequestMapping(value = "/getAllDCInfo", method = RequestMethod.POST)
    public Result<List<DataCenterInfoResult>> getAllDCInfo(@RequestBody CepArg arg, @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        Result<List<DataCenterInfoResult>> dataCenterInfo = connectInfoService.getAllDCInfo(tenantId, userId, lang);
        return dataCenterInfo;
    }

    /**
     * @deprecated 前端通过 {@link #listAddableDataCenter(ListAddableDataCenterArg, String)} 判断增加连接器
     */
    @ApiOperation(value = "判断是否显示添加连接器")
    @RequestMapping(value = "/enableAddConnect", method = RequestMethod.POST)
    @Deprecated
    public Result<Boolean> enableAddConnect(@RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        // 当企业购买金蝶K3C、SAP、用友EAI、通用连接器 、DB连接器其中一个，都需要显示添加连接器的入口
        Result<List<DataCenterInfoResult>> allDCInfo = connectInfoService.getAllDCInfo(tenantId, userId, lang);
        if (CollectionUtils.isEmpty(allDCInfo.getData())) {
            return Result.newSuccess(false);
        }
        Set<ErpChannelEnum> connectSets = allDCInfo.getData().stream().map(DataCenterInfoResult::getChannel).collect(Collectors.toSet());
        if (connectSets.contains(ErpChannelEnum.ERP_K3CLOUD) || connectSets.contains(ErpChannelEnum.ERP_DB_PROXY) || connectSets.contains(ErpChannelEnum.ERP_U8_EAI) || connectSets.contains(ErpChannelEnum.STANDARD_CHANNEL) || connectSets.contains(ErpChannelEnum.ERP_SAP)) {
            return Result.newSuccess(true);
        }
        return Result.newSuccess(false);
    }

    /**
     * 这个方法，的大多数作用，其实是新建连接器
     *
     * @param quickCopySettingArg
     * @param lang
     * @return
     */
    @ApiOperation(value = "快速配置")
    @RequestMapping(value = "/quickCopySettings", method = RequestMethod.POST)
    public Result<String> quickCopySettings(@RequestBody QuickCopySettingArg quickCopySettingArg, @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        quickCopySettingArg.setTenantId(tenantId);
        if (quickCopySettingArg.getErpChannelEnum() == ErpChannelEnum.CONNECTOR_STANDARD
                || quickCopySettingArg.getErpChannelEnum() == ErpChannelEnum.CONNECTOR_QYWX
                || quickCopySettingArg.getErpChannelEnum() == ErpChannelEnum.CONNECTOR_FEISHU
                || quickCopySettingArg.getErpChannelEnum() == ErpChannelEnum.OA) {
            AddOAConnectorArg addOAConnectorArg = new AddOAConnectorArg();
            addOAConnectorArg.setName(quickCopySettingArg.getDestDataCenterName());
            addOAConnectorArg.setChannel(quickCopySettingArg.getErpChannelEnum());
            addOAConnectorArg.setConnectorKey(quickCopySettingArg.getConnectorKey());
            Result<String> result = connectInfoService.addOAConnector(tenantId, addOAConnectorArg, lang);
            return result;
        } else {
            Result<String> voidResult = copySettingManager.handlerCopySetting(quickCopySettingArg, lang);
            return voidResult;
        }
    }

    @ApiOperation(value = "查看配额")
    @RequestMapping(value = "/getQuota", method = RequestMethod.POST)
    public Result<SyncQuota> getQuota(@RequestBody CepArg arg) {
        Result<SyncQuota> quotaRes = syncQuotaService.getQuota(getLoginUserTenantId(), true, true);
        return quotaRes;
    }

    @ApiOperation(value = "从缓存查询日志配额")
    @RequestMapping(value = "/getLogQuotaFromCache", method = RequestMethod.POST)
    public Result<SyncQuota> getLogQuotaFromCache(@RequestBody CepArg arg) {
        String userId = getLoginUserTenantId();
        if (StringUtils.isEmpty(userId)) {
            return Result.newError(ResultCodeEnum.NO_USER);
        }
        Result<SyncQuota> quotaRes = syncQuotaService.getLogQuotaFromCache(userId);
        return quotaRes;
    }

    @ApiOperation(value = "密文刷库")
    @RequestMapping(value = "/refreshPassWord", method = RequestMethod.POST)
    public Result<Integer> refreshPassWord(@RequestBody RefreshSettingArg arg) {

        Set<String> set = new HashSet<>();
        if (ObjectUtils.isNotEmpty(arg.getEnvType())) {
            set = ConfigCenter.GRAY_TENANTS;
        }
        if (!CollectionUtils.isEmpty(arg.getTenantIds())) {
            set.addAll(arg.getTenantIds());
        }
        log.info("getEnv enterprsie:{}", JSONObject.toJSONString(set));
        for (String tenantValue : set) {
            try {
                List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).listByTenantId(tenantValue);
                for (ErpConnectInfoEntity erpConnectInfoEntity : erpConnectInfoEntities) {
                    erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).updateById(erpConnectInfoEntity);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return Result.newSuccess(set.size());
    }

    @ApiOperation(value = "密文刷库")
    @RequestMapping(value = "/reversePassWord", method = RequestMethod.POST)
    public Result<Integer> reversePassWord(@RequestBody RefreshSettingArg arg) {

        Set<String> set = new HashSet<>();
        if (ObjectUtils.isNotEmpty(arg.getEnvType())) {
            set = ConfigCenter.GRAY_TENANTS;
        }
        if (!CollectionUtils.isEmpty(arg.getTenantIds())) {
            set.addAll(arg.getTenantIds());
        }
        log.info("getEnv enterprsie:{}", JSONObject.toJSONString(set));
        for (String tenantValue : set) {
            try {
                List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).listByTenantId(tenantValue);
                for (ErpConnectInfoEntity erpConnectInfoEntity : erpConnectInfoEntities) {
                    erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).updateById(erpConnectInfoEntity);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return Result.newSuccess(set.size());
    }

    @ApiOperation(value = "获取DB代理服务信息")
    @RequestMapping(value = "/getDbProxyServiceInfo", method = RequestMethod.POST)
    public Result<DbProxyServiceInfo> getDbProxyServiceInfo(@RequestBody GetDbProxyServiceInfoArg getDbProxyServiceInfoArg, @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        DBProxyConnectParam dbProxyConnectParam = new DBProxyConnectParam();
        urlCheckManager.checkUrl(getDbProxyServiceInfoArg.getBaseUrl());
        dbProxyConnectParam.setBaseUrl(getDbProxyServiceInfoArg.getBaseUrl());
        dbProxyConnectParam.setVersion(getDbProxyServiceInfoArg.getVersion());
        Result<DbProxyServiceInfo> dbProxyServiceInfo = erpConnectService.getDbProxyServiceInfo(dbProxyConnectParam, getLoginUserTenantId());
        return dbProxyServiceInfo;
    }

    @ApiOperation(value = "获取连接器header值选项")
    @PostMapping("/getHeaderValueOption")
    public Result<GetHeaderValueOption.Result> getHeaderValueOption(@RequestBody(required = false) GetHeaderValueOption.Arg arg) {
        final String s = i18NStringManager.get(I18NStringEnum.s3798, getLang(), getLoginUserTenantId());
        final List<GetHeaderValueOption.HeaderValue> collect = JSON.parseArray(s, GetHeaderValueOption.HeaderValue.class);
        return Result.newSuccess(new GetHeaderValueOption.Result(collect));
    }

    @ApiOperation(value = "JDY创建函数的接口")
    @RequestMapping(value = "/createJdyFunction", method = RequestMethod.POST)
    public Result<String> createJdyFunction(@RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang, @RequestBody ConnectInfoResult connectInfo) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        //根据预置函数模板创建JDY。默认函数名
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(tenantId), userId);
        //拿到配置信息
        if (ObjectUtils.isEmpty(connectInfo.getConnectParams().getJdy())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        JdyConnectParam jdyConnectParam = connectInfo.getConnectParams().getJdy();
        String jdyAplSuffix = getJdyAplSuffix(tenantId);
        String functionName = new StringBuilder().append(i18NStringManager.getByEi(I18NStringEnum.s3753, tenantId)).append(jdyAplSuffix).toString();
        String body = CreateObjectEnum.getJDYFunc(jdyConnectParam, tenantId, jdyAplSuffix);
        String JDYApiname = new StringBuilder().append(jdyAplSuffix).append("__c").toString();
        FunctionInfo functionInfo = FunctionInfo.builder().functionName(functionName).apiName(JDYApiname).bindingObjectApiName("NONE").nameSpace("erpdss-class").type("class").remark(i18NStringManager.getByEi(I18NStringEnum.s3725, tenantId)).body(body).build();
        Result<FunctionServiceFindResult> function = aplManager.createFunction(headerObj, functionInfo);
        if (!function.isSuccess()) {
            return Result.copy(function);
        }
        return Result.newSuccess(function.getData().getFunction().getApiName());
    }

    @ApiOperation(value = "校验jdy函数是否已存在")
    @RequestMapping(value = "/returnJDYfunc", method = RequestMethod.POST)
    public Result<String> returnJDYfunc(@RequestBody CepArg cepArg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId = getDcId();
        //根据预置函数模板创建JDY。默认函数名
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(tenantId), userId);
        QueryFuncArg functionServiceFindArg = new QueryFuncArg();
        functionServiceFindArg.setFunctionName(CommonConstant.JDY_SYSTEM_FUNC);
        Result<QueryFunctionResult> functionResultResult = aplManager.queryRegularFunction(headerObj, functionServiceFindArg);
        if (functionResultResult.isSuccess() && ObjectUtils.isNotEmpty(functionResultResult.getData().getResult())) {
            QueryFunctionResult.ListFunctionResult result = functionResultResult.getData().getResult();
            //判断是不是已经被其他连接器使用了
            ErpConnectInfoEntity connectInfo = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByIdAndTenantId(tenantId, dataCenterId);
            JdyConnectParam connectParam = JSONObject.parseObject(connectInfo.getConnectParams(), JdyConnectParam.class);
            if(ObjectUtils.isNotEmpty(connectParam)){
               return Result.newSuccess(connectParam.getAplClassApiName());
            }
            List<ErpConnectInfoEntity> listDcByTenantId = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getListDcByTenantId(tenantId, ErpChannelEnum.ERP_JDY);
            List<String> aplClassName = Lists.newArrayList();
            for (ErpConnectInfoEntity erpConnectInfoEntity : listDcByTenantId) {
                if (StringUtils.isNotEmpty(erpConnectInfoEntity.getConnectParams())) {
                     connectParam = JSONObject.parseObject(erpConnectInfoEntity.getConnectParams(), JdyConnectParam.class);
                    if (StringUtils.isNotEmpty(connectParam.getAplClassApiName())) {
                        return Result.newSuccess(connectParam.getAplClassApiName());
                    }
                }
            }
            if (result.getFunction().size() > 0) {
                List<String> collect = result.getFunction().stream().map(FunctionServiceFindData::getApiName).filter(u -> !aplClassName.contains(u)).collect(Collectors.toList());
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(collect)) {
                    return Result.newSuccess(collect.get(0));
                }
            }
        }
        return Result.newSuccess();
    }

    private String getJdyAplSuffix(String tenantId) {
        //判断是不是已经被其他连接器使用了
        List<ErpConnectInfoEntity> listDcByTenantId = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getListDcByTenantId(tenantId, ErpChannelEnum.ERP_JDY);
        if (listDcByTenantId.size() == 1) {
            return CommonConstant.JDY_SYSTEM_FUNC;
        }
        String suffixAPLName = new StringBuilder().append(CommonConstant.JDY_SYSTEM_FUNC).append(listDcByTenantId.size()).toString();
        return suffixAPLName;
    }

    @ApiOperation(value = "JDY刷数据")
    @RequestMapping(value = "/initJdySetting", method = RequestMethod.POST)
    public Result<String> initJdySetting(@RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        copySettingManager.initJdySetting();
        return Result.newSuccess();
    }

    @ApiOperation(value = "JDY刷数据")
    @RequestMapping(value = "/initSyncPloyDetail", method = RequestMethod.GET)
    public Result<String> initSyncPloyDetail(@RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        copySettingManager.initSyncPloyDetail(tenantId);
        return Result.newSuccess();
    }


    @ApiOperation(value = "获取连接器授权方式列表")
    @PostMapping("/getConnectorAuthTypeList")
    public Result<List<SelectOption>> getConnectorAuthTypeList(@RequestBody(required = false) BaseArg arg) {
        Result<List<ConnectorAuthType>> result = erpConnectService.getConnectorAuthTypeList(getLoginUserTenantId(), getDcId());
        if (!result.isSuccess()) {
            return Result.copy(result);
        }
        return Result.newSuccess(ConnectorAuthType.toSelectOptions(result.getData()));
    }

    @ApiOperation(value = "获取连接器介绍")
    @PostMapping("/getConnectorIntro")
    public Result<ConnectorIntro> getConnectorIntro(@RequestBody(required = false) GetConnectorIntroArg arg) {
        return erpConnectService.getConnectorIntro(getLoginUserTenantId(), getDcId(), arg);
    }

    @ApiOperation(value = "授权之后获取连接器系统参数")
    @PostMapping("/getSystemParamsAfterAuthorization")
    public Result<Map<String,Object>> getSystemParamsAfterAuthorization(@RequestBody(required = false) SystemParams systemParams) {
        return erpConnectService.getSystemParamsAfterAuthorization(getLoginUserTenantId(), getDcId(),systemParams);
    }


    @ApiOperation(value = "获取OAuth2授权接口")
    @PostMapping("/getOAuth2AuthUrl")
    public Result<String> getOAuth2AuthUrl(@RequestBody() SystemParams systemParams) {
        return erpConnectService.getOAuth2AuthUrl(getLoginUserTenantId(), getDcId(), systemParams);
    }

    @ApiOperation(value = "上传TCPath图标，返回下载地址")
    @PostMapping("/getIconUrlByTempPath")
    public Result<String> getIconUrlByTempPath(@RequestBody NPathModel nPathModel) {
        Result<String> result = connectInfoService.getIconUrlByTempPath(getLoginUserTenantId(), nPathModel);
        return result;
    }

    @ApiOperation(value = "获取已知的系统信息列表")
    @PostMapping("/listKnownSystemInfos")
    public Result<List<SystemInfo>> listKnownSystemInfos(@RequestBody(required = false) CepArg arg) {
        final String s = i18NStringManager.get(I18NStringEnum.knownSystem, getLang(), getLoginUserTenantId());
        List<SystemInfo> systemInfos = JSON.parseArray(s, SystemInfo.class);

        if (systemInfos == null) {
            systemInfos = ListUtil.empty();
        }
        return new Result<>(systemInfos);
    }

    @ApiOperation(value = "初始化对象和集成流")
    @PostMapping("initDcPresetObjAndStream")
    public Result<InitDcResult> initDcPresetObjAndStream(@RequestBody BaseArg arg) {
        final String dcId = getDcId();
        final String tenantId = getLoginUserTenantId();
        final Result<InitDcResult> result = erpObjPresetService.initDcPresetObjAndStream(tenantId, dcId);
        return result;
    }

    @ApiOperation(value = "获取连接器推送IP白名单")
    @PostMapping("getPushIpWhiteList")
    public Result<GetPushIpWhiteList.Result> getPushIpWhiteList(@RequestBody(required = false) GetPushIpWhiteList.Arg arg) {
        final String tenantId = getLoginUserTenantId();
        ErpTenantConfigurationEntity entity = tenantConfigurationManager.findOne(tenantId, "ALL", "ALL", TenantConfigurationTypeEnum.IP_REQUEST_TENANT_WHITE_LIST.name());

        final List<String> whiteList = Optional.ofNullable(entity).map(ErpTenantConfigurationEntity::getConfiguration).map(config -> Splitter.on(";").splitToList(config)).orElse(new ArrayList<>());

        return Result.newSuccess(new GetPushIpWhiteList.Result(whiteList));
    }

    @ApiOperation(value = "设置连接器推送IP白名单")
    @PostMapping("updatePushIpWhiteList")
    public Result<Void> updatePushIpWhiteList(@RequestBody UpdatePushIpWhiteList.Arg arg) {
        final String dcId = getDcId();
        final String tenantId = getLoginUserTenantId();
        final String configuration = String.join(";", arg.getIps());
        tenantConfigurationManager.updateConfig(tenantId, "ALL", "ALL", TenantConfigurationTypeEnum.IP_REQUEST_TENANT_WHITE_LIST.name(), configuration);

        return Result.newSuccess();
    }

    @ApiOperation(value = "设置账号自动绑定映射规则")
    @PostMapping("saveEmployeeAutoBindRule")
    public Result<Void> saveEmployeeAutoBindRule(@RequestBody SaveEmployeeAutoBindRule.Arg arg) {
        final String dcId = getDcId();
        final String tenantId = getLoginUserTenantId();

        if (SaveEmployeeAutoBindRule.autoBindRuleType.equals(arg.getType())&&StringsUtils.anyBlank(arg.getErpUserIdField(), arg.getErpUserNameFiled())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        GetEmployeeAutoBindRule.Result result= BeanUtil.copy(arg, GetEmployeeAutoBindRule.Result.class);
        tenantConfigurationManager.updateConfig(tenantId, dcId, "ALL", TenantConfigurationTypeEnum.CRM_EMPLOYEE_AUTO_BIND_FIELD_CONFIG.name(), JacksonUtil.toJson(result));
        if(SaveEmployeeAutoBindRule.autoBindRuleType.equals(arg.getType())){
            asyncExecute(() -> syncDepartmentOrPersonnelService.allEmployeeAutoBindFields(tenantId, dcId, arg.getErpUserIdField(), arg.getErpUserNameFiled()));
        }

        return Result.newSuccess();
    }

    @ApiOperation(value = "获取账号自动绑定映射规则")
    @PostMapping("getEmployeeAutoBindRule")
    public Result<GetEmployeeAutoBindRule.Result> getEmployeeAutoBindRule(@RequestBody(required = false) GetEmployeeAutoBindRule.Arg arg) {
        final String dcId = getDcId();
        final String tenantId = getLoginUserTenantId();
        final ErpTenantConfigurationEntity entity = tenantConfigurationManager.findOne(tenantId, dcId, "ALL", TenantConfigurationTypeEnum.CRM_EMPLOYEE_AUTO_BIND_FIELD_CONFIG.name());

        GetEmployeeAutoBindRule.Result result = new GetEmployeeAutoBindRule.Result();
        //默认情况
        result.setIsOpen(true);
        result.setType(SaveEmployeeAutoBindRule.integrationType);
        if (entity != null && StringUtils.isNotBlank(entity.getConfiguration())) {
            if (!entity.getConfiguration().startsWith("{")) {//兼容历史数据
                result = Optional.ofNullable(entity).map(ErpTenantConfigurationEntity::getConfiguration)
                        .map(configuration -> Splitter.on(";").splitToList(configuration)).filter(list -> list.size() == 2)
                        .map(list -> new GetEmployeeAutoBindRule.Result(true, SaveEmployeeAutoBindRule.autoBindRuleType, list.get(0), list.get(1)))
                        .orElseGet(GetEmployeeAutoBindRule.Result::new);
            } else {
                result = JacksonUtil.fromJson(entity.getConfiguration(), GetEmployeeAutoBindRule.Result.class);
            }
        }
        return Result.newSuccess(result);
    }

    @ApiOperation(value = "删除连接器")
    @PostMapping("deleteConnectInfo")
    public Result<Void> deleteConnectInfo(@RequestBody DeleteConnectInfo.Arg arg) {
        return connectInfoService.deleteConnectInfo(getLoginUserTenantId(), getLoginUserId(), arg.getDcId(), getLang());
    }

    @ApiOperation(value = "获取合法的处理类型")
    @PostMapping("getValidHandlerType")
    public Result<List<ConnectorHandlerType>> getValidHandlerType(@RequestBody BaseArg arg) {
        List<ConnectorHandlerType> list = new ArrayList<>();
        list.add(ConnectorHandlerType.REST_API);
        if (tenantConfigurationManager.inWhiteList(getLoginUserTenantId(), TenantConfigurationTypeEnum.DEV_TENANTS)) {
            list.add(ConnectorHandlerType.APL_CLASS);
        }
        return Result.newSuccess(list);
    }

    @ApiOperation(value = "获取企业配置信息")
    @PostMapping(value = "/getTenantInfo")
    public Result<GetTenantInfo.Result> getTenantInfo(@RequestBody(required = false) GetTenantInfo.Arg arg) {
        String tenantId = getLoginUserTenantId();
        final String upStreamIdByTemplateId = relationManageGroupDao.getUpStreamIdByTemplateId(tenantId);
        GetTenantInfo.Result result = new GetTenantInfo.Result();
        if (!StringUtils.isBlank(upStreamIdByTemplateId)) {
            //模板企业
            final String enterpriseName = userCenterService.getEnterpriseName(upStreamIdByTemplateId);
            return Result.newSuccess(result.templateTenant(upStreamIdByTemplateId, enterpriseName));
        }
        if (configCenterManager.readDenyTenantIds().contains(tenantId)) {
            return Result.newSuccess(result.alert(I18NStringEnum.kDenyTeanantAlert.getText()));
        }
        return Result.newSuccess(result);
    }

    /**
     * /updateConnectInfo更新逻辑太重了，新增一个轻量的更新接口
     * 只更新自定义连接器【对接的系统名称】没有什么逻辑
     *
     * @return
     */
    @ApiOperation(value = "更新企业连接信息")
    @RequestMapping(value = "/updateStandardConnectSysName", method = RequestMethod.POST)
    public Result<Void> updateStandardConnectSysName(@RequestBody StandardConnectParam connectParam) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String lang = getLang();
        return connectInfoService.updateStandardConnectSysName(connectParam, tenantId, userId, getDcId(), lang);
    }

    @ApiOperation(value = "检查当前用户是否有权限操作")
    @RequestMapping(value = "/checkPermission", method = RequestMethod.POST)
    public Result<CheckUserPermission.Result> checkPermission(@RequestBody(required = false) CheckUserPermission.Arg arg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        boolean hasErpdssPermission;
        try {
            hasErpdssPermission = userRoleManager.hasFuncPermission(tenantId, userId, ConfigCenter.functionErpdssTabPermissions);
        } catch (Exception e) {
            log.warn("userRoleManager.hasFuncPermission error, tenantId:{} userId:{}", tenantId, userId, e);
            hasErpdssPermission = true;
        }
        return Result.newSuccess(new CheckUserPermission.Result(hasErpdssPermission));
    }
}
