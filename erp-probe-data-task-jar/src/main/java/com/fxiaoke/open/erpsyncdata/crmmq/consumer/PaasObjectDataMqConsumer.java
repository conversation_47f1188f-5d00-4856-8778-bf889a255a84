package com.fxiaoke.open.erpsyncdata.crmmq.consumer;

import com.fxiaoke.open.erpsyncdata.common.mq.AbstractBatchMqConsumer;
import com.fxiaoke.open.erpsyncdata.crmmq.data.ObjectDataMqData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.retry.AsyncReTryIfFailedManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class PaasObjectDataMqConsumer extends AbstractBatchMqConsumer<ObjectDataMqData> {
    @Autowired
    public ConsumerPaasMqManager consumerPaasMqManager;
    @Autowired
    private AsyncReTryIfFailedManager asyncReTryIfFailedManager;


    public PaasObjectDataMqConsumer() {
        super(ObjectDataMqData.class);
    }

    @PostConstruct
    public void init() {
        super.init();
    }

    @Override
    public int batchProcessMessage(List<ObjectDataMqData> messages) throws Throwable {
        return consumerPaasMqManager.batchProcessMessage(messages);
    }

    public static void main(String[] args) {
        for (Method method : PaasObjectDataMqConsumer.class.getMethods()) {
            if(method.getName().equals("batchProcessMessage")){
                for (Class<?> parameterType : method.getParameterTypes()) {
                    log.info("parameterType");
                }
            }
        }
    }

    @Override
    protected void handlerException(List<ObjectDataMqData> messages, Throwable throwable) {
        if(ErrorUtils.judgeException(throwable)){
            log.error("consume paas object data handler exception:{}",throwable);
            List<Object> objectList = messages.stream()
                    .map(data -> (Object) data)
                    .collect(Collectors.toList());
            asyncReTryIfFailedManager.retryOriginMQ(objectList);
            log.info("handler exception data size:{}",objectList.size());
        }
    }

}
